# 环境变量配置说明

## Orama Cloud 搜索配置

为了使搜索功能正常工作，你需要配置以下环境变量：

### 1. 创建环境变量文件

在项目根目录创建 `.env.local` 文件（该文件不会被提交到 Git）：

```bash
# Orama Cloud 配置
NEXT_PUBLIC_ORAMA_API_KEY=UPc31JnTgKtFPL3bUzL18XfZ8zyiiz0G
NEXT_PUBLIC_ORAMA_ENDPOINT=https://cloud.orama.run/v1/indexes/nestjs-docs-leoku-dev-j21flz
```

### 2. 环境变量说明

- `NEXT_PUBLIC_ORAMA_API_KEY`: Orama Cloud 的 API 密钥
- `NEXT_PUBLIC_ORAMA_ENDPOINT`: Orama Cloud 的搜索端点 URL

### 3. 注意事项

- 环境变量名必须以 `NEXT_PUBLIC_` 开头，这样才能在客户端组件中使用
- `.env.local` 文件已被 `.gitignore` 忽略，不会被提交到版本控制
- 如果你需要在生产环境中部署，请在部署平台（如 Vercel）中配置相应的环境变量

### 4. 验证配置

重启开发服务器后，搜索功能应该能正常工作。如果遇到问题，请检查：

1. 环境变量文件是否在正确位置
2. 环境变量名是否正确
3. 是否重启了开发服务器
