# NestJS 中文文档 AI 问答功能

## 功能概述

基于 Orama Cloud 的 Answer Engine 实现的智能文档问答功能，用户可以通过自然语言询问关于 NestJS 的问题，获得基于官方文档的准确答案。

## 核心特性

### ✨ 主要功能

- **智能问答**: 基于 Orama Cloud AI 引擎，提供准确的中文回答
- **文档引用**: 每个答案都提供相关的文档来源链接
- **相关问题**: 自动推荐相关问题，便于深入了解
- **对话历史**: 保持对话上下文，支持连续提问
- **流式响应**: 实时显示回答过程，提升用户体验

### 🎯 用户体验

- **快捷键支持**: `Ctrl+I` 快速打开 AI 问答
- **建议问题**: 提供常见问题模板，快速开始对话
- **响应式设计**: 适配各种设备屏幕
- **加载状态**: 清晰的加载和思考状态提示

## 技术实现

### 架构设计

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   用户界面      │───▶│   Orama Cloud    │───▶│   OpenAI API    │
│  (React 组件)   │    │  Answer Engine   │    │   (GPT Models)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 核心组件

#### 1. `AnswerDialog.tsx` - 主对话界面

- 使用 `@oramacloud/client` 创建 Answer Session
- 处理用户输入和 AI 回复
- 管理对话状态和历史记录
- 展示相关文档和问题推荐

#### 2. `AnswerTrigger.tsx` - 触发器组件

- 集成到侧边栏搜索区域
- 提供快捷键提示
- 一键打开 AI 问答界面

#### 3. `StreamingAnswer.tsx` - 流式回答显示

- 打字机效果展示回答
- 优化用户等待体验
- 支持实时内容更新

#### 4. `AnswerHelp.tsx` - 使用说明

- 功能介绍和使用技巧
- 快捷键说明
- 最佳实践建议

### 配置要求

#### 环境变量

```bash
# Orama Cloud 配置
NEXT_PUBLIC_ORAMA_API_KEY=your-api-key
NEXT_PUBLIC_ORAMA_ENDPOINT=your-endpoint-url
```

#### 依赖包

```json
{
  "@oramacloud/client": "^2.1.4",
  "@oramacloud/react-client": "^2.1.4"
}
```

## 使用方式

### 基本操作

1. **打开问答**: 点击侧边栏"AI 问答助手"或按 `Ctrl+I`
2. **询问问题**: 在输入框中输入问题，按 `Enter` 发送
3. **查看答案**: AI 会基于文档内容提供详细回答
4. **参考文档**: 点击回答下方的文档链接查看原文
5. **继续对话**: 可以基于回答继续深入提问

### 快捷键

- `Ctrl+K`: 打开搜索功能
- `Ctrl+I`: 打开 AI 问答
- `Enter`: 发送问题
- `Escape`: 关闭对话框

### 最佳实践

#### 💡 提问技巧

- **具体明确**: "如何创建控制器？" ✅ vs "NestJS 怎么用？" ❌
- **包含上下文**: "在 NestJS 中如何使用 TypeORM 连接 MySQL？"
- **分步骤问**: 复杂问题可以拆分成多个简单问题

#### 📚 推荐问题类型

- 概念解释: "什么是依赖注入？"
- 实现方法: "如何创建自定义装饰器？"
- 最佳实践: "NestJS 项目结构的最佳实践是什么？"
- 故障排除: "为什么我的模块无法导入？"

## 技术特点

### 🔒 安全性

- 使用 Orama Cloud 代理，API Key 不暴露给客户端
- 基于官方文档内容，避免不准确信息
- 内置质量检查，减少 AI 幻觉问题

### ⚡ 性能优化

- 对话状态缓存，减少重复请求
- 流式响应，提升用户体验
- 懒加载组件，优化首屏加载

### 🌐 国际化支持

- 中文优化的提示词
- 本地化的用户界面
- 适合中文用户的交互设计

## 未来扩展

### 计划功能

- [ ] 代码示例生成
- [ ] 多轮对话优化
- [ ] 自定义知识库
- [ ] 问题分类和标签
- [ ] 用户反馈收集

### 可能的增强

- **多模态支持**: 支持图片和代码片段输入
- **个性化推荐**: 基于用户历史提供个性化建议
- **离线模式**: 缓存常见问答，支持离线使用
- **语音交互**: 支持语音输入和输出

## 故障排除

### 常见问题

#### Q: AI 无法回答我的问题？

A: 确保问题与 NestJS 相关，尝试使用更具体的描述。

#### Q: 回答速度很慢？

A: 检查网络连接，复杂问题需要更多处理时间。

#### Q: 无法打开 AI 问答？

A: 检查环境变量配置，确保 Orama Cloud 服务正常。

#### Q: 文档链接无法访问？

A: 检查文档路径配置，确保链接指向正确的页面。

### 调试信息

查看浏览器控制台获取详细错误信息：

```javascript
// 开启调试模式
localStorage.setItem('debug', 'orama:*')
```

## 贡献指南

如果您想改进 AI 问答功能：

1. **问题反馈**: 在 GitHub Issues 中报告问题
2. **功能建议**: 提交 Feature Request
3. **代码贡献**: 提交 Pull Request
4. **文档改进**: 完善使用说明和示例

---

_基于 Orama Cloud Answer Engine 提供支持 • [了解更多](https://docs.orama.com/cloud/answer-engine/)_
