# MDX 文档格式检查指南

本文档记录了用于查找和修复 MDX 文件中常见格式问题的正则表达式和相关说明。

## 规则一：查找链接前后有多余空格且链接文本不为英文的项

**用途**:
此规则用于查找 Markdown 链接 `[]()`，其链接文本 `[]` 中不包含任何英文字母，并且链接的**前面或后面**紧邻一个或多个空格。这通常是不规范的格式，需要手动修正。

**正则表达式**:

```regex
(?<=\s)\[[^A-zA-Z]+\]\([^)]+\)|\[[^A-zA-Z]+\]\([^)]+\)(?=\s)
```

**VSCode 使用方法**:

1.  按下 `Cmd + Shift + F` (macOS) 或 `Ctrl + Shift + F` (Windows) 打开全局搜索。
2.  在搜索框右侧，点击 `.*` 图标以启用正则表达式模式。
3.  将上述正则表达式粘贴到搜索框中。
4.  在 "files to include" 输入框中，填入 `*.mdx` 以限定搜索范围。

**正则表达式解释**:
这个表达式用 `|` (或) 分为两部分，满足任意一部分即可匹配：

1.  `(?<=\s)\[...\](...)`：匹配所有**前面是空格**的非英文链接。
    - `(?<=\s)` 是一个"正向后行断言"，它会检查链接左边是不是空格，但不会把空格算作匹配结果的一部分，让高亮更聚焦。
2.  `\[...\](...)(?=\s)`：匹配所有**后面是空格**的非英文链接。
    - `(?=\s)` 是一个"正向前行断言"，它检查链接右边是不是空格，同样不把空格算入结果。

这个版本比之前的更精确，能够正确处理 "前面有空格" 或 "后面有空格" 的情况。

**正例**:

- 这里有一个 [文档链接](https://example.com) 后面有空格。
- 前面有空格 [中文链接](https://example.com)是不规范的。

**反例**:

- 这里有一个[文档链接](https://example.com)没有多余空格，符合规范。
- 这里有一个 [Documentation Link](https://example.com) 虽然有空格，但链接文本为英文，不会被匹配。

---

_之前提供的旧规则 `(?<=\s|^)\[[^A-Za-z]{1,}?\]\([^\)]_?\)(?=\s|$)` 要求前后同时满足条件，过于严格，现已废弃。\*

## 规则二：查找英文双引号（""）内包含非英文字符的项

**用途**:
此规则用于查找所有英文双引号 `""` 中包含非 ASCII 字符（如中文、日文等）的实例。这有助于统一术语翻译、检查不应出现非英文字符的引文。

**正则表达式**:

```regex
"[^"]*[^\x00-\x7F][^"]*"
```

**VSCode 使用方法**:

1.  按下 `Cmd + Shift + F` (macOS) 或 `Ctrl + Shift + F` (Windows) 打开全局搜索。
2.  在搜索框右侧，点击 `.*` 图标以启用正则表达式模式。
3.  将上述正则表达式粘贴到搜索框中。
4.  在 "files to include" 输入框中，填入 `*.mdx` 以限定搜索范围。

**正则表达式解释**:

- `"` - 匹配开始的英文双引号。
- `[^"]*` - 匹配任意数量的非双引号字符。
- `[^\x00-\x7F]` - 匹配一个非 ASCII 字符。这是规则的核心，用于识别非英文字符。
- `[^"]*` - 再次匹配任意数量的非双引号字符。
- `"` - 匹配结束的英文双引号。

**更精确的模式**:
如果只想查找包含中文字符的项，可以使用 Unicode 范围：

```regex
"[^"]*[\u4e00-\u9fff][^"]*"
```

- `[\u4e00-\u9fff]`：精确匹配中文字符。

**更简化的模式**:
如果只需要查找英文双引号内包含任何非英文字母、数字、空格和标点的内容：

```regex
"[^"]*[^a-zA-Z\s\d\p{P}][^"]*"
```

- `[^a-zA-Z\s\d\p{P}]`：匹配任何不是英文字母、空格、数字、标点的字符。
  - `a-zA-Z`：英文字母
  - `\s`：空白字符（如空格、Tab）
  - `\d`：数字
  - `\p{P}`：标点符号（VS Code 支持 Unicode 属性类）

这个简化版本更方便查找所有非标准 ASCII 内容的实例，包括但不限于中文、日文、韩文等字符。

**正例**:

- 使用了 "中文双引号内容" 这样的格式。
- 引用了 "包含中文的引用" 会被检测到。
- 术语 "API 网关服务" 中混合了英文和中文。

**反例**:

- 纯英文引用 "This is a quote" 不会被匹配。
- 使用了中文引号「这是中文引号」不会被匹配。
- 没有使用引号的中文内容不会被匹配。

## 规则三：查找星号（\*\*）内包含非英文字符的项

**用途**:
此规则用于查找所有被星号 `**` 包围的非 ASCII 字符（如中文、日文等）的实例。这有助于检查强调格式的一致性，特别是在中英文混合文档中。

**正则表达式**:

```regex
\*\*[^\x00-\x7F]+?\*\*
```

**VSCode 使用方法**:

1.  按下 `Cmd + Shift + F` (macOS) 或 `Ctrl + Shift + F` (Windows) 打开全局搜索。
2.  在搜索框右侧，点击 `.*` 图标以启用正则表达式模式。
3.  将上述正则表达式粘贴到搜索框中。
4.  在 "files to include" 输入框中，填入 `*.mdx` 以限定搜索范围。

**正则表达式解释**:

- `\*\*` - 匹配开始的两个星号（需要转义）。
- `[^\x00-\x7F]+?` - 匹配至少一个非 ASCII 字符（非贪婪模式）。
- `\*\*` - 匹配结束的两个星号。

**带空格版本**:
如果需要匹配前面或后面任一位置有空格的模式，但排除空格前后是英文字符或半角符号的情况：

```regex
(?<=\s(?<![a-zA-Z\x21-\x2F\x3A-\x40\x5B-\x60\x7B-\x7E]\s))\*\*[^*]*[^\x00-\x7F][^*]*\*\*|\*\*[^*]*[^\x00-\x7F][^*]*\*\*(?=\s(?![a-zA-Z\x21-\x2F\x3A-\x40\x5B-\x60\x7B-\x7E]))
```

- `(?<=\s(?<![a-zA-Z\x21-\x2F\x3A-\x40\x5B-\x60\x7B-\x7E]\s))\*\*[^*]*[^\x00-\x7F][^*]*\*\*`：匹配前面有空格且空格前不是英文字符或半角符号的星号强调内容，内容中包含至少一个非ASCII字符。
- `\*\*[^*]*[^\x00-\x7F][^*]*\*\*(?=\s(?![a-zA-Z\x21-\x2F\x3A-\x40\x5B-\x60\x7B-\x7E]))`：匹配后面有空格但空格后不是英文字母或半角符号的星号强调内容，内容中包含至少一个非ASCII字符。
- `[^*]*[^\x00-\x7F][^*]*`：匹配星号内任意内容，但必须包含至少一个非ASCII字符（如中文），允许混合英文。
- `(?<![a-zA-Z\x21-\x2F\x3A-\x40\x5B-\x60\x7B-\x7E]\s)`：负向后行断言，排除空格前是英文字符或半角符号的情况。
- `(?![a-zA-Z\x21-\x2F\x3A-\x40\x5B-\x60\x7B-\x7E])`：负向前行断言，排除空格后跟英文字母或半角符号的情况。
- 使用 `|` 连接两个条件，满足任一条件即可匹配。

**正例**:

- 如果你在 **多包仓库结构** 下开发。
- 请注意 **重要提示** 部分的内容。
- 这是一个 **关键概念** 的解释。

**反例**:

- 纯英文强调 **important note** 不会被匹配。
- 没有使用星号的中文内容不会被匹配。
- 使用其他格式如 _单星号_ 或 `代码块` 的中文内容不会被匹配。

## 规则四：查找 CalloutInfo 标签后面紧跟的内容

**用途**:
此规则用于查找 MDX 文档中 `<CalloutInfo>` 标签后面紧跟的字符内容。这有助于检查 CalloutInfo 组件的使用情况，确保内容格式的一致性。

**正则表达式**:

基础版本（匹配标签后的所有内容）：

```regex
<CalloutInfo>(.*)
```

匹配标签后的非空白字符内容：

```regex
<CalloutInfo>\s*([^\s<]+)
```

匹配标签后整行内容：

```regex
<CalloutInfo>([^\n]*)
```

匹配标签后到下一个标签或换行的内容：

```regex
<CalloutInfo>([^<\n]*)
```

**VSCode 使用方法**:

1.  按下 `Cmd + Shift + F` (macOS) 或 `Ctrl + Shift + F` (Windows) 打开全局搜索。
2.  在搜索框右侧，点击 `.*` 图标以启用正则表达式模式。
3.  将上述正则表达式粘贴到搜索框中。
4.  在 "files to include" 输入框中，填入 `*.mdx` 以限定搜索范围。

**正则表达式解释**:

- `<CalloutInfo>` - 匹配 CalloutInfo 开始标签。
- `(.*)` - 匹配标签后的任意字符（贪婪模式）。
- `\s*` - 匹配任意数量的空白字符。
- `([^\s<]+)` - 匹配非空白且不是 `<` 的字符（即实际内容）。
- `([^\n]*)` - 匹配到行尾的所有字符。
- `([^<\n]*)` - 匹配到下一个标签或换行的所有字符。

**查找并替换用法**:
如果需要提取或修改 CalloutInfo 后的内容，可以使用查找并替换功能：

1. 在替换框中使用 `$1` 来引用括号内匹配的内容。
2. 例如，要在内容前添加空格，可以替换为：`<CalloutInfo> $1`

**正例**:

- `<CalloutInfo>这是一个重要提示</CalloutInfo>`
- `<CalloutInfo> 注意事项说明`
- `<CalloutInfo>Important note about the feature`

**反例**:

- `<CalloutWarning>这是警告信息</CalloutWarning>` - 不同的组件类型
- `<!-- CalloutInfo 注释 -->` - HTML 注释，不是标签
- `CalloutInfo 纯文本` - 没有标签包围

## 规则五：查找 Markdown 链接中包含英文字符 hash 的链接

**用途**:
此规则用于查找 MDX 文档中 Markdown 链接语法 `[文本](链接#hash)` 里包含英文字符 hash 的链接。这有助于检查文档中的锚点链接，确保 hash 部分使用了英文字符，便于维护和国际化。

**正则表达式**:

推荐版本（匹配所有带英文 hash 的 Markdown 链接）：

```regex
\[[^\]]*\]\([^#)]*#[a-zA-Z][a-zA-Z0-9-_]*\)
```

匹配内部链接（以 / 开头）：

```regex
\[[^\]]*\]\(/[^#)]*#[a-zA-Z][a-zA-Z0-9-_]*\)
```

匹配外部链接（包含 http/https）：

```regex
\[[^\]]*\]\(https?://[^#)]*#[a-zA-Z][a-zA-Z0-9-_]*\)
```

匹配所有带英文 hash 的链接（包含更多字符）：

```regex
\[[^\]]*\]\([^)]*#[a-zA-Z][a-zA-Z0-9-_]*[^)]*\)
```

**VSCode 使用方法**:

1. 按下 `Cmd + Shift + F` (macOS) 或 `Ctrl + Shift + F` (Windows) 打开全局搜索。
2. 在搜索框右侧，点击 `.*` 图标以启用正则表达式模式。
3. 将上述正则表达式粘贴到搜索框中。
4. 在 "files to include" 输入框中，填入 `*.mdx` 以限定搜索范围。

**正则表达式解释**:

以推荐版本为例：`\[[^\]]*\]\([^#)]*#[a-zA-Z][a-zA-Z0-9-_]*\)`

- `\[` - 匹配左方括号 `[`
- `[^\]]*` - 匹配任意非右方括号的字符（链接文本）
- `\]` - 匹配右方括号 `]`
- `\(` - 匹配左圆括号 `(`
- `[^#)]*` - 匹配任意非 `#` 和非 `)` 的字符（URL 部分）
- `#` - 匹配 hash 符号
- `[a-zA-Z]` - hash 的第一个字符必须是英文字母
- `[a-zA-Z0-9-_]*` - 后续字符可以是字母、数字、连字符或下划线
- `\)` - 匹配右圆括号 `)`

**正例**:

- `[详细内容](https://example.com#section-name)`
- `[参考文档](/docs/guide#getting-started)`
- `[API 说明](./api.md#method-list)`
- `[更多信息](https://graphql.org/learn/schema#interfaces)`
- `[配置选项](/config#advanced-settings)`

**反例**:

- `[纯中文hash](./link#中文锚点)` - hash 不是英文字符开头
- `[数字开头](./link#123section)` - hash 以数字开头
- `[无hash链接](./simple-link)` - 没有 hash 部分
- `[空hash](./link#)` - hash 为空
- `普通文本 #english-hash` - 不是 Markdown 链接格式

## 规则六：查找并替换中文双引号为中文书名号

**用途**:
此规则用于查找所有被中文双引号 `“”` 包裹的内容，并将其替换为中文书名号 `「」`。这有助于统一中文文档的引用格式，提高文档的规范性和可读性。

**查找正则表达式**:

```regex
“([^”]+)”
```

**替换表达式**:

```regex
「$1」
```

**VSCode 使用方法**:

1. 按下 `Cmd + Shift + H` (macOS) 或 `Ctrl + Shift + H` (Windows) 打开全局查找并替换。
2. 在搜索框右侧，点击 `.*` 图标以启用正则表达式模式。
3. 在查找框中输入：`"([^"]+)"`
4. 在替换框中输入：`「$1」`
5. 在「files to include」输入框中，填入 `*.mdx` 以限定搜索范围。
6. 点击「全部替换」按钮进行批量替换。

**正则表达式解释**:

查找表达式 `"([^"]+)"` 的含义：

- `"` - 匹配开始的中文左双引号
- `([^"]+)` - 捕获组，匹配一个或多个非中文右双引号的字符（被引用的内容）
- `"` - 匹配结束的中文右双引号

替换表达式 `「$1」` 的含义：

- `「` - 中文左书名号
- `$1` - 引用第一个捕获组的内容（即原双引号内的文本）
- `」` - 中文右书名号

**注意事项**:

1. **预览替换**：建议在执行全部替换前，先使用「查找下一个」预览几个匹配项，确保替换结果符合预期。
2. **备份文件**：执行批量替换前，建议先备份相关文件或使用版本控制系统。
3. **跨行内容**：如果引用内容跨多行，可能需要调整正则表达式为 `"([\s\S]+?)"`，使用非贪婪匹配。
4. **嵌套引号**：此规则不处理嵌套引号的情况，如有需要请手动处理。

**正例**:

替换前：

- 请参考"用户指南"中的说明
- 这是一个"重要概念"的解释
- 文档中提到了"最佳实践"

替换后：

- 请参考「用户指南」中的说明
- 这是一个「重要概念」的解释
- 文档中提到了「最佳实践」

**反例**:

- 英文双引号 "This is English" 不会被匹配
- 已经使用书名号的「现有内容」不会被匹配
- 没有使用引号的普通文本不会被匹配
- 单独的引号 " 或 " 不会被匹配
