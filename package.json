{"name": "nest-js-docs-cn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 8080", "build": "next build", "start": "next start -p 8080", "deps": "pnpm up --interactive --latest", "lint": "run-p lint:ts lint:es lint:css", "lint:css": "stylelint \"src/**/*.css\"", "lint:es": "eslint \"**/*.{ts,tsx}\"", "update-deps": "pnpm up --interactive --latest", "lint:ts": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "format:docs": "prettier --write \"src/content/docs/**/*.mdx\""}, "dependencies": {"@mdx-js/loader": "^3.1.0", "@mdx-js/mdx": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.3.4", "@oramacloud/client": "^2.1.4", "@oramacloud/react-client": "^2.1.4", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tooltip": "^1.2.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lodash-es": "^4.17.21", "lucide-react": "^0.522.0", "mermaid": "^11.7.0", "next": "15.3.4", "next-themes": "^0.4.6", "prefer-code-style": "^3.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-use-event-hook": "^0.9.6", "tailwind-merge": "^3.3.1", "ufo": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/swagger": "^11.2.0", "@shikijs/transformers": "^3.7.0", "@shikijs/twoslash": "^3.7.0", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "@types/lodash-es": "^4.17.12", "@types/mdx": "^2.0.13", "@types/node": "^20", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "npm-run-all": "^4.1.5", "prettier": "^3.6.0", "rehype-mdx-code-props": "^3.0.1", "rehype-slug": "^6.0.0", "rehype-slug-custom-id": "^2.0.0", "remark-gfm": "^4.0.1", "shiki": "^3.7.0", "stylelint": "^16.21.0", "stylelint-config-clean-order": "^7.0.0", "stylelint-config-standard": "^38.0.0", "stylelint-config-tailwindcss": "^1.0.0", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3"}, "packageManager": "pnpm@9.15.3", "engines": {"node": ">=20"}}