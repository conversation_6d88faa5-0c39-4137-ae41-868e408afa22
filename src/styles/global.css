@import 'tailwindcss';
@import 'tw-animate-css';

@import './shiki.css';
@import './doc.css';

@plugin '@tailwindcss/typography';

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);

  /* - */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* - */
  --font-sans: var(--font-harmony-os-sans-sc);
  --font-mono: var(--font-maple-mono);

  /* - */
  --spacing-panel: calc(var(--spacing) * 4);
  --spacing-panel-sm: calc(var(--spacing) * 2);
  --spacing-code-block: calc(var(--spacing) * 1.5);
}

:root {
  --radius: 0.625rem;
  --background: oklch(100% 0 0deg);
  --foreground: oklch(14.5% 0 0deg);
  --card: oklch(100% 0 0deg);
  --card-foreground: oklch(14.5% 0 0deg);
  --popover: oklch(100% 0 0deg);
  --popover-foreground: oklch(14.5% 0 0deg);
  --primary: oklch(20.5% 0 0deg);
  --primary-foreground: oklch(98.5% 0 0deg);
  --secondary: oklch(97% 0 0deg);
  --secondary-foreground: oklch(20.5% 0 0deg);
  --muted: oklch(97% 0 0deg);
  --muted-foreground: oklch(55.6% 0 0deg);
  --accent: oklch(58.6% 0.253 17.585deg);
  --accent-foreground: oklch(20.5% 0 0deg);
  --destructive: oklch(57.7% 0.245 27.325deg);
  --border: oklch(92.2% 0 0deg);
  --input: oklch(92.2% 0 0deg);
  --ring: oklch(70.8% 0 0deg);
  --chart-1: oklch(64.6% 0.222 41.116deg);
  --chart-2: oklch(60% 0.118 184.704deg);
  --chart-3: oklch(39.8% 0.07 227.392deg);
  --chart-4: oklch(82.8% 0.189 84.429deg);
  --chart-5: oklch(76.9% 0.188 70.08deg);
  --sidebar: oklch(98.5% 0 0deg);
  --sidebar-foreground: oklch(14.5% 0 0deg);
  --sidebar-primary: oklch(20.5% 0 0deg);
  --sidebar-primary-foreground: oklch(98.5% 0 0deg);

  /* -- */
  --sidebar-accent: var(--background);
  --sidebar-accent-foreground: oklch(20.5% 0 0deg);
  --sidebar-border: oklch(92.2% 0 0deg);
  --sidebar-ring: oklch(70.8% 0 0deg);

  /* 文章图片的滤镜 */
  --images-filter: unset;

  &.dark {
    --background: oklch(14.5% 0 0deg);
    --foreground: oklch(98.5% 0 0deg);
    --card: oklch(20.5% 0 0deg);
    --card-foreground: oklch(98.5% 0 0deg);
    --popover: oklch(20.5% 0 0deg);
    --popover-foreground: oklch(98.5% 0 0deg);
    --primary: oklch(92.2% 0 0deg);
    --primary-foreground: oklch(20.5% 0 0deg);
    --secondary: oklch(26.9% 0 0deg);
    --secondary-foreground: oklch(98.5% 0 0deg);
    --muted: oklch(26.9% 0 0deg);
    --muted-foreground: oklch(70.8% 0 0deg);
    --accent: oklch(51.4% 0.222 16.935deg);
    --accent-foreground: oklch(98.5% 0 0deg);
    --destructive: oklch(70.4% 0.191 22.216deg);
    --border: oklch(100% 0 0deg / 10%);
    --input: oklch(100% 0 0deg / 15%);
    --ring: oklch(55.6% 0 0deg);
    --chart-1: oklch(48.8% 0.243 264.376deg);
    --chart-2: oklch(69.6% 0.17 162.48deg);
    --chart-3: oklch(76.9% 0.188 70.08deg);
    --chart-4: oklch(62.7% 0.265 303.9deg);
    --chart-5: oklch(64.5% 0.246 16.439deg);
    --sidebar: oklch(20.5% 0 0deg);
    --sidebar-foreground: oklch(98.5% 0 0deg);
    --sidebar-primary: oklch(48.8% 0.243 264.376deg);
    --sidebar-primary-foreground: oklch(98.5% 0 0deg);
    --sidebar-accent: oklch(26.9% 0 0deg);
    --sidebar-accent-foreground: oklch(98.5% 0 0deg);
    --sidebar-border: oklch(100% 0 0deg / 10%);
    --sidebar-ring: oklch(55.6% 0 0deg);

    /* 文章图片的滤镜 */
    --images-filter: invert(1) contrast(0.85);
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50 scroll-smooth;

    /* 现代滚动条样式，让滚动条更纤细、更美观 */
    scrollbar-width: thin;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* 锚点跳转时为标题添加上边距，避免标题紧贴窗口上边 */
  article :where(h1, h2, h3, h4, h5, h6) {
    scroll-margin-top: 2em;
  }
}

.text-shimmer {
  font-weight: 500;
  background: linear-gradient(
    to right,
    var(--color-from),
    var(--color-via) 35%,
    var(--color-via) 65%,
    var(--color-to) 100%
  );
  background-clip: text;
  background-size: 200% auto;
  animation: animated-text-gradient 1s linear infinite;
  -webkit-text-fill-color: transparent;
}

@keyframes animated-text-gradient {
  from {
    background-position: 200% center;
  }
}
