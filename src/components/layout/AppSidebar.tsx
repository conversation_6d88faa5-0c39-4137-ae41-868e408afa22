import Image from 'next/image'
import Link from 'next/link'

import { CollapsibleNavItem } from '~/components/CollapsibleNavItem'
import { ScrollGradientContainer } from '~/components/ScrollGradientContainer'
import { SearchForm } from '~/components/SearchForm'
import { ThemeModeToggle } from '~/components/ThemeModeToggle'
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '~/components/ui/sidebar'
import { RoutePath, SITE_CONFIG } from '~/constants'
import { navMainData } from '~/lib/data/nav'

export function AppSidebar(props: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar {...props}>
      <SidebarHeader className="gap-4">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild size="lg">
              <Link href={RoutePath.Home}>
                <div className="flex items-center gap-2">
                  <div className="flex justify-center items-center">
                    <Image
                      alt="NestJS Logo"
                      height={32}
                      src={SITE_CONFIG.logoPath}
                      width={32}
                    />
                  </div>

                  <div className="flex flex-col gap-0.5">
                    <div className="font-semibold">NestJS 中文文档</div>
                    <div className="text-xs text-muted-foreground font-medium">v10.0.0</div>
                  </div>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>

        <SearchForm />
      </SidebarHeader>

      <ScrollGradientContainer gradientFromColor="from-sidebar" gradientHeight="h-12">
        <SidebarContent>
          <SidebarGroup>
            <SidebarMenu>
              {navMainData.map((item, idx) => (
                <CollapsibleNavItem
                  key={`${item.title ?? ''}-${idx}`}
                  item={item}
                />
              ))}
            </SidebarMenu>
          </SidebarGroup>
        </SidebarContent>
      </ScrollGradientContainer>

      <div className="flex items-center gap-2 p-4 border-t border-border">
        <ThemeModeToggle />
      </div>
    </Sidebar>
  )
}
