import type { SVGProps } from 'react'

export function MaterialIconThemeTypescript(props: SVGProps<SVGSVGElement>) {
  return (
    <svg height={16} viewBox="0 0 16 16" width={16} xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M2 2v12h12V2zm4 6h3v1H8v4H7V9H6zm5 0h2v1h-2v1h1a1.003 1.003 0 0 1 1 1v1a1.003 1.003 0 0 1-1 1h-2v-1h2v-1h-1a1.003 1.003 0 0 1-1-1V9a1.003 1.003 0 0 1 1-1" fill="#0288d1" />
    </svg>
  )
}

export function MaterialIconThemeJavascript(props: SVGProps<SVGSVGElement>) {
  return (
    <svg height={16} viewBox="0 0 16 16" width={16} xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M2 2v12h12V2zm6 6h1v4a1.003 1.003 0 0 1-1 1H7a1.003 1.003 0 0 1-1-1v-1h1v1h1zm3 0h2v1h-2v1h1a1.003 1.003 0 0 1 1 1v1a1.003 1.003 0 0 1-1 1h-2v-1h2v-1h-1a1.003 1.003 0 0 1-1-1V9a1.003 1.003 0 0 1 1-1" fill="#ffca28" />
    </svg>
  )
}

export function MaterialIconThemeHtml(props: SVGProps<SVGSVGElement>) {
  return (
    <svg height={32} viewBox="0 0 32 32" width={32} xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="m4 4l2 22l10 2l10-2l2-22Zm19.72 7H11.28l.29 3h11.86l-.802 9.335L15.99 25l-6.635-1.646L8.93 19h3.02l.19 2l3.86.77l3.84-.77l.29-4H8.84L8 8h16Z" fill="#e65100" />
    </svg>
  )
}

export function MaterialIconThemeJson(props: SVGProps<SVGSVGElement>) {
  return (
    <svg height={960} viewBox="0 -960 960 960" width={960} xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M560-160v-80h120q17 0 28.5-11.5T720-280v-80q0-38 22-69t58-44v-14q-36-13-58-44t-22-69v-80q0-17-11.5-28.5T680-720H560v-80h120q50 0 85 35t35 85v80q0 17 11.5 28.5T840-560h40v160h-40q-17 0-28.5 11.5T800-360v80q0 50-35 85t-85 35zm-280 0q-50 0-85-35t-35-85v-80q0-17-11.5-28.5T120-400H80v-160h40q17 0 28.5-11.5T160-600v-80q0-50 35-85t85-35h120v80H280q-17 0-28.5 11.5T240-680v80q0 38-22 69t-58 44v14q36 13 58 44t22 69v80q0 17 11.5 28.5T280-240h120v80z" fill="#f9a825" />
    </svg>
  )
}

export function MaterialIconThemeYaml(props: SVGProps<SVGSVGElement>) {
  return (
    <svg height={24} viewBox="0 0 24 24" width={24} xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M13 9h5.5L13 3.5zM6 2h8l6 6v12c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2m12 16v-2H9v2zm-4-4v-2H6v2z" fill="#ff5252" />
    </svg>
  )
}

export function MaterialIconThemePowershell(props: SVGProps<SVGSVGElement>) {
  return (
    <svg height={32} viewBox="0 0 32 32" width={32} xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M29.07 6H7.677A1.535 1.535 0 0 0 6.24 7.113l-4.2 17.774A.852.852 0 0 0 2.93 26h21.393a1.535 1.535 0 0 0 1.436-1.113L29.96 7.112A.852.852 0 0 0 29.07 6M8.626 23.797a1.4 1.4 0 0 1-1.814-.31l-.007-.009a1.075 1.075 0 0 1 .315-1.599l9.6-6.061l-6.102-5.852l-.01-.01a1.068 1.068 0 0 1 .084-1.625l.037-.03a1.38 1.38 0 0 1 1.8.07l7.233 6.957a1.1 1.1 0 0 1 .236.739a1.08 1.08 0 0 1-.412.79c-.074.04-.146.119-10.951 6.935ZM24 22.94A1.135 1.135 0 0 1 22.803 24h-5.634a1.061 1.061 0 1 1 .001-2.112h5.633A1.134 1.134 0 0 1 24 22.938Z" fill="#03a9f4" />
    </svg>
  )
}

export function MaterialIconThemeGroovy(props: SVGProps<SVGSVGElement>) {
  return (
    <svg height={32} viewBox="0 0 32 32" width={32} xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M19.322 2a6.5 6.5 0 0 1 4.352 1.419a4.55 4.55 0 0 1 1.685 3.662a5.82 5.82 0 0 1-1.886 4.275a6.04 6.04 0 0 1-4.34 1.846a4.15 4.15 0 0 1-2.385-.649a1.91 1.91 0 0 1-.936-1.603a1.6 1.6 0 0 1 .356-1.024a1.1 1.1 0 0 1 .861-.447q.469 0 .468.504a.79.79 0 0 0 .358.693a1.43 1.43 0 0 0 .826.245a3.1 3.1 0 0 0 2.39-1.573a5.66 5.66 0 0 0 1.154-3.39a2.64 2.64 0 0 0-.891-2.064a3.28 3.28 0 0 0-2.293-.812a6.18 6.18 0 0 0-4.086 1.736a12.9 12.9 0 0 0-3.215 4.557a13.4 13.4 0 0 0-1.233 5.36a5.86 5.86 0 0 0 1.091 3.723a3.53 3.53 0 0 0 2.905 1.372q3.058 0 5.848-4.002l2.935-.388q.546-.07.545.246a8 8 0 0 1-.423 1.24q-.421 1.097-1.152 3.668A12.7 12.7 0 0 0 26 17.72v1.66a14.2 14.2 0 0 1-4.055 2.57a10.38 10.38 0 0 1-2.764 5.931a6.7 6.7 0 0 1-4.806 2.11a3.3 3.3 0 0 1-2.012-.55a1.8 1.8 0 0 1-.718-1.514q0-2.685 5.634-5.212q.532-1.766 1.152-3.507a8.6 8.6 0 0 1-2.853 2.323a7.4 7.4 0 0 1-3.48 1.01a5.46 5.46 0 0 1-4.366-2.093A8.1 8.1 0 0 1 6 15.122a11.6 11.6 0 0 1 1.966-6.426a14.7 14.7 0 0 1 5.162-4.862A12.44 12.44 0 0 1 19.322 2m-2.407 22.17q-4.055 1.875-4.054 3.695a.87.87 0 0 0 .999.97q1.964 0 3.055-4.665" fill="#26c6da" />
    </svg>
  )
}

export function MaterialIconThemeGraphql(props: SVGProps<SVGSVGElement>) {
  return (
    <svg height={32} viewBox="0 0 32 32" width={32} xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M6 20h20v2H6z" fill="#ec407a" />
      <circle cx={7} cy={21} fill="#ec407a" r={3} />
      <circle cx={16} cy={27} fill="#ec407a" r={3} />
      <circle cx={25} cy={21} fill="#ec407a" r={3} />
      <path d="M6 10h20v2H6z" fill="#ec407a" />
      <circle cx={7} cy={11} fill="#ec407a" r={3} />
      <circle cx={16} cy={5} fill="#ec407a" r={3} />
      <circle cx={25} cy={11} fill="#ec407a" r={3} />
      <path d="M6 12h2v10H6zm18-2h2v12h-2z" fill="#ec407a" />
      <path d="m5.014 19.41l11.674 6.866L15.674 28L4 21.134z" fill="#ec407a" />
      <path d="M26.688 21.724L15.014 28.59L14 26.866L25.674 20zM5.124 10.382l11.415-7.29l1.077 1.686L6.2 12.068z" fill="#ec407a" />
      <path d="m25.798 12.067l-11.415-7.29l1.077-1.685l11.415 7.29zM6.2 19.932l11.416 7.29l-1.077 1.686l-11.415-7.29z" fill="#ec407a" />
      <path d="m26.875 21.619l-11.415 7.29l-1.077-1.687l11.415-7.289zM5.877 22.6L16.04 3.686l1.762.946L7.638 23.546z" fill="#ec407a" />
      <path d="M24.361 23.545L14.197 4.633l1.761-.947l10.165 18.913z" fill="#ec407a" />
    </svg>
  )
}

export function MaterialIconThemeDocker(props: SVGProps<SVGSVGElement>) {
  return (
    <svg height={24} viewBox="0 0 24 24" width={24} xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M21.81 10.25c-.06-.04-.56-.43-1.64-.43c-.28 0-.56.03-.84.08c-.21-1.4-1.38-2.11-1.43-2.14l-.29-.17l-.18.27c-.24.36-.43.77-.51 1.19c-.2.8-.08 1.56.33 2.21c-.49.28-1.29.35-1.46.35H2.62c-.34 0-.62.28-.62.63c0 1.15.18 2.3.58 3.38c.45 1.19 1.13 2.07 2 2.61c.98.6 2.59.94 4.42.94c.79 0 1.61-.07 2.42-.22c1.12-.2 2.2-.59 3.19-1.16A8.3 8.3 0 0 0 16.78 16c1.05-1.17 1.67-2.5 2.12-3.65h.19c1.14 0 1.85-.46 2.24-.85c.26-.24.45-.53.59-.87l.08-.24zm-17.96.99h1.76c.08 0 .16-.07.16-.16V9.5c0-.08-.07-.16-.16-.16H3.85c-.09 0-.16.07-.16.16v1.58c.01.09.07.16.16.16m2.43 0h1.76c.08 0 .16-.07.16-.16V9.5c0-.08-.07-.16-.16-.16H6.28c-.09 0-.16.07-.16.16v1.58c.01.09.07.16.16.16m2.47 0h1.75c.1 0 .17-.07.17-.16V9.5c0-.08-.06-.16-.17-.16H8.75c-.08 0-.15.07-.15.16v1.58c0 .09.06.16.15.16m2.44 0h1.77c.08 0 .15-.07.15-.16V9.5c0-.08-.06-.16-.15-.16h-1.77c-.08 0-.15.07-.15.16v1.58c0 .09.07.16.15.16M6.28 9h1.76c.08 0 .16-.09.16-.18V7.25c0-.09-.07-.16-.16-.16H6.28c-.09 0-.16.06-.16.16v1.57c.01.09.07.18.16.18m2.47 0h1.75c.1 0 .17-.09.17-.18V7.25c0-.09-.06-.16-.17-.16H8.75c-.08 0-.15.06-.15.16v1.57c0 .09.06.18.15.18m2.44 0h1.77c.08 0 .15-.09.15-.18V7.25c0-.09-.07-.16-.15-.16h-1.77c-.08 0-.15.06-.15.16v1.57c0 .09.07.18.15.18m0-2.28h1.77c.08 0 .15-.07.15-.16V5c0-.1-.07-.17-.15-.17h-1.77c-.08 0-.15.06-.15.17v1.56c0 .08.07.16.15.16m2.46 4.52h1.76c.09 0 .16-.07.16-.16V9.5c0-.08-.07-.16-.16-.16h-1.76c-.08 0-.15.07-.15.16v1.58c0 .09.07.16.15.16" fill="#0288d1" />
    </svg>
  )
}

export function MaterialIconThemeDocument(props: SVGProps<SVGSVGElement>) {
  return (
    <svg height={24} viewBox="0 0 24 24" width={24} xmlns="http://www.w3.org/2000/svg" {...props}>
      <g fill="none">
        <path d="M0 0h24v24H0z" />
        <path d="M8 16h8v2H8zm0-4h8v2H8zm6-10H6c-1.1 0-2 .9-2 2v16c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8zm4 18H6V4h7v5h5z" fill="#42a5f5" />
      </g>
    </svg>
  )
}
