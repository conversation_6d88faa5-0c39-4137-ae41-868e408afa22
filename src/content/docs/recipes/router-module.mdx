# 路由模块

<CalloutInfo>
  <div>本章内容仅适用于 HTTP 应用。</div>
</CalloutInfo>

在 HTTP 应用（如 RESTful API）中，路由的完整路径是由控制器上 `@Controller()` 装饰器所定义的前缀（可选），与方法装饰器（如 `@Get('users')`) 中指定的路径组合而成。你可以在[本章](/controllers#路由)了解更多细节。此外，你也可以为应用的所有路由设置[全局前缀](/faq/global-prefix)，或启用[版本控制](/techniques/versioning)。

有时，为模块内注册的所有控制器统一添加路径前缀会非常方便。例如，假设你的 REST 应用中有一组专门服务于「仪表盘（Dashboard）」功能的端点。此时，为避免在每个相关控制器中重复添加 `/dashboard` 前缀，你可以借助便捷的 `RouterModule` 来实现，示例如下：

```ts filename='app.module.ts'
import { RouterModule } from '@nestjs/core'

@Module({
  imports: [
    DashboardModule,
    RouterModule.register([
      {
        path: 'dashboard',
        module: DashboardModule,
      },
    ]),
  ],
})
export class AppModule {}
```

`RouterModule` 还支持定义层级路由。这意味着模块可以包含 `children` 属性来注册子模块，而子模块会自动继承父模块的路径前缀。在下例中，我们将 `AdminModule` 注册为 `DashboardModule` 和 `MetricsModule` 的父级。

```ts
@Module({
  imports: [
    AdminModule,
    DashboardModule,
    MetricsModule,
    RouterModule.register([
      {
        path: 'admin',
        module: AdminModule,
        children: [
          {
            path: 'dashboard',
            module: DashboardModule,
          },
          {
            path: 'metrics',
            module: MetricsModule,
          },
        ],
      },
    ])
  ],
})
```

<CalloutInfo>
  <div>建议谨慎使用此特性，因为过度嵌套可能会增加后续的代码维护难度。</div>
</CalloutInfo>

在上面的例子中，`DashboardModule` 中注册的所有控制器都会被添加 `/admin/dashboard` 路径前缀（因为模块路径会自顶向下递归拼接）。同理，`MetricsModule` 中的所有控制器都将应用 `/admin/metrics` 前缀。
