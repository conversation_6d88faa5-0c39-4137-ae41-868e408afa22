import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '~/components/ui/accordion'

# Prisma

[Prisma](https://www.prisma.io) 是一款适用于 Node.js 和 TypeScript 的[开源](https://github.com/prisma/prisma) ORM（Object-Relational Mapping，对象关系映射）。你可以用它来替代手写原生 SQL、[knex.js](https://knexjs.org/) 等 SQL 查询构建器，以及 [TypeORM](https://typeorm.io/)、[Sequelize](https://sequelize.org/) 等其他 ORM。Prisma 目前支持 PostgreSQL、MySQL、SQL Server、SQLite、MongoDB 和 CockroachDB（[预览版](https://www.prisma.io/docs/reference/database-reference/supported-databases)）。

尽管 Prisma 也能与原生 JavaScript 搭配使用，但它与 TypeScript 深度集成，提供了比同类 ORM 更强的类型安全（type-safety）保障。关于 Prisma 与 TypeORM 在类型安全方面的详细对比，你可以参阅[这篇文档](https://www.prisma.io/docs/concepts/more/comparisons/prisma-and-typeorm#type-safety)。

<CalloutInfo>
  若想快速了解 Prisma
  的工作原理，可以查阅其[快速上手](https://www.prisma.io/docs/getting-started/quickstart)指南或[官方介绍](https://www.prisma.io/docs/understand-prisma/introduction)。此外，[`prisma-examples`](https://github.com/prisma/prisma-examples/)
  仓库中也提供了可直接运行的
  [REST](https://github.com/prisma/prisma-examples/tree/b53fad046a6d55f0090ddce9fd17ec3f9b95cab3/orm/nest)
  和
  [GraphQL](https://github.com/prisma/prisma-examples/tree/b53fad046a6d55f0090ddce9fd17ec3f9b95cab3/orm/nest-graphql)
  示例项目。
</CalloutInfo>

## 快速上手

在本节中，你将从零开始，学习如何在 NestJS 中集成 Prisma。我们将一起构建一个简单的 NestJS 应用，并实现一个能够读写数据库的 RESTful API。

为简化起见，本指南将使用 [SQLite](https://sqlite.org/) 数据库，它无需单独的数据库服务器。当然，如果你使用的是 PostgreSQL 或 MySQL，本指南同样适用 —— 在相关步骤中，我们提供了针对这些数据库的额外说明。

<CalloutInfo>
  如果你希望将 Prisma 集成到现有项目中，请参考[在现有项目中添加 Prisma
  的指南](https://www.prisma.io/docs/getting-started/setup-prisma/add-to-existing-project-typescript-postgres)。如果你正在从
  TypeORM 迁移，可以阅读[从 TypeORM 迁移到 Prisma
  的指南](https://www.prisma.io/docs/guides/migrate-to-prisma/migrate-from-typeorm)。
</CalloutInfo>

## 创建 NestJS 项目

首先，安装 Nest 命令行工具（Nest CLI），并使用以下命令创建一个新的项目：

```bash
$ npm install -g @nestjs/cli
$ nest new hello-prisma
```

关于该命令创建的项目结构的详细说明，请参阅[第一步](/first-steps)章节。现在，你可以运行 `npm start` 来启动应用。默认情况下，应用运行在 `http://localhost:3000/`，并只包含一个定义在 `src/app.controller.ts` 中的路由。接下来，你将逐步添加更多路由，用于存储和获取「用户」 (`User`) 和「帖子」 (`Post`) 数据。

## 配置 Prisma

首先，在项目中安装 Prisma CLI 作为开发依赖：

```bash
$ cd hello-prisma
$ npm install prisma --save-dev
```

在后续步骤中，我们将使用 [Prisma CLI](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-cli)。作为最佳实践，我们推荐通过 `npx` 在项目本地执行它：

```bash
$ npx prisma
```

<Accordion type="single" collapsible>
  <AccordionItem value="yarn">
    <AccordionTrigger>如果你使用 Yarn</AccordionTrigger>
    <AccordionContent>
如果你使用 Yarn，可以通过以下方式安装 Prisma CLI：

```bash
$ yarn add prisma --dev
```

安装完成后，可以在命令前添加 `yarn` 来调用 CLI：

```bash
$ yarn prisma
```

    </AccordionContent>

  </AccordionItem>
</Accordion>

现在，使用 Prisma CLI 的 `init` 命令来初始化 Prisma 配置：

```bash
$ npx prisma init
```

该命令会创建一个 `prisma` 目录，其中包含两个文件：

- `schema.prisma`：用于定义数据库连接信息和数据模型。
- `.env`：一个 [dotenv](https://github.com/motdotla/dotenv) 文件，通常用于存放数据库连接凭据等环境变量。

## 配置数据库连接

数据库连接信息配置在 `schema.prisma` 文件的 `datasource` 代码块中。`provider` 字段默认为 `postgresql`。由于本指南将使用 SQLite，你需要将 `provider` 的值修改为 `sqlite`：

```groovy
datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}
```

接下来，打开 `.env` 文件，将 `DATABASE_URL` 环境变量修改为如下内容：

```bash
DATABASE_URL="file:./dev.db"
```

请确保已配置 [ConfigModule](/techniques/configuration)，否则 `DATABASE_URL` 环境变量将无法从 `.env` 文件中加载。

SQLite 是一个基于文件的数据库，无需专门的服务器进程即可运行。因此，你只需将连接 URL 指向一个本地文件（本例中为 `dev.db`），而无需像其他数据库那样配置 `host` 和 `port`。这个文件将在下一步被自动创建。

<Accordion type="single" collapsible className="border border-border rounded-md px-2 [&_h3]:m-0">
  <AccordionItem value="yarn">
    <AccordionTrigger>针对 PostgreSQL、MySQL 或 SQL Server 的配置说明</AccordionTrigger>
    <AccordionContent>
对于 PostgreSQL 和 MySQL，你需要将连接 URL 配置为指向数据库服务器。你可以在[这篇 Prisma 文档](https://www.prisma.io/docs/reference/database-reference/connection-urls)中了解所需的连接 URL 格式。

**PostgreSQL**

如果你使用的是 PostgreSQL，需要按如下方式调整 `schema.prisma` 和 `.env` 文件：

**`schema.prisma`**

```groovy
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}
```

**`.env`**

```bash
DATABASE_URL="postgresql://USER:PASSWORD@HOST:PORT/DATABASE?schema=SCHEMA"
```

请将上述大写字母占位符替换为你的实际数据库凭据。如果你不确定 `SCHEMA` 的值，可以直接使用默认的 `public`：

```bash
DATABASE_URL="postgresql://USER:PASSWORD@HOST:PORT/DATABASE?schema=public"
```

如果你需要从零开始搭建 PostgreSQL 数据库，可以参考这篇关于[在 Heroku 上搭建免费 PostgreSQL 数据库](https://dev.to/prisma/how-to-setup-a-free-postgresql-database-on-heroku-1dc1)的指南。

**MySQL**

如果你使用的是 MySQL，需要按如下方式调整 `schema.prisma` 和 `.env` 文件：

**`schema.prisma`**

```groovy
datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}
```

**`.env`**

```bash
DATABASE_URL="mysql://USER:PASSWORD@HOST:PORT/DATABASE"
```

请将上述大写字母占位符替换为你的实际数据库凭据。

**Microsoft SQL Server / Azure SQL Server**

如果你使用的是 Microsoft SQL Server 或 Azure SQL Server，需要按如下方式调整 `schema.prisma` 和 `.env` 文件：

**`schema.prisma`**

```groovy
datasource db {
  provider = "sqlserver"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}
```

**`.env`**

请将上述大写字母占位符替换为你的实际数据库凭据。如果你不确定 `encrypt` 的值，通常可以设为 `true`：

```bash
DATABASE_URL="sqlserver://HOST:PORT;database=DATABASE;user=USER;password=PASSWORD;encrypt=true"
```

    </AccordionContent>

  </AccordionItem>
</Accordion>

## 使用 Prisma Migrate 创建两张数据库表

在本节中，你将学习如何使用 [Prisma Migrate](https://www.prisma.io/docs/concepts/components/prisma-migrate) 在数据库中创建两张新表。Prisma Migrate 会根据你在 `schema.prisma` 文件中定义的声明式数据模型，生成相应的 SQL 迁移文件。这些迁移文件是完全可定制的，你可以按需调整，用于配置底层数据库的特定功能或添加数据填充（seeding）等额外操作。

首先，在你的 `schema.prisma` 文件中添加以下两个模型：

```prisma
model User {
  id    Int     @default(autoincrement()) @id
  email String  @unique
  name  String?
  posts Post[]
}

model Post {
  id        Int      @default(autoincrement()) @id
  title     String
  content   String?
  published Boolean? @default(false)
  author    User?    @relation(fields: [authorId], references: [id])
  authorId  Int?
}
```

定义好 Prisma 模型后，你就可以生成 SQL 迁移文件并将其应用到数据库。请在终端中运行以下命令：

```bash
$ npx prisma migrate dev --name init
```

`prisma migrate dev` 命令会生成 SQL 文件并直接在数据库中执行。该命令将在 `prisma` 目录下创建如下迁移文件结构：

```bash
$ tree prisma
prisma
├── dev.db
├── migrations
│   └── 20201207100915_init
│       └── migration.sql
└── schema.prisma
```

<Accordion type="single" collapsible className="border border-border rounded-md px-2 [&_h3]:m-0">
  <AccordionItem value="yarn">
    <AccordionTrigger>查看生成的 SQL 语句</AccordionTrigger>
    <AccordionContent>
本次迁移会在你的 SQLite 数据库中创建如下表结构：

```sql
-- CreateTable
CREATE TABLE "User" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "email" TEXT NOT NULL,
    "name" TEXT
);

-- CreateTable
CREATE TABLE "Post" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "title" TEXT NOT NULL,
    "content" TEXT,
    "published" BOOLEAN DEFAULT false,
    "authorId" INTEGER,

    FOREIGN KEY ("authorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "User.email_unique" ON "User"("email");
```

    </AccordionContent>

  </AccordionItem>
</Accordion>

## 安装并生成 Prisma Client

Prisma Client 是一款类型安全的数据库客户端，它会根据你的 Prisma 模型定义自动**生成**。通过这种方式，Prisma 客户端能够为你的模型**量身定制** [CRUD](https://www.prisma.io/docs/concepts/components/prisma-client/crud) 操作。

要在你的项目中安装 Prisma 客户端，请在终端中运行以下命令：

```bash
$ npm install @prisma/client
```

请注意，在安装过程中，Prisma 会自动为你执行 `prisma generate` 命令。此后，每当你修改 Prisma 模型时，都需要重新运行该命令，以更新生成的 Prisma 客户端。

<CalloutInfo>
  `prisma generate` 命令会读取你的 Prisma schema，并在
  `node_modules/@prisma/client` 目录下更新 Prisma 客户端库。
</CalloutInfo>

## 在 NestJS 服务中使用 Prisma Client

现在，你已经可以通过 Prisma Client 来发送数据库查询。如果你想进一步了解如何使用 Prisma Client 构建查询，可以查阅其 [API 文档](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/crud)。

在构建 NestJS 应用时，我们建议将 Prisma Client 的数据库查询 API 封装在服务（Service）中。首先，你可以创建一个新的 `PrismaService`，它负责实例化 `PrismaClient` 并连接到数据库。

在 `src` 目录下，创建一个名为 `prisma.service.ts` 的文件，并添加以下代码：

```ts
import { Injectable, OnModuleInit } from '@nestjs/common'
import { PrismaClient } from '@prisma/client'

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit {
  async onModuleInit() {
    // 建议显式调用 $connect()，以尽早捕获连接错误
    await this.$connect()
  }
}
```

<CalloutInfo>
  `onModuleInit` 是可选的。如果省略此方法，Prisma
  会在首次执行数据库查询时进行懒连接（lazy connect）。
</CalloutInfo>

接下来，你可以开始编写服务，通过 Prisma schema 中定义的 `User` 和 `Post` 模型来操作数据库。

同样在 `src` 目录下，创建 `user.service.ts` 文件并添加以下代码：

```ts
import { Injectable } from '@nestjs/common'
import { PrismaService } from './prisma.service'
import { User, Prisma } from '@prisma/client'

@Injectable()
export class UsersService {
  constructor(private prisma: PrismaService) {}

  async user(
    userWhereUniqueInput: Prisma.UserWhereUniqueInput
  ): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: userWhereUniqueInput,
    })
  }

  async users(params: {
    skip?: number
    take?: number
    cursor?: Prisma.UserWhereUniqueInput
    where?: Prisma.UserWhereInput
    orderBy?: Prisma.UserOrderByWithRelationInput
  }): Promise<User[]> {
    const { skip, take, cursor, where, orderBy } = params
    return this.prisma.user.findMany({
      skip,
      take,
      cursor,
      where,
      orderBy,
    })
  }

  async createUser(data: Prisma.UserCreateInput): Promise<User> {
    return this.prisma.user.create({
      data,
    })
  }

  async updateUser(params: {
    where: Prisma.UserWhereUniqueInput
    data: Prisma.UserUpdateInput
  }): Promise<User> {
    const { where, data } = params
    return this.prisma.user.update({
      data,
      where,
    })
  }

  async deleteUser(where: Prisma.UserWhereUniqueInput): Promise<User> {
    return this.prisma.user.delete({
      where,
    })
  }
}
```

值得注意的是，你在这里使用了 Prisma Client 自动生成的类型。这确保了服务所暴露的方法是类型安全的，因此你无需为模型单独编写数据传输对象（DTO）文件，从而减少了样板代码。

接下来，为 `Post` 模型实现一个类似的服务。

继续在 `src` 目录下，创建 `post.service.ts` 文件并添加以下代码：

```ts
import { Injectable } from '@nestjs/common'
import { PrismaService } from './prisma.service'
import { Post, Prisma } from '@prisma/client'

@Injectable()
export class PostsService {
  constructor(private prisma: PrismaService) {}

  async post(
    postWhereUniqueInput: Prisma.PostWhereUniqueInput
  ): Promise<Post | null> {
    return this.prisma.post.findUnique({
      where: postWhereUniqueInput,
    })
  }

  async posts(params: {
    skip?: number
    take?: number
    cursor?: Prisma.PostWhereUniqueInput
    where?: Prisma.PostWhereInput
    orderBy?: Prisma.PostOrderByWithRelationInput
  }): Promise<Post[]> {
    const { skip, take, cursor, where, orderBy } = params
    return this.prisma.post.findMany({
      skip,
      take,
      cursor,
      where,
      orderBy,
    })
  }

  async createPost(data: Prisma.PostCreateInput): Promise<Post> {
    return this.prisma.post.create({
      data,
    })
  }

  async updatePost(params: {
    where: Prisma.PostWhereUniqueInput
    data: Prisma.PostUpdateInput
  }): Promise<Post> {
    const { data, where } = params
    return this.prisma.post.update({
      data,
      where,
    })
  }

  async deletePost(where: Prisma.PostWhereUniqueInput): Promise<Post> {
    return this.prisma.post.delete({
      where,
    })
  }
}
```

目前，你的 `UsersService` 和 `PostsService` 封装了 Prisma Client 提供的 CRUD 查询。在真实的应用场景中，服务层通常还承载着核心的业务逻辑。例如，你可以在 `UsersService` 中添加一个 `updatePassword` 方法，专门用于处理用户密码的更新。

请记得，在 `AppModule` 中注册这些新创建的服务，以便让 NestJS 的依赖注入系统能够识别并管理它们。

## 在主控制器中实现 REST API 路由

接下来，你将使用先前创建的服务，在主控制器（`AppController`）中实现 REST API 的路由。为简化演示，本指南将所有路由都集中在 `AppController` 这一个类中。

将 `app.controller.ts` 文件内容替换为如下代码：

```ts
import { Controller, Get, Param, Post, Body, Put, Delete } from '@nestjs/common'
import { UsersService } from './user.service'
import { PostsService } from './post.service'
import { User as UserModel, Post as PostModel } from '@prisma/client'

@Controller()
export class AppController {
  constructor(
    private readonly userService: UsersService,
    private readonly postService: PostsService
  ) {}

  @Get('post/:id')
  async getPostById(@Param('id') id: string): Promise<PostModel> {
    return this.postService.post({ id: Number(id) })
  }

  @Get('feed')
  async getPublishedPosts(): Promise<PostModel[]> {
    return this.postService.posts({
      where: { published: true },
    })
  }

  @Get('filtered-posts/:searchString')
  async getFilteredPosts(
    @Param('searchString') searchString: string
  ): Promise<PostModel[]> {
    return this.postService.posts({
      where: {
        OR: [
          {
            title: { contains: searchString },
          },
          {
            content: { contains: searchString },
          },
        ],
      },
    })
  }

  @Post('post')
  async createDraft(
    @Body() postData: { title: string; content?: string; authorEmail: string }
  ): Promise<PostModel> {
    const { title, content, authorEmail } = postData
    return this.postService.createPost({
      title,
      content,
      author: {
        connect: { email: authorEmail },
      },
    })
  }

  @Post('user')
  async signupUser(
    @Body() userData: { name?: string; email: string }
  ): Promise<UserModel> {
    return this.userService.createUser(userData)
  }

  @Put('publish/:id')
  async publishPost(@Param('id') id: string): Promise<PostModel> {
    return this.postService.updatePost({
      where: { id: Number(id) },
      data: { published: true },
    })
  }

  @Delete('post/:id')
  async deletePost(@Param('id') id: string): Promise<PostModel> {
    return this.postService.deletePost({ id: Number(id) })
  }
}
```

该控制器实现了如下路由：

### `GET`

- `/post/:id`：根据 `id` 获取单个帖子
- `/feed`：获取所有已发布的帖子
- `/filtered-posts/:searchString`：根据 `title` 或 `content` 过滤帖子

### `POST`

- `/post`：创建新帖子
  - 请求体：
    - `title: String`（必填）：帖子的标题
    - `content: String`（可选）：帖子的内容
    - `authorEmail: String`（必填）：发帖用户的邮箱
- `/user`：创建新用户
  - 请求体：
    - `email: String`（必填）：用户邮箱
    - `name: String`（可选）：用户名

### `PUT`

- `/publish/:id`：根据 `id` 发布帖子

### `DELETE`

- `/post/:id`：根据 `id` 删除帖子

## 总结

在本教程中，你学习了如何结合 Prisma 与 NestJS 来实现一个 RESTful API。其工作流程是：控制器接收并处理 API 请求，然后调用 `PrismaService`，服务层再通过 Prisma Client 向数据库发送查询，从而满足客户端的数据请求。

如果你想深入探索 NestJS 结合 Prisma 的用法，可以参考下列资源：

- [NestJS & Prisma](https://www.prisma.io/nestjs)
- [REST 与 GraphQL 的开箱即用示例项目](https://github.com/prisma/prisma-examples/)
- [适用于生产环境的入门模板](https://github.com/notiz-dev/nestjs-prisma-starter#instructions)
- [视频：使用 NestJS 与 Prisma 访问数据库（5 分钟）](https://www.youtube.com/watch?v=UlVJ340UEuk&ab_channel=Prisma)（作者：[Marc Stammerjohann](https://github.com/marcjulian)）
