import terminusErrorLogPrettyImage from '/public/assets/Terminus_Error_Log_Pretty.png'
import terminusErrorLogJsonImage from '/public/assets/Terminus_Error_Log_Json.png'

# 健康检查

Terminus 集成可以为你提供**就绪性/存活性（readiness/liveness）** 健康检查。在复杂的后端架构中，健康检查是不可或缺的一环。简单来说，Web 开发中的健康检查通常指一个特殊的端点（Endpoint），例如 `https://my-website.com/health/readiness`。

你的基础设施中的某个服务或组件（例如 [Kubernetes](https://kubernetes.io/)）会持续轮询此端点，当向此端点发起的 `GET` 请求返回「不健康」的 HTTP 状态码时，该服务就会采取相应的纠正措施。

「健康」或「不健康」的定义因服务类型而异，因此，**Terminus** 提供了一套 **健康指示器（Health Indicator）**，让你可以根据具体场景自定义健康评估逻辑。

例如，假设你的 Web 服务器使用 MongoDB 存储数据，那么确认 MongoDB 服务是否正常运行就至关重要。
此时，便可以使用 `MongooseHealthIndicator`。正确配置后（下文将详细说明），你的健康检查端点就会根据 MongoDB 的运行状态返回「健康」或「不健康」的 HTTP 状态码。

## 快速开始

要开始使用 `@nestjs/terminus`，首先需要安装相应的依赖包。

```bash
npm install @nestjs/terminus
```

## 配置健康检查

健康检查是多个**健康指示器**的集合。每个健康指示器负责检查特定服务是否处于健康状态。只有当所有健康指示器都报告正常时，整个健康检查才算通过。
由于许多应用程序都有相似的健康检查需求，[`@nestjs/terminus`](https://github.com/nestjs/terminus) 提供了一套预定义的指示器，例如：

- `HttpHealthIndicator`
- `TypeOrmHealthIndicator`
- `MongooseHealthIndicator`
- `SequelizeHealthIndicator`
- `MikroOrmHealthIndicator`
- `PrismaHealthIndicator`
- `MicroserviceHealthIndicator`
- `GRPCHealthIndicator`
- `MemoryHealthIndicator`
- `DiskHealthIndicator`

接下来，我们来创建一个 `HealthModule`，并在其 `imports` 数组中导入 `TerminusModule`。

<CalloutInfo>
  你可以通过 [Nest 命令行工具（Nest CLI）](/cli/overview) 快速创建模块，只需执行
  `$ nest g module health` 命令。
</CalloutInfo>

```ts filename='health.module.ts'
import { Module } from '@nestjs/common'
import { TerminusModule } from '@nestjs/terminus'

@Module({
  imports: [TerminusModule],
})
export class HealthModule {}
```

接下来，我们需要通过[控制器](/controllers)来暴露健康检查端点。同样，你可以使用 Nest CLI 轻松创建控制器：

```bash
$ nest g controller health
```

<CalloutInfo>
  强烈建议为你的应用启用[关闭钩子（Shutdown
  Hooks）](/fundamentals/lifecycle-events#application-shutdown)。启用后，Terminus
  集成将利用此生命周期事件，以确保服务能够平稳关闭。
</CalloutInfo>

## HTTP 健康检查

在安装 `@nestjs/terminus`、导入 `TerminusModule` 并创建控制器后，你就可以着手实现健康检查了。

`HTTPHealthIndicator` 依赖 `@nestjs/axios` 包，因此你需要先安装它：

```bash
npm install @nestjs/axios axios
```

接下来，我们来设置 `HealthController`：

```ts filename='health.controller.ts'
import { Controller, Get } from '@nestjs/common'
import {
  HealthCheckService,
  HttpHealthIndicator,
  HealthCheck,
} from '@nestjs/terminus'

@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private http: HttpHealthIndicator
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.http.pingCheck('nestjs-docs', 'https://docs.nestjs.com'),
    ])
  }
}
```

```ts filename='health.module.ts'
import { Module } from '@nestjs/common'
import { TerminusModule } from '@nestjs/terminus'
import { HttpModule } from '@nestjs/axios'
import { HealthController } from './health.controller'

@Module({
  imports: [TerminusModule, HttpModule],
  controllers: [HealthController],
})
export class HealthModule {}
```

上述代码配置了一个健康检查端点，它会向 `https://docs.nestjs.com` 发送 `GET` 请求。如果请求成功，访问 `http://localhost:3000/health` 将返回 `200` 状态码和如下内容的 JSON 对象：

```json
{
  "status": "ok",
  "info": {
    "nestjs-docs": {
      "status": "up"
    }
  },
  "error": {},
  "details": {
    "nestjs-docs": {
      "status": "up"
    }
  }
}
```

该响应对象的结构遵循 `@nestjs/terminus` 包中定义的 `HealthCheckResult` 接口。

| 属性      | 描述                                                                                                           | 类型                                 |
| :-------- | :------------------------------------------------------------------------------------------------------------- | :----------------------------------- |
| `status`  | 当任一健康指示器失败时，状态为 `'error'`。若 NestJS 应用正在关闭但仍接受 HTTP 请求，状态为 `'shutting_down'`。 | `'error' \| 'ok' \| 'shutting_down'` |
| `info`    | 包含所有状态为 `'up'` 的健康指示器信息的对象。                                                                 | `object`                             |
| `error`   | 包含所有状态为 `'down'` 的健康指示器信息的对象。                                                               | `object`                             |
| `details` | 包含所有健康指示器（无论状态如何）的详细信息的对象。                                                           | `object`                             |

## 检查特定的 HTTP 响应码

在某些场景下，你可能需要检查特定条件来验证响应。例如，假设 `https://my-external-service.com` 会返回 `204` 响应码。你可以使用 `HttpHealthIndicator.responseCheck` 方法来专门检查此响应码，同时将所有其他响应码都视为不健康状态。

在下面的示例中，如果返回的响应码不是 `204`，则健康检查会被视为不通过。该方法的第三个参数是一个函数（同步或异步均可），用于自定义判断响应是否健康的逻辑。该函数应返回布尔值，`true` 表示健康，`false` 表示不健康。

```ts filename='health.controller.ts'
// 在 `HealthController` 类中

@Get()
@HealthCheck()
check() {
  return this.health.check([
    () =>
      this.http.responseCheck(
        'my-external-service',
        'https://my-external-service.com',
        (res) => res.status === 204,
      ),
  ])
}
```

## TypeORM 健康检查指示器

Terminus 提供了将数据库检查集成到健康检查中的功能。在使用此健康检查指示器之前，请先阅读[数据库](/techniques/database)章节，并确保你的应用已成功建立数据库连接。

<CalloutInfo>
  在底层，`TypeOrmHealthIndicator` 实际上执行的是一条 `SELECT 1` SQL
  命令，这是一种验证数据库是否仍然可用的常用方法。如果你使用的是 Oracle
  数据库，则会执行 `SELECT 1 FROM DUAL`。
</CalloutInfo>

```ts filename='health.controller.ts'
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([() => this.db.pingCheck('database')])
  }
}
```

如果数据库可用，向 `http://localhost:3000/health` 发送 `GET` 请求后，你将看到如下 JSON 响应：

```json
{
  "status": "ok",
  "info": {
    "database": {
      "status": "up"
    }
  },
  "error": {},
  "details": {
    "database": {
      "status": "up"
    }
  }
}
```

如果你的应用使用了[多个数据库](/techniques/database#多数据库)，则需要将每个数据库连接都注入到 `HealthController` 中。然后，只需将相应的连接实例传递给 `TypeOrmHealthIndicator` 即可。

```ts filename='health.controller.ts'
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
    @InjectConnection('albumsConnection')
    private albumsConnection: Connection,
    @InjectConnection()
    private defaultConnection: Connection
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () =>
        this.db.pingCheck('albums-database', {
          connection: this.albumsConnection,
        }),
      () =>
        this.db.pingCheck('database', { connection: this.defaultConnection }),
    ])
  }
}
```

## 磁盘健康指示器

`DiskHealthIndicator` 用于检查磁盘的存储空间使用情况。首先，你需要在 `HealthController`（健康检查控制器）中注入 `DiskHealthIndicator`。以下示例将检查 `/` 路径（在 Windows 上为 `C:\`）的存储使用情况。当已用空间超过总磁盘空间的 50% 时，健康检查将返回不健康状态。

```ts filename='health.controller.ts'
@Controller('health')
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly disk: DiskHealthIndicator
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () =>
        this.disk.checkStorage('storage', { path: '/', thresholdPercent: 0.5 }),
    ])
  }
}
```

`DiskHealthIndicator.checkStorage` 方法也允许你根据一个固定的字节数阈值进行检查。在下面的示例中，如果 `/my-app/` 路径占用的空间超过 250GB，健康检查会返回不健康状态。

```ts filename='health.controller.ts'
@Controller('health')
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly disk: DiskHealthIndicator
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () =>
        this.disk.checkStorage('storage', {
          path: '/',
          threshold: 250 * 1024 * 1024 * 1024,
        }),
    ])
  }
}
```

## 内存健康指示器

为了确保你的进程不会超过指定的内存限制，可以使用 `MemoryHealthIndicator`。下面的示例演示了如何检查进程的堆内存（heap）。

<CalloutInfo>
<div>
堆（Heap）是用于存储动态分配内存（例如通过 `malloc` 分配）的内存区域。分配在堆上的内存会一直存在，直到发生以下两种情况之一：
</div>

<ul className="!m-0">
  <li>内存被显式释放（例如通过 `free()`）</li>
  <li>程序终止</li>
</ul>

</CalloutInfo>

```ts filename='health.controller.ts'
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private memory: MemoryHealthIndicator
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
    ])
  }
}
```

你还可以使用 `MemoryHealthIndicator.checkRSS` 方法来检查进程的 RSS（Resident Set Size，常驻集大小）。在以下示例中，如果进程分配的内存超过 150MB，健康检查将返回不健康状态。

<CalloutInfo>
  RSS（常驻集大小）指分配给进程并实际保留在 RAM
  中的内存总量。它不包括已交换到磁盘的内存，但包括共享库中已加载到内存的页面以及所有的栈和堆内存。
</CalloutInfo>

```ts filename='health.controller.ts'
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private memory: MemoryHealthIndicator
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024),
    ])
  }
}
```

## 自定义健康指示器

有时，`@nestjs/terminus` 提供的预定义健康指示器可能无法满足你所有的健康检查需求。在这种情况下，你可以创建自定义的健康指示器。

我们先来创建一个服务，用于实现自定义指示器。为了帮助你理解其结构，这里以一个 `DogHealthIndicator` 为例。这个指示器会检查所有 `Dog` 对象。如果其中有 `Dog` 的 `type` 属性为 `'badboy'`，指示器将返回不健康状态。反之，如果所有 `Dog` 的 `type` 都是 `'goodboy'`，指示器则返回健康状态 (`'up'`)。

```ts filename='dog.health.ts'
import { Injectable } from '@nestjs/common'
import { HealthIndicatorService } from '@nestjs/terminus'

export interface Dog {
  name: string
  type: string
}

@Injectable()
export class DogHealthIndicator {
  constructor(
    private readonly healthIndicatorService: HealthIndicatorService
  ) {}

  private dogs: Dog[] = [
    { name: 'Fido', type: 'goodboy' },
    { name: 'Rex', type: 'badboy' },
  ]

  async isHealthy(key: string) {
    const indicator = this.healthIndicatorService.check(key)
    const badboys = this.dogs.filter((dog) => dog.type === 'badboy')
    const isHealthy = badboys.length === 0

    if (!isHealthy) {
      return indicator.down({ badboys: badboys.length })
    }

    return indicator.up()
  }
}
```

接着，需要将这个健康指示器注册为提供者。

```ts filename='health.module.ts'
import { Module } from '@nestjs/common'
import { TerminusModule } from '@nestjs/terminus'
import { DogHealthIndicator } from './dog.health'

@Module({
  controllers: [HealthController],
  imports: [TerminusModule],
  providers: [DogHealthIndicator],
})
export class HealthModule {}
```

<CalloutInfo>
  在实际项目中，`DogHealthIndicator` 应该在它自己所属的模块（例如
  `DogModule`）中提供，然后再由 `HealthModule` 导入。
</CalloutInfo>

最后一步是在健康检查端点中调用我们的自定义健康指示器。为此，我们需要回到 `HealthController` 并在 `check` 方法中添加该指示器。

```ts filename='health.controller.ts'
import { HealthCheckService, HealthCheck } from '@nestjs/terminus'
import { Injectable, Dependencies, Get } from '@nestjs/common'
import { DogHealthIndicator } from './dog.health'

@Injectable()
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private dogHealthIndicator: DogHealthIndicator
  ) {}

  @Get()
  @HealthCheck()
  healthCheck() {
    return this.health.check([() => this.dogHealthIndicator.isHealthy('dog')])
  }
}
```

## 日志记录

Terminus 只会记录错误信息，例如当健康检查失败时。通过 `TerminusModule.forRoot()` 方法，你可以更灵活地控制错误日志的记录方式，甚至可以完全接管日志记录。

本节将介绍如何创建自定义日志记录器 `TerminusLogger`，它继承自内置的 `Logger`，因此你可以根据需要重写其部分功能。

<CalloutInfo>
  如果你想深入了解 NestJS
  的自定义日志记录器，请参阅[相关章节](/techniques/logger#injecting-a-custom-logger)。
</CalloutInfo>

```ts filename='terminus-logger.service.ts'
import { Injectable, Scope, ConsoleLogger } from '@nestjs/common'

@Injectable({ scope: Scope.TRANSIENT })
export class TerminusLogger extends ConsoleLogger {
  error(message: any, stack?: string, context?: string): void
  error(message: any, ...optionalParams: any[]): void
  error(
    message: unknown,
    stack?: unknown,
    context?: unknown,
    ...rest: unknown[]
  ): void {
    // 在这里重写错误信息的日志记录方式
  }
}
```

创建好自定义日志记录器后，只需将其传递给 `TerminusModule.forRoot()` 即可，如下所示：

```ts filename='health.module.ts'
@Module({
  imports: [
    TerminusModule.forRoot({
      logger: TerminusLogger,
    }),
  ],
})
export class HealthModule {}
```

如果你希望完全屏蔽 Terminus 输出的所有日志（包括错误日志），可以按如下方式配置 Terminus：

```ts filename='health.module.ts'
@Module({
  imports: [
    TerminusModule.forRoot({
      logger: false,
    }),
  ],
})
export class HealthModule {}
```

Terminus 允许你配置健康检查错误在日志中的展示方式。

| 错误日志样式   | 描述                                                                        | 示例图                                                                         |
| :------------- | :-------------------------------------------------------------------------- | :----------------------------------------------------------------------------- |
| `json`（默认） | 当发生错误时，以 JSON 对象的形式输出健康检查结果摘要                        | <DocImage src={terminusErrorLogJsonImage} alt="Terminus Error Log Json" />     |
| `pretty`       | 当发生错误时，以更易读的美化格式输出健康检查摘要，并高亮显示成功/失败的结果 | <DocImage src={terminusErrorLogPrettyImage} alt="Terminus Error Log Pretty" /> |

你可以通过如下配置项 `errorLogStyle` 来切换日志样式：

```ts filename='health.module.ts'
@Module({
  imports: [
    TerminusModule.forRoot({
      errorLogStyle: 'pretty',
    }),
  ],
})
export class HealthModule {}
```

## 优雅关闭超时时间

如果你的应用在关闭（shutdown）前需要一段延迟时间，可以使用 Terminus 来实现。
这个设置在使用 Kubernetes 等编排器（orchestrator）时尤其有用。
将延迟时间设置得略长于就绪检查（readiness check）的间隔，就可以在关闭容器时实现零停机（zero downtime）。

```ts filename='health.module.ts'
@Module({
  imports: [
    TerminusModule.forRoot({
      gracefulShutdownTimeoutMs: 1000,
    }),
  ],
})
export class HealthModule {}
```

## 更多示例

更多示例请参见 [Terminus GitHub 仓库](https://github.com/nestjs/terminus/tree/master/sample)。
