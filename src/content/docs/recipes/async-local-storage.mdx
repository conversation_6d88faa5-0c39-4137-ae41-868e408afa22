# 异步本地存储（Async Local Storage）

`AsyncLocalStorage` 是一个 [Node.js API](https://nodejs.org/api/async_context.html#async_context_class_asynclocalstorage)（基于 `async_hooks` API），它为在应用中传播本地状态提供了一种替代方案，你不再需要通过函数参数来显式传递状态。该机制类似于其他编程语言中的线程本地存储（Thread-Local Storage）。

异步本地存储的核心思想是，使用 `AsyncLocalStorage#run` 方法来*包裹*一个函数调用。这样，所有在该函数调用内部执行的代码，都能访问到同一个 `store`，并且这个 `store` 对每个调用链都是唯一的。

在 NestJS 的上下文中，这意味着我们可以在请求生命周期的某个恰当位置，使用它来包裹后续的请求处理逻辑。这样，我们便能访问和修改那些仅对当前请求可见的状态。该方法为请求作用域（Request-scoped）提供者及其固有的局限性提供了一种有效的替代方案。

此外，我们也可以只在系统的某个特定部分（例如，处理一个事务对象时）使用异步本地存储来传播上下文，而无需在服务之间显式传递它，从而提高了隔离性和封装性。

## 自定义实现

NestJS 本身没有为 `AsyncLocalStorage` 提供内置抽象。下面我们以最简单的 HTTP 场景为例，演示如何自行实现，以帮助你更好地理解这个概念：

<CalloutInfo>
  如果你想直接使用现成的专用包，请直接跳转到 [NestJS CLS](#nestjs-cls) 章节。
</CalloutInfo>

1.  首先，在一个共享的源文件中创建一个 `AsyncLocalStorage` 的实例。在 NestJS 中，推荐的做法是将其封装在一个模块中，并通过自定义提供者进行注册。

```ts filename='als.module.ts'
@Module({
  providers: [
    {
      provide: AsyncLocalStorage,
      useValue: new AsyncLocalStorage(),
    },
  ],
  exports: [AsyncLocalStorage],
})
export class AlsModule {}
```

<CalloutInfo>

`AsyncLocalStorage` 需要从 `async_hooks` 模块导入。

</CalloutInfo>

2.  由于我们当前只关注 HTTP 场景，因此可以通过中间件，用 `AsyncLocalStorage#run` 来包裹 `next` 函数。中间件是处理入站请求的第一个环节，这可以确保 `store` 在所有后续处理逻辑（如增强器、守卫、拦截器等）中都可用。

```ts filename='app.module.ts'
@Module({
  imports: [AlsModule],
  providers: [CatsService],
  controllers: [CatsController],
})
export class AppModule implements NestModule {
  constructor(
    // 注入 AsyncLocalStorage 实例
    private readonly als: AsyncLocalStorage<any>
  ) {}

  configure(consumer: MiddlewareConsumer) {
    // 绑定中间件
    consumer
      .apply((req, res, next) => {
        // 根据请求初始化 store 的默认值
        const store = {
          userId: req.headers['x-user-id'],
        }
        // 用 als.run 方法和 store 包裹 "next" 回调
        this.als.run(store, () => next())
      })
      .forRoutes('*path')
  }
}
```

3.  现在，在请求处理生命周期的任何地方，我们都可以访问到这个本地 `store` 实例了。

```ts filename='cats.service.ts'
@Injectable()
export class CatsService {
  constructor(
    // 注入 AsyncLocalStorage 实例
    private readonly als: AsyncLocalStorage<any>,
    private readonly catsRepository: CatsRepository
  ) {}

  getCatForUser() {
    // getStore 方法始终返回与当前请求关联的 store 实例
    const userId = this.als.getStore()['userId'] as number
    return this.catsRepository.getForUser(userId)
  }
}
```

4.  通过这种方式，我们无需注入完整的 `REQUEST` 对象，便能在同一个请求的生命周期内共享状态。

<CalloutInfo type="warning">
  尽管这项技术在许多场景下都很有用，但它本质上是通过创建隐式上下文来工作的，这可能会让数据流变得不那么明确。因此，请谨慎使用此模式，尤其要避免创建"无所不包"的上下文"[上帝对象](https://zh.wikipedia.org/wiki/上帝对象)"。
</CalloutInfo>

## NestJS CLS

[`nestjs-cls`](https://github.com/Papooch/nestjs-cls) 包在直接使用 `AsyncLocalStorage` 的基础上，提供了多项改善开发者体验（DX）的功能（CLS 是 `continuation-local storage` 的缩写）。它将实现细节封装在 `ClsModule` 中，为不同的传输层（不仅限于 HTTP）提供了多种初始化 `store` 的方式，并且支持强类型。

随后，你可以通过可注入的 `ClsService` 访问 store，或者通过[代理提供者（Proxy Providers）](https://www.npmjs.com/package/nestjs-cls#proxy-providers)将其从业务逻辑中完全抽象出来。

<CalloutInfo>
  `nestjs-cls` 是一个第三方包，并非由 NestJS
  核心团队维护。如果你遇到任何问题，请在[该项目的 GitHub
  仓库](https://github.com/Papooch/nestjs-cls/issues)中提出。
</CalloutInfo>

### 安装

该包除了依赖 `@nestjs` 相关库外，仅使用了 Node.js 的内置 API，因此你可以像安装其他 npm 包一样安装它。

```bash
npm i nestjs-cls
```

### 使用方法

我们可以使用 `nestjs-cls` 来实现与[上文](#自定义实现)手动实现类似的功能，示例如下：

1.  在根模块中导入 `ClsModule`。

```ts filename='app.module.ts'
@Module({
  imports: [
    // 注册 ClsModule
    ClsModule.forRoot({
      middleware: {
        // 自动为所有路由挂载
        // ClsMiddleware
        mount: true,
        // 并通过 setup 方法
        // 提供默认 store 值
        setup: (cls, req) => {
          cls.set('userId', req.headers['x-user-id'])
        },
      },
    }),
  ],
  providers: [CatsService],
  controllers: [CatsController],
})
export class AppModule {}
```

2.  然后，通过 `ClsService` 访问 store 中的值。

```ts filename='cats.service.ts'
@Injectable()
export class CatsService {
  constructor(
    // 注入 ClsService 实例
    private readonly cls: ClsService,
    private readonly catsRepository: CatsRepository
  ) {}

  getCatForUser() {
    // 使用 "get" 方法获取存储的值
    const userId = this.cls.get('userId')
    return this.catsRepository.getForUser(userId)
  }
}
```

3.  如果你希望对 `ClsService` 所管理的 store 值进行强类型约束（并获得字符串键的自动补全），可以在注入 `ClsService<MyClsStore>` 时提供一个可选的类型参数。

```ts
export interface MyClsStore extends ClsStore {
  userId: number
}
```

<CalloutInfo>
  该包还可以自动生成请求 ID，你可以通过 `cls.getId()` 获取它，或通过
  `cls.get(CLS_REQ)` 获取完整的请求对象。
</CalloutInfo>

### 测试

由于 `ClsService` 只是一个普通的可注入提供者，因此在单元测试中可以轻松地将其 mock 掉。

但在某些集成测试中，你可能仍希望使用 `ClsService` 的真实实现。在这种情况下，你需要用 `ClsService#run` 或 `ClsService#runWith` 方法来包裹那些依赖上下文的代码。

```ts
describe('CatsService', () => {
  let service: CatsService
  let cls: ClsService
  const mockCatsRepository = createMock<CatsRepository>()

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      // 按常规方式设置测试模块
      providers: [
        CatsService,
        {
          provide: CatsRepository
          useValue: mockCatsRepository
        }
      ],
      imports: [
        // 导入 ClsModule 的静态版本，仅提供
        // ClsService，不会设置 store
        ClsModule
      ],
    }).compile()

    service = module.get(CatsService)

    // 获取 ClsService 以便后续使用
    cls = module.get(ClsService)
  })

  describe('getCatForUser', () => {
    it('根据用户 id 获取猫', async () => {
      const expectedUserId = 42
      mocksCatsRepository.getForUser.mockImplementationOnce(
        (id) => ({ userId: id })
      )

      // 用 `runWith` 方法包裹测试调用
      // 并传入自定义 store 值
      const cat = await cls.runWith(
        { userId: expectedUserId },
        () => service.getCatForUser()
      )

      expect(cat.userId).toEqual(expectedUserId)
    })
  })
})
```

### 更多信息

访问 [NestJS CLS 的 GitHub 页面](https://github.com/Papooch/nestjs-cls)可以获取完整的 API 文档和更多代码示例。
