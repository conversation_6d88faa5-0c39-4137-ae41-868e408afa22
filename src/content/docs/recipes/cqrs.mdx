# CQRS（命令查询职责分离）

一个典型的 [CRUD](https://zh.wikipedia.org/wiki/增刪查改)（创建、读取、更新和删除）应用，其数据流转过程通常如下：

1. **控制器层**处理 HTTP 请求，并将任务委托给**服务层**（Service）。
2. **服务层**负责处理大部分业务逻辑。
3. **服务层**通过仓储（Repository）或数据访问对象来变更或持久化实体。
4. **实体**是承载数据的对象，通常包含 getter 和 setter 方法。

虽然这种模式对中小型应用来说通常已经足够，但对于更大型、更复杂的项目，它可能不是最佳选择。在这些场景下，**CQRS（Command and Query Responsibility Segregation，命令查询职责分离）** 模型可能是一种更合适、更具可扩展性的方案。其核心优势包括：

- **关注点分离**：将应用的读取（查询）和写入（命令）操作分离到不同的模型中。
- **独立扩展**：可以根据负载情况，独立地对读取和写入两端进行扩展。
- **存储灵活性**：允许为读取和写入操作选择不同类型的、最适合其场景的数据存储方案。
- **性能优化**：可以针对读取和写入操作分别进行性能调优。

为支持该模型，Nest 提供了一个轻量级的 [CQRS 模块](https://github.com/nestjs/cqrs)。本章将介绍如何使用该模块。

## 安装

首先，安装所需依赖包：

```bash
$ npm install --save @nestjs/cqrs
```

安装完成后，在应用的根模块（通常是 `AppModule`）中导入 `CqrsModule.forRoot()`：

```ts
import { Module } from '@nestjs/common'
import { CqrsModule } from '@nestjs/cqrs'

@Module({
  imports: [CqrsModule.forRoot()],
})
export class AppModule {}
```

`CqrsModule` 支持一个可选的配置对象，其可用属性如下表所示：

| 属性                          | 说明                                                                        | 默认值                            |
| ----------------------------- | --------------------------------------------------------------------------- | --------------------------------- |
| `commandPublisher`            | 负责将命令（Command）分发给相应处理器的发布器。                             | `DefaultCommandPubSub`            |
| `eventPublisher`              | 用于发布事件（Event）的发布器，支持事件广播或后续处理。                     | `DefaultPubSub`                   |
| `queryPublisher`              | 用于发布查询（Query）并触发数据检索操作的发布器。                           | `DefaultQueryPubSub`              |
| `unhandledExceptionPublisher` | 负责处理未捕获异常（Unhandled Exception）的发布器，以确保异常被追踪或记录。 | `DefaultUnhandledExceptionPubSub` |
| `eventIdProvider`             | 提供唯一事件 ID 的服务，可通过生成或从事件实例中提取 ID。                   | `DefaultEventIdProvider`          |
| `rethrowUnhandled`            | 控制在处理未捕获异常后是否重新抛出该异常，便于调试和错误管理。              | `false`                           |

## 命令（Commands）

命令用于改变应用的状态。与数据驱动的操作不同，命令应以任务为中心。当一个命令被分发时，它会由对应的**命令处理器（Command Handler）** 来处理，该处理器负责更新应用状态。

```ts filename='heroes-game.service.ts'
@Injectable()
export class HeroesGameService {
  constructor(private commandBus: CommandBus) {}

  async killDragon(heroId: string, killDragonDto: KillDragonDto) {
    return this.commandBus.execute(
      new KillDragonCommand(heroId, killDragonDto.dragonId)
    )
  }
}
```

在上述代码中，我们实例化了 `KillDragonCommand` 类，并将其传递给 `CommandBus` 的 `execute()` 方法。该命令类的定义如下：

```ts filename='kill-dragon.command.ts'
export class KillDragonCommand extends Command<{
  actionId: string // 此类型表示命令执行的结果
}> {
  constructor(
    public readonly heroId: string,
    public readonly dragonId: string
  ) {}
}
```

可以看到，`KillDragonCommand` 继承了 `Command` 类。`Command` 是从 `@nestjs/cqrs` 包导出的一个辅助类，用于约束命令的返回类型。在本例中，我们指定返回类型为一个包含 `actionId` 属性的对象。这样，当分发 `KillDragonCommand` 时，`CommandBus#execute()` 方法的返回类型就会被 TypeScript 推断为 `Promise<{ actionId: string }>`。当你希望命令处理器能返回一个值时，这种方式非常有用。

<CalloutInfo>
  继承 `Command` 类是可选的，仅在需要为命令定义返回类型时才需要这样做。
</CalloutInfo>

`CommandBus` 是一个命令**流**，负责将命令分发到对应的处理器。它的 `execute()` 方法会返回一个 Promise，该 Promise 的解析值即为处理器返回的结果。

接下来，我们为 `KillDragonCommand` 创建一个对应的命令处理器。

```ts filename='kill-dragon.handler.ts'
@CommandHandler(KillDragonCommand)
export class KillDragonHandler implements ICommandHandler<KillDragonCommand> {
  constructor(private repository: HeroesRepository) {}

  async execute(command: KillDragonCommand) {
    const { heroId, dragonId } = command
    const hero = this.repository.findOneById(+heroId)

    hero.killEnemy(dragonId)
    await this.repository.persist(hero)

    // "ICommandHandler<KillDragonCommand>" 会强制你返回与命令返回类型一致的值
    return {
      actionId: crypto.randomUUID(), // 该值会返回给调用方
    }
  }
}
```

该处理器从仓储中获取 `Hero` 实体，调用其 `killEnemy()` 方法，然后持久化变更。`KillDragonHandler` 类实现了 `ICommandHandler` 接口，该接口要求实现一个 `execute()` 方法，此方法接收命令实例作为其参数。

值得注意的是，泛型 `ICommandHandler<KillDragonCommand>` 会强制 `execute` 方法的返回值与 `KillDragonCommand` 的返回类型保持一致。在本例中，返回类型是包含 `actionId` 属性的对象。这一类型约束仅在命令继承 `Command` 类时生效，否则你可以从 `execute` 方法中返回任何内容。

最后，将 `KillDragonHandler` 添加到模块的 `providers` 数组中：

```ts
providers: [KillDragonHandler]
```

## 查询（Query）

查询用于从应用程序状态中检索数据，查询应以数据为中心，而非以任务为导向。当分发（dispatch）一个查询时，它会由对应的 **查询处理器（Query Handler）** 进行处理，该处理器负责完成数据检索。

`QueryBus`（查询总线）遵循与 `CommandBus`（命令总线）相同的模式。查询处理器应实现 `IQueryHandler`（查询处理器接口）接口，并使用 `@QueryHandler()` 装饰器进行标记。示例如下：

```ts
export class GetHeroQuery extends Query<Hero> {
  constructor(public readonly heroId: string) {}
}
```

与 `Command` 类类似，`Query` 类是从 `@nestjs/cqrs` 包中导出的一个简单工具类，用于定义查询的返回类型。在本例中，返回类型为 `Hero` 对象。现在，每当分发 `GetHeroQuery` 查询时，`QueryBus#execute()` 方法的返回类型会被自动推断为 `Promise<Hero>`。

要检索 hero 数据，我们需要创建一个查询处理器：

```ts filename='get-hero.handler.ts'
@QueryHandler(GetHeroQuery)
export class GetHeroHandler implements IQueryHandler<GetHeroQuery> {
  constructor(private repository: HeroesRepository) {}

  async execute(query: GetHeroQuery) {
    return this.repository.findOneById(query.heroId)
  }
}
```

`GetHeroHandler` 类实现了 `IQueryHandler` 接口，该接口要求实现 `execute()` 方法。`execute()` 方法接收查询对象作为参数，并且必须返回与查询返回类型匹配的数据（在本例中为 `Hero` 对象）。

最后，请确保在模块中将 `GetHeroHandler` 注册为提供者：

```ts
providers: [GetHeroHandler]
```

现在，可以通过 `QueryBus` 分发查询：

```ts
const hero = await this.queryBus.execute(new GetHeroQuery(heroId)) // "hero" 的类型将自动推断为 "Hero"
```

## 事件（Events）

事件是领域模型中表示状态变化的重要部分。当应用状态改变时，可以发布一个事件来通知其他部分。事件通常由模型（Model）或直接通过 `EventBus`（事件总线）分发。当事件被分发时，会由相应的**事件处理器（Event Handler）** 进行处理。事件处理器可以用来更新读取模型、与其他限界上下文集成等。

下面是一个事件类的示例：

```ts filename='hero-killed-dragon.event.ts'
export class HeroKilledDragonEvent {
  constructor(
    public readonly heroId: string,
    public readonly dragonId: string
  ) {}
}
```

事件既可以直接通过 `EventBus.publish()` 方法分发，也可以在模型中分发。接下来，我们将更新 `Hero` 模型，让它在 `killEnemy()` 方法被调用时，分发一个 `HeroKilledDragonEvent` 事件。

```ts filename='hero.model.ts'
export class Hero extends AggregateRoot {
  constructor(private id: string) {
    super()
  }

  killEnemy(enemyId: string) {
    // 业务逻辑
    this.apply(new HeroKilledDragonEvent(this.id, enemyId))
  }
}
```

`apply()` 方法用于分发事件，它接收一个事件对象作为参数。然而，模型本身并不知道 `EventBus` 的存在，因此需要将事件发布器（Event Publisher）与模型关联起来。这可以通过 `EventPublisher` 类来实现。

```ts filename='kill-dragon.handler.ts'
@CommandHandler(KillDragonCommand)
export class KillDragonHandler implements ICommandHandler<KillDragonCommand> {
  constructor(
    private repository: HeroesRepository,
    private publisher: EventPublisher
  ) {}

  async execute(command: KillDragonCommand) {
    const { heroId, dragonId } = command
    const hero = this.publisher.mergeObjectContext(
      await this.repository.findOneById(+heroId)
    )
    hero.killEnemy(dragonId)
    hero.commit()
  }
}
```

`EventPublisher#mergeObjectContext` 方法会将事件发布器合并到指定的对象实例中。这样，该对象就能将事件发布到事件流中。

注意，本例中我们还调用了模型的 `commit()` 方法，它会分发所有待处理的事件。如果希望事件自动分发，可以将 `autoCommit` 属性设置为 `true`：

```ts
export class Hero extends AggregateRoot {
  constructor(private id: string) {
    super()
    this.autoCommit = true
  }
}
```

如果我们希望将事件发布器合并到一个类（而非其实例）上，可以使用 `EventPublisher#mergeClassContext` 方法：

```ts
const HeroModel = this.publisher.mergeClassContext(Hero)
const hero = new HeroModel('id') // <-- HeroModel 是一个类
```

现在，`HeroModel` 类的每个实例都可以发布事件，而无需再使用 `mergeObjectContext()` 方法。

此外，我们还可以通过 `EventBus` 手动发布事件：

```ts
this.eventBus.publish(new HeroKilledDragonEvent())
```

<CalloutInfo>
  <div>使用事件处理器时需注意，其执行已脱离了传统的 HTTP 请求上下文。</div>
  <ul className="!m-0">
    <li>
      内置的异常过滤器可以捕获 `CommandHandler` （命令处理器）中抛出的错误。
    </li>
    <li>
      但异常过滤器**无法**捕获 `EventHandler`
      （事件处理器）中的错误，因此你需要手动处理。可以采用 `try/catch` 块、通过
      [Saga](#saga) 触发补偿事件，或其它备选方案。
    </li>
    <li>`CommandHandler` 仍然可以向客户端发送 HTTP 响应。</li>
    <li>
      `EventHandler` 则无法发送 HTTP
      响应。如果需要向客户端推送信息，可以考虑使用
      [WebSocket](/websockets/gateways)、[SSE](/techniques/server-sent-events)
      或其它技术方案。
    </li>
  </ul>
</CalloutInfo>

每个事件都可以拥有多个**事件处理器**。

```ts filename='hero-killed-dragon.handler.ts'
@EventsHandler(HeroKilledDragonEvent)
export class HeroKilledDragonHandler
  implements IEventHandler<HeroKilledDragonEvent>
{
  constructor(private repository: HeroesRepository) {}

  handle(event: HeroKilledDragonEvent) {
    // 业务逻辑
  }
}
```

与命令和查询处理器一样，`HeroKilledDragonHandler` 也需要在模块中注册为提供者：

```ts
providers: [HeroKilledDragonHandler]
```

## Saga

Saga（或称「编排器」）是一种设计模式，用于管理应用中的复杂工作流。它通过监听事件并触发新命令来协调各个流程。例如，一个 Saga 可以监听 `UserRegisteredEvent` 事件，在用户成功注册后触发一个发送欢迎邮件的命令。

Saga 是一项非常强大的功能。单个 Saga 可以监听一个或多个事件。借助 [RxJS](https://github.com/ReactiveX/rxjs) 库，可以对事件流进行过滤、映射、分支和合并，从而创建复杂的工作流。每个 Saga 都返回一个产生命令实例的 Observable。随后，`CommandBus` 会**异步**分发这些命令。

下面，我们来创建一个 Saga，它将监听 `HeroKilledDragonEvent` 事件，并分发 `DropAncientItemCommand` 命令。

```ts filename='heroes-game.saga.ts'
@Injectable()
export class HeroesGameSagas {
  @Saga()
  dragonKilled = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType(HeroKilledDragonEvent),
      map((event) => new DropAncientItemCommand(event.heroId, fakeItemID))
    )
  }
}
```

<CalloutInfo>
  <div>`ofType` 操作符和 `@Saga()` 装饰器均由 `@nestjs/cqrs` 包导出。</div>
</CalloutInfo>

`@Saga()` 装饰器用于将一个方法标记为 Saga。`events$` 参数是包含所有事件的 Observable 流。`ofType` 操作符用于根据指定的事件类型过滤事件流，而 `map` 操作符则将事件映射为新的命令实例。

在本例中，Saga 将 `HeroKilledDragonEvent` 事件映射为 `DropAncientItemCommand` 命令，`CommandBus` 随后会自动分发该命令。

与查询、命令和事件处理器一样，`HeroesGameSagas` 也需要在模块中注册为提供者：

```ts
providers: [HeroesGameSagas]
```

## 未处理异常

事件处理器是异步执行的，因此必须始终妥善处理异常，以防止应用进入不一致的状态。如果异常未被处理，`EventBus` 会创建一个 `UnhandledExceptionInfo` 对象，并将其发布到 `UnhandledExceptionBus` 这个 `Observable` 流中。之后，你便可以订阅此流来处理未捕获的异常。

```ts
private destroy$ = new Subject<void>()

constructor(private unhandledExceptionsBus: UnhandledExceptionBus) {
  this.unhandledExceptionsBus
    .pipe(takeUntil(this.destroy$))
    .subscribe((exceptionInfo) => {
      // 在此处处理异常
      // 例如：发送到外部服务、终止进程或发布新事件
    })
}

onModuleDestroy() {
  this.destroy$.next()
  this.destroy$.complete()
}
```

如果需要筛选特定异常，可以使用 `ofType` 操作符，示例如下：

```ts
this.unhandledExceptionsBus
  .pipe(
    takeUntil(this.destroy$),
    UnhandledExceptionBus.ofType(TransactionNotAllowedException)
  )
  .subscribe((exceptionInfo) => {
    // 在此处处理异常
  })
```

其中，`TransactionNotAllowedException` 是我们希望筛选的异常类型。

`UnhandledExceptionInfo` 对象包含以下属性：

```ts
export interface UnhandledExceptionInfo<
  Cause = IEvent | ICommand,
  Exception = any,
> {
  /**
   * 抛出的异常。
   */
  exception: Exception
  /**
   * 异常的原因（事件或命令的引用）。
   */
  cause: Cause
}
```

## 订阅所有事件

`CommandBus`、`QueryBus` 和 `EventBus` 都是**可观察对象**。这意味着我们可以订阅整个事件流，例如处理所有事件。比如，我们可以将所有事件记录到控制台，或者保存到事件存储中。

```ts
private destroy$ = new Subject<void>()

constructor(private eventBus: EventBus) {
  this.eventBus
    .pipe(takeUntil(this.destroy$))
    .subscribe((event) => {
      // 将事件保存到数据库
    })
}

onModuleDestroy() {
  this.destroy$.next()
  this.destroy$.complete()
}
```

## 请求作用域

对于来自其他编程语言背景的开发者而言，可能会对 Nest 的一个特性感到意外：在 Nest 应用中，绝大部分模块都是单例，并在所有请求之间共享。这包括数据库连接池、包含全局状态的单例服务等。需要强调的是，Node.js 并不采用为每个请求分配独立线程的请求/响应模型。因此，在 Nest 应用中使用单例实例是完全安全的。

不过，在某些特殊场景下，可能希望处理器具有基于请求的生命周期。例如在 GraphQL 应用中进行每个请求的缓存、请求追踪或多租户等场景。你可以在[这里](/fundamentals/injection-scopes)了解如何控制作用域。

在 CQRS 模式中，`CommandBus`、`QueryBus` 和 `EventBus` 都是单例，这给请求作用域的提供者带来了额外的复杂性。不过，`@nestjs/cqrs` 包巧妙地解决了这个问题：它会为每个待处理的命令、查询或事件自动创建请求作用域的处理器实例，从而极大地简化了开发过程。

要让处理器成为请求作用域，你可以：

1. 依赖于请求作用域的提供者。
2. 或者，显式地在 `@CommandHandler`、`@QueryHandler` 或 `@EventsHandler` 装饰器中将其作用域设置为 `REQUEST`，如下所示：

```ts
@CommandHandler(KillDragonCommand, {
  scope: Scope.REQUEST,
})
export class KillDragonHandler {
  // 具体实现
}
```

如果你想在任何请求作用域的提供者中注入请求载荷，可以使用 `@Inject(REQUEST)` 装饰器。不过，在 CQRS 场景下，请求载荷的具体内容取决于上下文 —— 它可能是 HTTP 请求、定时任务，或是任何触发命令的操作。

该请求载荷必须是一个继承自 `AsyncContext`（由 `@nestjs/cqrs` 包提供）的类实例。`AsyncContext` 扮演着请求上下文的角色，它允许你在整个请求生命周期内传递和访问数据。

```ts
import { AsyncContext } from '@nestjs/cqrs'

export class MyRequest extends AsyncContext {
  constructor(public readonly user: User) {
    super()
  }
}
```

在执行命令时，将自定义的请求上下文作为第二个参数传递给 `CommandBus#execute` 方法：

```ts
const myRequest = new MyRequest(user)
await this.commandBus.execute(
  new KillDragonCommand(heroId, killDragonDto.dragonId),
  myRequest
)
```

这样，`MyRequest` 实例就会作为 `REQUEST` 提供者，注入到对应的处理器中：

```ts
@CommandHandler(KillDragonCommand, {
  scope: Scope.REQUEST,
})
export class KillDragonHandler {
  constructor(
    @Inject(REQUEST) private request: MyRequest // 注入请求上下文
  ) {}

  // 处理器具体实现
}
```

对于查询（Query）同样适用：

```ts
const myRequest = new MyRequest(user)
const hero = await this.queryBus.execute(new GetHeroQuery(heroId), myRequest)
```

在查询处理器中：

```ts
@QueryHandler(GetHeroQuery, {
  scope: Scope.REQUEST,
})
export class GetHeroHandler {
  constructor(
    @Inject(REQUEST) private request: MyRequest // 注入请求上下文
  ) {}

  // 处理器具体实现
}
```

对于事件（Event），虽然你可以将请求提供者传递给 `EventBus#publish`，但这种做法较少见。更常见的方式是使用 `EventPublisher`，将请求提供者合并到模型对象中：

```ts
const hero = this.publisher.mergeObjectContext(
  await this.repository.findOneById(+heroId),
  this.request // 在此注入请求上下文
)
```

订阅这些事件的请求作用域事件处理器将能够访问到请求提供者。

Saga 负责管理长生命周期的业务流程，因此它始终是单例的。不过，你仍然可以从事件对象中提取出请求上下文：

```ts
@Saga()
dragonKilled = (events$: Observable<any>): Observable<ICommand> => {
  return events$.pipe(
    ofType(HeroKilledDragonEvent),
    map((event) => {
      const request = AsyncContext.of(event) // 获取请求上下文
      const command = new DropAncientItemCommand(event.heroId, fakeItemID)

      AsyncContext.merge(request, command) // 将请求上下文合并到命令中
      return command
    }),
  )
}
```

或者，你也可以使用 `request.attachTo(command)` 方法将请求上下文附加到命令对象上。

## 示例

完整的示例代码，请参见[这个 GitHub 仓库](https://github.com/kamilmysliwiec/nest-cqrs-example)。
