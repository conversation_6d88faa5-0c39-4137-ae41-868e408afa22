# Passport

[Passport](https://github.com/jaredhanson) 是社区中最流行的 Node.js 身份验证库，已在众多生产环境中得到广泛验证。通过 `@nestjs/passport` 模块，你可以轻松地将 Passport 集成到 Nest 应用中。总体而言，Passport 的核心流程包括：

- **验证凭证**：通过验证用户的凭证（如用户名/密码、JSON Web Token 或来自身份提供商的令牌）来完成身份认证。
- **管理会话**：管理认证状态，例如签发可携带的令牌（如 JWT），或创建 [Express 会话（Session）](https://github.com/expressjs/session)。
- **附加用户**：将已认证的用户信息附加到 `request` 对象上，便于后续在路由处理器中访问。

Passport 拥有丰富的[策略（Strategy）](http://www.passportjs.org/)生态，支持多种不同的身份验证机制。尽管 Passport 的核心理念非常简单，但其策略的多样性提供了极高的灵活性。Passport 将这些不同的认证流程抽象为一种标准模式，而 `@nestjs/passport` 模块则将其进一步封装，以完美融入 Nest 的架构风格。

本章将基于这些强大而灵活的模块，为 RESTful API 服务实现一个完整的端到端认证解决方案。你可以沿用本章的思路，选择任意 Passport 策略，来定制自己的认证方案。按照本章的步骤，你将能构建一个完整的示例。

## 认证需求

首先，我们来明确认证需求。在本例中，应用场景如下：

1.  客户端使用用户名和密码进行初始认证。
2.  认证成功后，服务器将签发一个 JWT (JSON Web Token)。
3.  在后续请求中，客户端可以携带此 JWT（作为[授权头中的 `Bearer` 令牌](https://tools.ietf.org/html/rfc6750)）来表明身份。
4.  我们将创建一个受保护的路由，该路由只对持有有效 JWT 的请求开放。

整个实现过程分为三步：实现用户认证、扩展以支持 JWT 签发、以及创建一个用于校验 JWT 的受保护路由。

首先，安装所需依赖。Passport 的 [`passport-local`](https://github.com/jaredhanson/passport-local) 策略实现了基于用户名和密码的本地认证机制，非常适合本例的需求。

```bash
$ npm install --save @nestjs/passport passport passport-local
$ npm install --save-dev @types/passport-local
```

<CalloutInfo type="warning">
  无论选择哪种 Passport 策略，`@nestjs/passport` 和 `passport`
  都是必须安装的核心依赖包。随后，再根据你选择的具体策略（例如 `passport-jwt` 或
  `passport-local`）安装相应的包。此外，为了获得更好的 TypeScript
  开发体验和类型提示，建议同时安装该策略的类型定义包（例如
  `@types/passport-local`）。
</CalloutInfo>

## 实现 Passport 策略

现在，我们来着手实现身份验证功能。首先，我们来概述一下实现**任意** Passport 策略的通用流程。

Passport 可以理解为一个微型框架，它的精妙之处在于将身份验证流程抽象为几个基础步骤，开发者可以根据所选策略进行自定义。它之所以被称为框架，是因为允许你通过传递自定义参数（通常是普通的 JSON 对象）和回调函数来配置其行为，Passport 会在适当的时机调用这些回调。`@nestjs/passport` 模块则将这个框架封装成一个 NestJS 风格的包，以便轻松地集成到 NestJS 应用中。

接下来，我们将使用 `@nestjs/passport`。但在此之前，我们先来了解一下**原生 Passport** 的工作原理。

在原生 Passport 中，配置一个策略需要提供两样东西：

1.  一组特定于该策略的选项。例如，对于 JWT 策略，你可能需要提供用于签名令牌的密钥。
2.  一个「验证回调」（verify callback），它告诉 Passport 如何与你的用户存储（即你管理用户账户的地方）进行交互。在这个回调中，你需要验证用户是否存在（或在必要时创建新用户）以及其凭证是否有效。Passport 期望该回调在验证成功时返回完整的用户对象；如果验证失败（例如用户不存在，或在 `passport-local` 策略下密码不匹配），则返回 `null`。

使用 `@nestjs/passport` 时，你可以通过继承 `PassportStrategy` 类来配置一个 Passport 策略。你可以在子类的构造函数中，通过调用 `super()` 方法来传递策略选项（即上述第一项）。同时，你还需要在子类中实现 `validate()` 方法来提供验证回调（即上述第二项）。

首先，我们来创建一个 `AuthModule` 和 `AuthService`：

```bash
$ nest g module auth
$ nest g service auth
```

为了更好地组织代码，我们将用户相关的逻辑封装到 `UsersService` 中。因此，我们也一并创建 `UsersModule` 和 `UsersService`：

```bash
$ nest g module users
$ nest g service users
```

接下来，将自动生成的文件内容替换为如下代码。在我们的示例应用中，`UsersService` 仅维护一个硬编码的内存用户列表，并提供一个按用户名查找用户的方法。在真实的应用中，这里通常会集成你的用户模型和持久化层，你可以选择任何偏好的库（如 TypeORM、Sequelize、Mongoose 等）。

```ts filename='users/users.service.ts'
import { Injectable } from '@nestjs/common'

// 在实际应用中，这里应该是一个代表用户实体的类或接口
export type User = any

@Injectable()
export class UsersService {
  private readonly users = [
    {
      userId: 1,
      username: 'john',
      password: 'changeme',
    },
    {
      userId: 2,
      username: 'maria',
      password: 'guess',
    },
  ]

  async findOne(username: string): Promise<User | undefined> {
    return this.users.find((user) => user.username === username)
  }
}
```

对于 `UsersModule`，唯一的改动是在 `@Module` 装饰器的 `exports` 数组中添加 `UsersService`。这样，它就可以在模块外部被访问（我们很快将在 `AuthService` 中使用它）。

```ts filename='users/users.module.ts'
import { Module } from '@nestjs/common'
import { UsersService } from './users.service'

@Module({
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
```

`AuthService` 的职责是检索用户并验证密码。为此，我们创建了 `validateUser()` 方法。在下面的代码中，我们使用了 ES6 的展开运算符，在返回用户对象前移除了其 `password` 属性。稍后，我们将在 Passport 的本地策略（local strategy）中调用这个 `validateUser()` 方法。

```ts filename='auth/auth.service.ts'
import { Injectable } from '@nestjs/common'
import { UsersService } from '../users/users.service'

@Injectable()
export class AuthService {
  constructor(private usersService: UsersService) {}

  async validateUser(username: string, pass: string): Promise<any> {
    const user = await this.usersService.findOne(username)
    if (user && user.password === pass) {
      const { password, ...result } = user
      return result
    }
    return null
  }
}
```

<CalloutInfo type="warning">
  在实际应用中，**绝对不要**以明文形式存储密码。你应该使用像
  [bcrypt](https://github.com/kelektiv/node.bcrypt.js#readme)
  这样的库，来对密码进行加盐和单向哈希处理。这意味着，你只存储哈希后的密码，并通过比较存储的哈希值与传入密码的哈希值来进行验证。通过这种方式，用户的明文密码永远不会被存储或暴露。为了保持示例的简洁性，我们在此处使用了明文密码，但这违背了安全的基本原则。**请勿在你的生产环境或真实应用中这样做！**
</CalloutInfo>

现在，我们来更新 `AuthModule`，导入 `UsersModule`。

```ts filename='auth/auth.module.ts'
import { Module } from '@nestjs/common'
import { AuthService } from './auth.service'
import { UsersModule } from '../users/users.module'

@Module({
  imports: [UsersModule],
  providers: [AuthService],
})
export class AuthModule {}
```

## 实现 Passport 本地策略

现在我们可以实现 Passport 的**本地身份验证策略**了。在 `auth` 文件夹下创建一个名为 `local.strategy.ts` 的文件，并添加如下代码：

```ts filename='auth/local.strategy.ts'
import { Strategy } from 'passport-local'
import { PassportStrategy } from '@nestjs/passport'
import { Injectable, UnauthorizedException } from '@nestjs/common'
import { AuthService } from './auth.service'

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private authService: AuthService) {
    super()
  }

  async validate(username: string, password: string): Promise<any> {
    const user = await this.authService.validateUser(username, password)
    if (!user) {
      throw new UnauthorizedException()
    }
    return user
  }
}
```

此实现遵循了前文介绍的 Passport 策略实现模式。在本例中，`passport-local` 策略没有额外的配置项，因此构造函数中直接调用 `super()` 即可，无需传递配置对象。

<CalloutInfo>
  你可以在调用 `super()` 时传递一个配置对象，来自定义该 Passport 策略的行为。例如，`passport-local` 策略默认需要从请求体中获取 `username` 和 `password` 字段。如果需要使用不同的字段名（如 `email`），你可以通过传递配置对象来覆盖默认设置：`super({{ '{' }} usernameField: 'email' {{ '}' }})`。更多信息请参考 [Passport 官方文档](http://www.passportjs.org/docs/configure/)。
</CalloutInfo>

每个 Passport 策略都必须提供一个 `validate()` 方法。对于本地策略，Passport 会从请求中提取 `username` 和 `password`，然后调用我们提供的 `validate(username: string, password: string): any` 方法进行验证。

在这个例子中，实际的用户校验逻辑已经委托给了 `AuthService`，所以 `validate()` 方法的实现非常简洁。它只负责调用服务，然后处理结果。

如果用户凭证有效，`validate()` 方法会返回该用户对象。随后，Passport 会将此用户对象附加到**请求对象**上（通常是 `req.user`）。如果凭证无效，则应抛出 `UnauthorizedException` 异常，这个异常将由 NestJS 的[异常处理层（exceptions layer）](/exception-filters)自动处理。

不同策略的 `validate()` 方法，其核心区别在于如何验证凭证的有效性。例如，JWT 策略的验证逻辑可能是解码令牌（token），并根据令牌中的用户 ID 查询数据库。这种将验证逻辑封装在策略类中的模式，使得认证机制既统一又易于扩展。

最后，我们需要在 `AuthModule` 中注册（register）这个策略，并导入所需的 `PassportModule`。更新 `auth/auth.module.ts` 文件，内容如下：

```ts filename='auth/auth.module.ts'
import { Module } from '@nestjs/common'
import { AuthService } from './auth.service'
import { UsersModule } from '../users/users.module'
import { PassportModule } from '@nestjs/passport'
import { LocalStrategy } from './local.strategy'

@Module({
  imports: [UsersModule, PassportModule],
  providers: [AuthService, LocalStrategy],
})
export class AuthModule {}
```

## 内置 Passport 守卫

在[守卫](/guards) 章节中，我们了解到守卫的核心职责是判断一个请求是否应由特定的路由处理器处理，这个概念依然适用。然而，`@nestjs/passport` 模块在此基础上引入了一些新机制。从身份验证的角度来看，你的应用程序可以分为两种状态：

1.  **未认证**：用户尚未登录。
2.  **已认证**：用户已经登录。

在未认证状态下，主要涉及两个场景：

- **保护路由**：对于需要认证才能访问的路由，我们使用守卫来限制未认证用户的访问。这个守卫的逻辑通常是检查 JWT 的有效性，我们将在能够签发 JWT 之后实现它。

- **处理登录请求**：当用户尝试登录时（例如，通过 `POST` 请求提交用户名和密码到 `/auth/login` 路由），我们需要启动认证流程。要实现这一点，我们需要在路由中触发 `passport-local` 策略。

答案是使用一个特殊守卫。`@nestjs/passport` 模块提供了一个内置的 `AuthGuard`，它会自动调用底层的 Passport 策略，并启动认证流程（如获取凭证、调用验证函数、创建 `user` 属性等）。

对于已认证的用户，我们则使用常规的守卫，允许它们访问受保护的路由。

## 实现登录路由

有了策略之后，我们就可以实现一个 `/auth/login` 路由，并应用内置守卫来启动 `passport-local` 流程。

打开 `app.controller.ts` 文件，并将其内容替换为如下代码：

```ts filename='app.controller.ts'
import { Controller, Request, Post, UseGuards } from '@nestjs/common'
import { AuthGuard } from '@nestjs/passport'

@Controller()
export class AppController {
  @UseGuards(AuthGuard('local'))
  @Post('auth/login')
  async login(@Request() req) {
    return req.user
  }
}
```

通过 `@UseGuards(AuthGuard('local'))`，我们应用了一个 `AuthGuard`。当我们扩展 `passport-local` 策略时，`@nestjs/passport` 会自动配置并提供此守卫。

`'local'` 是 Passport 本地策略的默认名称。在 `@UseGuards()` 装饰器中引用此名称，即可将守卫与 `passport-local` 包提供的代码关联起来。当应用中存在多个 Passport 策略时（例如，本地策略和 JWT 策略），这个名称就用于区分具体要调用哪个策略。

为了便于测试，`/auth/login` 路由目前直接返回了 `user` 对象。这也演示了 Passport 的一个核心特性：在认证成功后，Passport 会自动将 `validate()` 方法返回的用户信息附加到 `Request` 对象上，作为 `req.user` 属性。在后续章节中，我们会将此处的实现替换为生成并返回 JWT 的逻辑。

你可以使用 [cURL](https://curl.haxx.se/) 等工具来测试这个 API 路由，并使用 `UsersService` 中硬编码的用户凭证进行测试。

```bash
$ # 向 /auth/login 发送 POST 请求
$ curl -X POST http://localhost:3000/auth/login -d '{"username": "john", "password": "changeme"}' -H "Content-Type: application/json"
$ # 结果 -> {"userId":1,"username":"john"}
```

尽管这种方式可以正常工作，但直接在 `AuthGuard()` 中使用 `'local'` 这样的魔法字符串（magic string）并非最佳实践。因此，我们推荐创建一个专有的守卫类：

```ts filename='auth/local-auth.guard.ts'
import { Injectable } from '@nestjs/common'
import { AuthGuard } from '@nestjs/passport'

@Injectable()
export class LocalAuthGuard extends AuthGuard('local') {}
```

现在，我们可以更新 `/auth/login` 路由处理器，改为使用 `LocalAuthGuard`：

```ts
@UseGuards(LocalAuthGuard)
@Post('auth/login')
async login(@Request() req) {
  return req.user
}
```

## 实现注销功能

要实现注销功能，可以创建一个额外的路由。在传统的会话认证（session-based authentication）中，通常会调用 `req.logout()` 来清除用户会话。

但需要注意的是，这种方法并**不适用于**纯粹的 JWT 认证方案。由于 JWT 是无状态的，所谓的"注销"实际上是在客户端完成的，即客户端主动删除存储的 JWT。服务端无需进行任何操作。

## JWT 功能

接下来，我们将深入探讨认证系统中的 JWT 部分。让我们回顾并明确一下具体需求：

- 允许用户通过用户名和密码进行身份验证，并返回一个 JWT，用于后续访问受保护的 API 接口。此功能的大部分我们已经实现，现在只需补全签发 JWT 的逻辑即可。
- 创建一个受 JWT（作为 Bearer Token）保护的 API 路由。

我们需要安装几个额外的依赖包来支持 JWT 功能：

```bash
$ npm install --save @nestjs/jwt passport-jwt
$ npm install --save-dev @types/passport-jwt
```

`@nestjs/jwt` 包（详情请见[这里](https://github.com/nestjs/jwt)）是官方提供的 JWT 工具集。`passport-jwt` 则提供了在 Passport 中实现 JWT 认证策略的模块，而 `@types/passport-jwt` 为其提供了 TypeScript 类型定义。

现在，我们来详细分析 `POST /auth/login` 端点的处理流程。该路由受到 `AuthGuard` 保护，并应用了 `passport-local` 策略。这意味着：

1. **只有在用户凭证校验成功后**，该路由的处理函数才会被执行。
2. 请求对象 `req` 中将包含一个 `user` 属性，这是 Passport 在 `local` 策略认证流程中自动注入的。

基于此机制，我们就可以在路由处理函数中生成并返回 JWT。为了保持代码的模块化，我们将把 JWT 的签发逻辑封装在 `AuthService` 中。请打开 `auth` 文件夹下的 `auth.service.ts` 文件，添加 `login()` 方法并注入 `JwtService`：

```ts filename='auth/auth.service.ts'
import { Injectable } from '@nestjs/common'
import { UsersService } from '../users/users.service'
import { JwtService } from '@nestjs/jwt'

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService
  ) {}

  async validateUser(username: string, pass: string): Promise<any> {
    const user = await this.usersService.findOne(username)
    if (user && user.password === pass) {
      const { password, ...result } = user
      return result
    }
    return null
  }

  async login(user: any) {
    const payload = { username: user.username, sub: user.userId }
    return {
      access_token: this.jwtService.sign(payload),
    }
  }
}
```

在 `AuthService` 中，我们注入了 `JwtService` 并利用其 `sign()` 方法来创建 JWT。`payload` 的内容是根据传入的 `user` 对象构建的。`login()` 方法最终返回一个包含已签名 JWT 的对象。

注意，我们遵循 JWT 的标准实践，将 `userId` 存储在 `payload` 的 `sub` 属性（`sub` 即 subject）中。

现在，我们需要更新 `AuthModule`，引入新的依赖并配置 `JwtModule`。

首先，在 `auth` 文件夹下创建 `constants.ts` 文件，用于存放密钥等常量：

```ts filename='auth/constants.ts'
export const jwtConstants = {
  secret:
    'DO NOT USE THIS VALUE. INSTEAD, CREATE A COMPLEX SECRET AND KEEP IT SAFE OUTSIDE OF THE SOURCE CODE.',
}
```

这个常量将用于在后续 JWT 的签发和验证流程中共享同一个密钥。

<CalloutInfo type="warning">
  请勿公开暴露此密钥。此处为了演示方便，密钥被硬编码在代码中，但在生产环境中，**你必须通过机密管理工具、环境变量或配置服务等方式妥善保护此密钥**。
</CalloutInfo>

接下来，打开 `auth` 文件夹下的 `auth.module.ts` 文件，并将其更新为如下内容：

```ts filename='auth/auth.module.ts'
import { Module } from '@nestjs/common'
import { AuthService } from './auth.service'
import { LocalStrategy } from './local.strategy'
import { UsersModule } from '../users/users.module'
import { PassportModule } from '@nestjs/passport'
import { JwtModule } from '@nestjs/jwt'
import { jwtConstants } from './constants'

@Module({
  imports: [
    UsersModule,
    PassportModule,
    JwtModule.register({
      secret: jwtConstants.secret,
      signOptions: { expiresIn: '60s' },
    }),
  ],
  providers: [AuthService, LocalStrategy],
  exports: [AuthService],
})
export class AuthModule {}
```

我们通过 `JwtModule.register()` 方法来配置 `JwtModule`。该方法接收一个配置对象，其中的 `secret` 用于给 JWT 签名，而 `signOptions` 则可以配置令牌的有效期（例如 `expiresIn: '60s'` 表示 60 秒后过期）。

更多关于 `JwtModule` 的信息，请查阅[官方文档](https://github.com/nestjs/jwt/blob/master/README.md)，更多 `signOptions` 的配置项，请参考[这个链接](https://github.com/auth0/node-jsonwebtoken#usage)。

最后，我们更新 `app.controller.ts` 中的 `login` 处理器，让它调用 `AuthService` 的 `login` 方法，并返回生成的 JWT。

```ts filename='app.controller.ts'
import { Controller, Request, Post, UseGuards } from '@nestjs/common'
import { LocalAuthGuard } from './auth/local-auth.guard'
import { AuthService } from './auth/auth.service'

@Controller()
export class AppController {
  constructor(private authService: AuthService) {}

  @UseGuards(LocalAuthGuard)
  @Post('auth/login')
  async login(@Request() req) {
    return this.authService.login(req.user)
  }
}
```

现在，我们用 cURL 来测试 `/auth/login` 路由。你可以使用 `UsersService` 中硬编码的任意一个 `user` 对象来测试。

```bash
$ # 向 /auth/login 发送 POST 请求
$ curl -X POST http://localhost:3000/auth/login -d '{"username": "john", "password": "changeme"}' -H "Content-Type: application/json"
$ # 返回结果 -> {"access_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}
$ # 注意：上方的 JWT 已被截断
```

## 实现 Passport JWT

接下来，我们要实现最后一个核心需求：通过验证请求中携带的 JWT 来保护 API 接口。Passport 同样可以帮助我们实现这一点，它提供了专门用于保护 RESTful 接口的 [passport-jwt](https://github.com/mikenicholson/passport-jwt) 策略。首先，在 `auth` 目录下创建一个名为 `jwt.strategy.ts` 的文件，并添加如下代码：

```ts filename='auth/jwt.strategy.ts'
import { ExtractJwt, Strategy } from 'passport-jwt'
import { PassportStrategy } from '@nestjs/passport'
import { Injectable } from '@nestjs/common'
import { jwtConstants } from './constants'

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: jwtConstants.secret,
    })
  }

  async validate(payload: any) {
    return { userId: payload.sub, username: payload.username }
  }
}
```

`JwtStrategy` 的实现遵循了前文介绍的 Passport 策略的通用模式。该策略需要初始化配置，因此我们向 `super()` 构造函数传递一个选项对象。你可以在[这里](https://github.com/mikenicholson/passport-jwt#configure-strategy)阅读所有可用选项的详细说明。在本例中，主要配置如下：

- `jwtFromRequest`：指定了从请求中提取 JWT 的方法。我们采用标准做法，即在 API 请求的 `Authorization` 请求头中通过 `Bearer` 方案传递令牌（Token）。更多提取方式可参考[这里](https://github.com/mikenicholson/passport-jwt#extracting-the-jwt-from-the-request)。
- `ignoreExpiration`：这里明确地设置为默认值 `false`。这会将 JWT 是否过期的校验责任委托给 Passport 模块。如果请求中带有已过期的 JWT，请求将被拒绝，并返回 `401 Unauthorized` 响应。Passport 会自动处理这个流程。
- `secretOrKey`：我们直接传递对称密钥来为令牌（Token）签名。对于生产环境，强烈建议使用更安全的方式，例如由密钥管理服务（如 Vault）或环境变量提供的 PEM 编码的公钥。无论如何，**切勿在代码中硬编码密钥或将其暴露在公开可访问的地方**。

`validate()` 方法值得重点说明。对于 JWT 策略，Passport 会首先验证令牌（Token）的签名并解析其内容。然后，它会将解析出的 JSON 对象作为唯一参数，传递给 `validate()` 方法。基于 JWT 的签名机制，我们可以确信收到的令牌是由我们系统签发且未被篡改的。

因此，在 `validate()` 回调中，我们只需返回一个包含 `userId` 和 `username` 属性的对象。Passport 会使用这个返回值来创建一个 `user` 对象，并将其附加到请求对象上。

此外，`validate()` 方法还支持返回一个数组，其第一个元素用于创建 `user` 对象，第二个元素用于创建 `authInfo` 对象。

值得一提的是，这种设计为我们后续扩展业务逻辑预留了"钩子"。例如，你可以在 `validate()` 方法中查询数据库，获取更完整的用户信息，从而让 `user` 对象更加丰富；也可以在这里实现更复杂的令牌校验逻辑，比如将 `userId` 与已吊销令牌列表进行比对，以实现令牌撤销功能。本示例采用的是无状态（stateless）JWT 方案，它完全依赖 JWT 本身的有效性来完成授权，并在请求对象中附加少量用户信息（如 `userId` 和 `username`）。

将新的 `JwtStrategy` 作为 `provider` 添加到 `AuthModule` 中：

```ts filename='auth/auth.module.ts'
import { Module } from '@nestjs/common'
import { AuthService } from './auth.service'
import { LocalStrategy } from './local.strategy'
import { JwtStrategy } from './jwt.strategy'
import { UsersModule } from '../users/users.module'
import { PassportModule } from '@nestjs/passport'
import { JwtModule } from '@nestjs/jwt'
import { jwtConstants } from './constants'

@Module({
  imports: [
    UsersModule,
    PassportModule,
    JwtModule.register({
      secret: jwtConstants.secret,
      signOptions: { expiresIn: '60s' },
    }),
  ],
  providers: [AuthService, LocalStrategy, JwtStrategy],
  exports: [AuthService],
})
export class AuthModule {}
```

这里我们提供了与签发 JWT 时相同的密钥（`secret`）。这确保了负责**验证**的 Passport 模块与负责**签发**的 `AuthService` 使用的是同一个密钥。

最后，我们来定义一个 `JwtAuthGuard` 类，它继承自内置的 `AuthGuard`：

```ts filename='auth/jwt-auth.guard.ts'
import { Injectable } from '@nestjs/common'
import { AuthGuard } from '@nestjs/passport'

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {}
```

## 实现受保护的路由和 JWT 策略守卫

现在，我们来实现受保护的路由及其对应的守卫。

打开 `app.controller.ts` 文件并进行如下更新：

```ts filename='app.controller.ts'
import { Controller, Get, Request, Post, UseGuards } from '@nestjs/common'
import { JwtAuthGuard } from './auth/jwt-auth.guard'
import { LocalAuthGuard } from './auth/local-auth.guard'
import { AuthService } from './auth/auth.service'

@Controller()
export class AppController {
  constructor(private authService: AuthService) {}

  @UseGuards(LocalAuthGuard)
  @Post('auth/login')
  async login(@Request() req) {
    return this.authService.login(req.user)
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  getProfile(@Request() req) {
    return req.user
  }
}
```

这里，我们再次应用了由 `@nestjs/passport` 模块提供的 `AuthGuard`。它在配置 `passport-jwt` 模块时被自动创建，并以默认名称 `jwt` 被引用。

当 `GET /profile` 路由被访问时，该守卫会自动执行我们自定义的 `passport-jwt` 策略。此策略会验证 JWT，然后将用户（`user`）的负载（payload）附加到请求对象上。

确保你的应用正在运行，然后使用 `cURL` 测试这些路由。

```bash
$ # GET /profile
$ curl http://localhost:3000/profile
$ # 结果 -> {"statusCode":401,"message":"Unauthorized"}

$ # POST /auth/login
$ curl -X POST http://localhost:3000/auth/login -d '{"username": "john", "password": "changeme"}' -H "Content-Type: application/json"
$ # 结果 -> {"access_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2Vybm... }

$ # 使用上一步返回的 access_token 作为 Bearer 令牌访问 GET /profile
$ curl http://localhost:3000/profile -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2Vybm..."
$ # 结果 -> {"userId":1,"username":"john"}
```

值得注意的是，在 `AuthModule` 中，我们将 JWT 的过期时间有意设置为 `60 秒`。在实际应用中，这个值通常太短。关于令牌过期和刷新机制的详细处理，已超出了本文的讨论范畴。

我们这样设置，是为了演示 JWT 及 `passport-jwt` 策略的一项重要特性：如果你在认证后等待超过 60 秒再请求 `GET /profile`，你将收到一个 `401 Unauthorized`（未授权）响应。这是因为 Passport 会自动验证 JWT 的过期时间，你无需在自己的业务逻辑中手动处理这一过程。

至此，JWT 身份验证的实现就完成了。现在，无论是 JavaScript 客户端（如 Angular、React、Vue）还是其他应用，都可以安全地与我们的 API 服务器进行身份验证和通信了。

## 扩展认证守卫

在大多数情况下，直接使用内置的 `AuthGuard` 就已经足够了。但在某些场景下，你可能希望扩展其默认的错误处理或认证逻辑。为此，你可以继承 `AuthGuard`，并在子类中重写相应方法。

```ts
import {
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common'
import { AuthGuard } from '@nestjs/passport'

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  canActivate(context: ExecutionContext) {
    // 在这里添加自定义认证逻辑
    // 例如，可以调用 super.logIn(request) 来建立一个会话
    return super.canActivate(context)
  }

  handleRequest(err, user, info) {
    // 你可以基于 "info" 或 "err" 参数抛出异常
    if (err || !user) {
      throw err || new UnauthorizedException()
    }
    return user
  }
}
```

除了扩展默认的错误处理与认证逻辑外，你还可以将多个认证策略链接起来。认证将按顺序逐一尝试每个策略，当其中一个策略成功、重定向或抛出错误时，认证链便会中止。只有在所有策略都失败后，认证才会最终失败。

```ts
export class JwtAuthGuard extends AuthGuard(['strategy_jwt_1', 'strategy_jwt_2', '...']) { ... }
```

## 全局启用认证

如果你希望大部分路由默认都受到保护，可以将认证守卫注册为[全局守卫](/guards#binding-guards)。这样就无需在每个控制器上都使用 `@UseGuards()` 装饰器，只需为需要公开访问的路由单独标记即可。

首先，在任意模块中，通过以下方式将 `JwtAuthGuard` 注册为全局守卫：

```ts
providers: [
  {
    provide: APP_GUARD,
    useClass: JwtAuthGuard,
  },
],
```

完成此项配置后，Nest 会自动将 `JwtAuthGuard` 绑定到所有路由。

接下来，需要提供一种机制来声明哪些路由是公开的。为此，可以利用 `SetMetadata` 装饰器工厂函数创建一个自定义装饰器。

```ts
import { SetMetadata } from '@nestjs/common'

export const IS_PUBLIC_KEY = 'isPublic'
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true)
```

这段代码导出了两个常量：元数据键 `IS_PUBLIC_KEY` 和一个名为 `Public` 的自定义装饰器。当然，你也可以根据项目需求将其命名为 `SkipAuth` 或 `AllowAnon`。

有了 `@Public()` 装饰器后，就可以用它来标记任何需要公开访问的方法，例如：

```ts
@Public()
@Get()
findAll() {
  return []
}
```

最后，需要让 `JwtAuthGuard` 在检测到 `isPublic` 元数据时直接放行。为此，可以使用 `Reflector` 辅助类（详细说明见[此处](/guards#putting-it-all-together)）。

```ts
@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(private reflector: Reflector) {
    super()
  }

  canActivate(context: ExecutionContext) {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ])
    if (isPublic) {
      return true
    }
    return super.canActivate(context)
  }
}
```

## 请求作用域策略

Passport API 的核心机制是将策略注册为全局单例。因此，策略本身在设计上并不支持依赖于请求的选项，也无法按请求动态实例化（详情请参阅[请求作用域](/fundamentals/injection-scopes)一章）。当你将策略配置为请求作用域时，Nest 不会实例化它，因为它没有绑定到特定的路由。实际上，也无法在每个请求中区分应该执行哪个「请求作用域」策略。

不过，我们仍然可以在策略内部动态地解析请求作用域的提供者。为此，可以利用[模块引用](/fundamentals/module-ref)功能。

首先，打开 `local.strategy.ts` 文件，并以常规方式注入 `ModuleRef`：

```ts
import { ModuleRef } from '@nestjs/core'

constructor(private moduleRef: ModuleRef) {
  super({
    passReqToCallback: true,
  })
}
```

请确保如上所示，将 `passReqToCallback` 配置属性设置为 `true`。

接着，我们会使用请求实例来获取当前请求的上下文标识符（context ID），而不是生成新的标识符（更多关于请求上下文的内容，请参阅[此节](/fundamentals/module-ref#getting-current-sub-tree)）。

现在，在 `LocalStrategy` 类的 `validate()` 方法中，使用 `ContextIdFactory.getByRequest()` 方法，根据请求对象创建上下文 ID，然后将其传递给 `moduleRef.resolve()` 方法：

```ts
async validate(
  request: Request,
  username: string,
  password: string,
) {
  const contextId = ContextIdFactory.getByRequest(request)
  // 假设 AuthService 是一个请求作用域提供者
  const authService = await this.moduleRef.resolve(AuthService, contextId)
  ...
}
```

在上面的示例中，`resolve()` 方法会异步返回 `AuthService` 提供者的一个请求作用域实例（前提是 `AuthService` 已被注册为请求作用域提供者）。

## 自定义 Passport

你可以通过 `register()` 方法传递任何标准的 Passport 选项，可用选项取决于你所实现的具体策略。例如：

```ts
PassportModule.register({ session: true })
```

你还可以在策略的构造函数中传递一个配置对象，以自定义其行为。以 `local` 策略为例，你可以这样传递参数：

```ts
constructor(private authService: AuthService) {
  super({
    usernameField: 'email',
    passwordField: 'password',
  })
}
```

更多配置项，请参阅官方 [Passport 网站](http://www.passportjs.org/docs/oauth/)。

## 命名策略

实现策略时，可以通过向 `PassportStrategy` 函数传递第二个参数来为策略指定一个名称。如果不提供名称，策略将拥有一个默认名称（例如，`jwt` 策略的默认名称是 `'jwt'`）：

```ts
export class JwtStrategy extends PassportStrategy(Strategy, 'myjwt')
```

然后，就可以通过装饰器 `@UseGuards(AuthGuard('myjwt'))` 来引用该策略。

## GraphQL

如果你想在 [GraphQL](/graphql/quick-start) 中使用 `AuthGuard`，需要继承它并重写 `getRequest()` 方法。

```ts
@Injectable()
export class GqlAuthGuard extends AuthGuard('jwt') {
  getRequest(context: ExecutionContext) {
    const ctx = GqlExecutionContext.create(context)
    return ctx.getContext().req
  }
}
```

如果你想在 GraphQL 的解析器（resolver）中获取当前已认证的用户，可以创建一个 `@CurrentUser()` 装饰器：

```ts
import { createParamDecorator, ExecutionContext } from '@nestjs/common'
import { GqlExecutionContext } from '@nestjs/graphql'

export const CurrentUser = createParamDecorator(
  (data: unknown, context: ExecutionContext) => {
    const ctx = GqlExecutionContext.create(context)
    return ctx.getContext().req.user
  }
)
```

要在解析器中使用该装饰器，只需在查询（query）或变更（mutation）处理函数中将其作为参数注入即可：

```ts
@Query(() => User)
@UseGuards(GqlAuthGuard)
whoAmI(@CurrentUser() user: User) {
  return this.usersService.findById(user.id)
}
```

对于 `passport-local` 策略，你还需要将 GraphQL 上下文中的参数添加到请求体中，以便 Passport 能找到并验证它们。否则，请求将因缺少凭证而认证失败。

```ts
@Injectable()
export class GqlLocalAuthGuard extends AuthGuard('local') {
  getRequest(context: ExecutionContext) {
    const gqlExecutionContext = GqlExecutionContext.create(context)
    const gqlContext = gqlExecutionContext.getContext()
    const gqlArgs = gqlExecutionContext.getArgs()

    gqlContext.req.body = { ...gqlContext.req.body, ...gqlArgs }
    return gqlContext.req
  }
}
```
