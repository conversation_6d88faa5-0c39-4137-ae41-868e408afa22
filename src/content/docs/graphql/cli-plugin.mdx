# CLI 插件

<CalloutInfo type="warning">

本章节仅适用于代码优先（code first）方式。

</CalloutInfo>

TypeScript 的元数据反射（metadata reflection）系统存在一些限制，例如无法确定一个类包含哪些属性，或者识别某个属性是可选还是必填。不过，其中部分限制可以在编译时解决。Nest 提供了一个插件，用于增强 TypeScript 的编译过程，从而减少模板代码（boilerplate）的编写量。

<CalloutInfo>
  该插件为**可选**。如果你愿意，也可以手动声明所有装饰器，或者只在需要的地方声明特定装饰器。
</CalloutInfo>

## 概述

GraphQL 插件会自动完成以下操作：

- 为所有输入对象、对象类型和参数类的属性自动添加 `@Field` 装饰器，除非使用了 `@HideField`
- 根据问号（例如 `name?: string` 会设置 `nullable: true`）自动设置 `nullable` 属性
- 根据类型自动设置 `type` 属性（也支持数组类型）
- 如果设置了 `introspectComments: true`，会根据注释自动生成属性描述

请注意，文件名**必须**包含以下后缀之一，插件才会分析：`['.input.ts', '.args.ts', '.entity.ts', '.model.ts']`（例如：`author.entity.ts`）。如果你使用了不同的后缀，可以通过 `typeFileNameSuffix` 选项调整插件行为（见下文）。

按照目前的做法，你需要重复编写大量代码，让包能够识别你的类型应如何在 GraphQL 中声明。例如，你可以这样定义一个简单的 `Author` 类：

```ts filename='authors/models/author.model.ts'
@ObjectType()
export class Author {
  @Field((type) => ID)
  id: number

  @Field({ nullable: true })
  firstName?: string

  @Field({ nullable: true })
  lastName?: string

  @Field((type) => [Post])
  posts: Post[]
}
```

在中等规模的项目中，这样做问题不大，但当类数量变多时，代码会变得冗长且难以维护。

启用 GraphQL 插件后，上述类定义可以简化为：

```ts filename='authors/models/author.model.ts'
@ObjectType()
export class Author {
  @Field((type) => ID)
  id: number
  firstName?: string
  lastName?: string
  posts: Post[]
}
```

该插件会基于**抽象语法树**，在编译时自动添加合适的装饰器。这样，你无需在代码中到处手动添加 `@Field` 装饰器。

<CalloutInfo>
  插件会自动生成所有缺失的 GraphQL 属性。如果你需要覆盖默认行为，只需通过
  `@Field()` 显式设置即可。
</CalloutInfo>

## 注释自动提取功能

启用注释自动提取（comments introspection）功能后，CLI 插件会根据注释内容为字段自动生成描述信息。

例如，假设有如下 `roles` 属性：

```ts
/**
 * 用户角色列表
 */
@Field(() => [String], {
  description: `用户角色列表`
})
roles: string[]
```

你必须手动重复填写 description 字段。当启用 `introspectComments` 功能后，CLI 插件可以自动提取这些注释，并为属性自动生成描述信息。此时，上述字段可以简化为：

```ts
/**
 * 用户角色列表
 */
roles: string[]
```

## 使用命令行工具插件

要启用该插件，请打开 `nest-cli.json`（如果你使用 [Nest 命令行工具](/cli/overview)），并添加如下 `plugins` 配置：

```json
{
  "collection": "@nestjs/schematics",
  "sourceRoot": "src",
  "compilerOptions": {
    "plugins": ["@nestjs/graphql"]
  }
}
```

你可以通过 `options` 属性自定义插件的行为。

```json
{
  "collection": "@nestjs/schematics",
  "sourceRoot": "src",
  "compilerOptions": {
    "plugins": [
      {
        "name": "@nestjs/graphql",
        "options": {
          "typeFileNameSuffix": [".input.ts", ".args.ts"],
          "introspectComments": true
        }
      }
    ]
  }
}
```

`options` 属性需符合以下接口定义：

```ts
export interface PluginOptions {
  typeFileNameSuffix?: string[]
  introspectComments?: boolean
}
```

| 选项                 | 默认值                                                 | 说明                                              |
| -------------------- | ------------------------------------------------------ | ------------------------------------------------- |
| `typeFileNameSuffix` | `['.input.ts', '.args.ts', '.entity.ts', '.model.ts']` | GraphQL 类型文件的后缀名                          |
| `introspectComments` | `false`                                                | 如果设置为 true，插件会根据注释为属性生成描述信息 |

如果你没有使用命令行工具，而是采用自定义的 `webpack` 配置，也可以将该插件与 `ts-loader` 结合使用：

```js
getCustomTransformers: (program: any) => ({
  before: [require('@nestjs/graphql/plugin').before({}, program)]
}),
```

## SWC 构建器（SWC builder）

对于标准项目结构（非多包仓库结构（Monorepo）），如果希望在 SWC 构建器中使用 CLI 插件，需要按照[此处](/recipes/swc#type-checking)说明启用类型检查（type checking）。

```bash
$ nest start -b swc --type-check
```

如果是多包仓库结构（Monorepo），请参考[这里](/recipes/swc#monorepo-and-cli-plugins)的指引。

```bash
$ npx ts-node src/generate-metadata.ts
# 或 npx ts-node apps/{YOUR_APP}/src/generate-metadata.ts
```

此时，序列化后的元数据文件必须通过 `GraphQLModule` 方法加载，示例如下：

```ts
import metadata from './metadata' // <-- 该文件由 "PluginMetadataGenerator" 自动生成

GraphQLModule.forRoot<...>({
  ..., // 其他选项
  metadata,
}),
```

## 与 `ts-jest` 的集成（端到端测试）

当你在启用此插件的情况下运行端到端测试（e2e tests）时，可能会遇到 schema 编译相关的问题。例如，最常见的错误之一是：

```json
Object type <name> must define one or more fields.
```

出现该问题的原因是 `jest` 配置中并未导入 `@nestjs/graphql/plugin` 插件。

为了解决这个问题，请在你的端到端测试目录下创建如下文件：

```js
const transformer = require('@nestjs/graphql/plugin')

module.exports.name = 'nestjs-graphql-transformer'
// 每当你更改下面的配置时，都应该修改版本号，否则 jest 不会检测到配置变更
module.exports.version = 1

module.exports.factory = (cs) => {
  return transformer.before(
    {
      // @nestjs/graphql/plugin 选项（可以为空）
    },
    cs.program // 对于较旧版本的 Jest（<= v27），应为 "cs.tsCompiler.program"
  )
}
```

完成上述操作后，在你的 `jest` 配置文件中引入 AST 转换器。默认情况下（在起始应用中），端到端测试的配置文件位于 `test` 文件夹下，文件名为 `jest-e2e.json`。

```json
{
  ... // 其他配置
  "globals": {
    "ts-jest": {
      "astTransformers": {
        "before": ["<path to the file created above>"]
      }
    }
  }
}
```

如果你使用的是 `jest@^29`，请参考下方的配置片段，因为前述方法已被废弃。

```json
{
  ... // 其他配置
  "transform": {
    "^.+\\.(t|j)s$": [
      "ts-jest",
      {
        "astTransformers": {
          "before": ["<path to the file created above>"]
        }
      }
    ]
  }
}
```
