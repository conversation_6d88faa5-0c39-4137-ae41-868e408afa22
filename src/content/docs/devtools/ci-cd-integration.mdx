import graphPublishedTerminalImage from '/public/assets/devtools/graph-published-terminal.png'
import projectImage from '/public/assets/devtools/project.png'
import reportImage from '/public/assets/devtools/report.png'
import nodesSelectionImage from '/public/assets/devtools/nodes-selection.png'
import integrateGithubAppImage from '/public/assets/devtools/integrate-github-app.png'
import actionsPreviewImage from '/public/assets/devtools/actions-preview.png'

# CI/CD 集成

<CalloutInfo>
  本节介绍 Nest Devtools 与 Nest 框架的集成。如需了解 Devtools
  应用本身，请访问其[官方网站](https://devtools.nestjs.com)。
</CalloutInfo>

CI/CD 集成功能仅对[企业版](https://enterprise.nestjs.com/)用户开放。

你可以观看下方视频，快速了解 CI/CD 集成的作用与使用方法：

<figure>
  <iframe
    width="100%"
    height="400"
    src="https://www.youtube.com/embed/r5RXcBrnEQ8"
    title="YouTube 视频播放器"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
    allowFullScreen
  />
</figure>

## 发布依赖关系图

首先，你需要在应用的引导文件（`main.ts`）中配置 `GraphPublisher` 类（从 `@nestjs/devtools-integration` 导入，详见上一章）。示例如下：

```ts filename='main.ts'
async function bootstrap() {
  const shouldPublishGraph = process.env.PUBLISH_GRAPH === "true"

  const app = await NestFactory.create(AppModule, {
    snapshot: true,
    preview: shouldPublishGraph,
  })

  if (shouldPublishGraph) {
    await app.init()

    const publishOptions = { ... } // 注意：此 options 对象会根据你所用的 CI/CD 服务商而有所不同
    const graphPublisher = new GraphPublisher(app)
    await graphPublisher.publish(publishOptions)

    await app.close()
  } else {
    await app.listen(process.env.PORT ?? 3000)
  }
}
```

如上所示，此功能使用 `GraphPublisher` 将序列化后的依赖关系图（Graph）发布到集中式注册中心（Registry）。`PUBLISH_GRAPH` 是一个自定义环境变量，用于在 CI/CD 流程中控制是否发布依赖关系图。

将 `preview` 属性设为 `true`，可使应用以预览模式启动，此时所有控制器、增强器和提供者的构造函数及生命周期钩子都不会被执行。请注意，这并非强制要求，但这样做可以简化流程，让应用在 CI/CD 流水线中运行时无需连接数据库等外部服务。

`publishOptions` 对象的具体内容因你使用的 CI/CD 服务商而异。后续章节将为各大主流 CI/CD 服务商提供详细的配置说明。

当依赖关系图成功发布后，你会在工作流视图中看到如下输出：

<DocImage src={graphPublishedTerminalImage} alt="Graph published terminal" />

每次发布依赖关系图后，你都可以在项目页面看到一条新记录：

<DocImage src={projectImage} alt="Project" />

## 报告

只要集中式注册表中存有相应的快照，Devtools 就会为每次构建生成一份报告。例如，当你针对 `master` 分支创建一个拉取请求（PR），并且该分支的依赖关系图已发布过，应用便能检测出差异并生成报告。反之，则不会生成报告。

要查看报告，请前往项目对应的页面（通常在你的组织下）。

<DocImage src={reportImage} alt="Report" />

这有助于发现代码评审过程中容易忽略的变更。例如，假设有人更改了某个**深层嵌套的提供者**的作用域（Scope）。这种变更可能不会被评审者立即察觉，但借助 Devtools，你可以轻松发现这些变更，并确保其是有意为之。再比如，如果你从某个特定端点移除了守卫（Guard），该变更也会在报告中高亮为"受影响项"。若没有为该路由编写集成或端到端测试，你可能不会注意到该路由已不受保护，等到发现时则为时已晚。

同理，如果你在**大型代码库**中将某个模块修改为全局模块（Global），就能看到依赖关系图中新增了多少条边（Edge）。在大多数情况下，这通常意味着该做法可能欠妥。

## 构建预览

对于每个已发布的依赖关系图，你都可以通过点击 **Preview** 按钮来追溯历史，预览其任意历史状态。此外，如果构建已生成报告，你还可以在图中看到高亮显示的差异：

- 绿色节点表示新增元素
- 灰色节点表示已更新元素
- 红色节点表示已删除元素

<DocImage src={nodesSelectionImage} alt="Nodes selection" />

这种"时光回溯"能力让你可以通过对比当前与历史版本的依赖关系图来排查和定位问题。根据你的设置，每个拉取请求，甚至每次提交（Commit），都可以在注册表中生成对应快照，方便你轻松回溯、查看具体变更。你可以将 Devtools 理解为一个具备 Nest 应用依赖关系图可视化能力的 Git，它不仅能追踪变更，还能将其**直观地展示**出来。

## 集成：GitHub Actions

首先，在项目的 `.github/workflows` 目录下创建一个新的 GitHub 工作流文件，例如 `publish-graph.yml`，内容如下：

```yaml filename='publish-graph.yml'
name: Devtools

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - '*'

jobs:
  publish:
    if: github.actor!= 'dependabot[bot]'
    name: Publish graph
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Setup Environment (PR)
        if: {{ '${{' }} github.event_name == 'pull_request' {{ '}}' }}
        shell: bash
        run: |
          echo "COMMIT_SHA={{ '${{' }} github.event.pull_request.head.sha {{ '}}' }}" >>\${GITHUB_ENV}
      - name: Setup Environment (Push)
        if: {{ '${{' }} github.event_name == 'push' {{ '}}' }}
        shell: bash
        run: |
          echo "COMMIT_SHA=\${GITHUB_SHA}" >> \${GITHUB_ENV}
      - name: Publish
        run: PUBLISH_GRAPH=true npm run start
        env:
          DEVTOOLS_API_KEY: CHANGE_THIS_TO_YOUR_API_KEY
          REPOSITORY_NAME: {{ '${{' }} github.event.repository.name {{ '}}' }}
          BRANCH_NAME: {{ '${{' }} github.head_ref || github.ref_name {{ '}}' }}
          TARGET_SHA: {{ '${{' }} github.event.pull_request.base.sha {{ '}}' }}
```

理想情况下，`DEVTOOLS_API_KEY` 环境变量应通过 GitHub Secrets（加密 Secret）进行管理，详情请参考其[官方文档](https://docs.github.com/en/actions/security-guides/encrypted-secrets#creating-encrypted-secrets-for-a-repository)。

该工作流会在每次向 `master` 分支发起拉取请求（Pull Request）或直接推送（Push）时运行。你可以根据项目实际需求调整此配置。此配置的关键在于为 `GraphPublisher` 类提供所需的环境变量。

在使用此工作流前，请务必更新 `DEVTOOLS_API_KEY` 变量。你可以在[此页面](https://devtools.nestjs.com/settings/manage-api-keys)为你的项目生成专用的 API 密钥。

最后，再次打开 `main.ts` 文件，补全之前留空的 `publishOptions` 对象：

```ts filename='main.ts'
const publishOptions = {
  apiKey: process.env.DEVTOOLS_API_KEY,
  repository: process.env.REPOSITORY_NAME,
  owner: process.env.GITHUB_REPOSITORY_OWNER,
  sha: process.env.COMMIT_SHA,
  target: process.env.TARGET_SHA,
  trigger: process.env.GITHUB_BASE_REF ? 'pull' : 'push',
  branch: process.env.BRANCH_NAME,
}
```

为了获得最佳的开发体验，建议你为项目集成 GitHub App，只需点击「Integrate GitHub app」按钮即可（如下图所示）。注意：此为可选步骤。

<DocImage src={integrateGithubAppImage} alt="Integrate GitHub app" />

通过该集成，你可以在拉取请求页面直接查看预览和报告的生成状态：

<DocImage src={actionsPreviewImage} alt="Actions preview" />

## 集成：GitLab Pipelines

首先，在项目根目录创建 `.gitlab-ci.yml` 文件，内容如下：

```yaml filename='.gitlab-ci.yml'
image: node:16

stages:
  - build

cache:
  key:
    files:
      - package-lock.json
  paths:
    - node_modules/

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: always
    - if: $CI_COMMIT_BRANCH == "master" && $CI_PIPELINE_SOURCE == "push"
      when: always
    - when: never

install_dependencies:
  stage: build
  script:
    - npm ci

publish_graph:
  stage: build
  needs:
    - install_dependencies
  script: npm run start
  variables:
    PUBLISH_GRAPH: 'true'
    DEVTOOLS_API_KEY: 'CHANGE_THIS_TO_YOUR_API_KEY'
```

<CalloutInfo>
  与 GitHub Actions 类似，此工作流会在每次向 `master` 分支发起合并请求（Merge
  Request）或直接推送时运行。请务必将 `DEVTOOLS_API_KEY` 作为 CI/CD
  变量添加到你的 GitLab 项目中。理想情况下，应使用 GitLab 的[项目 CI/CD
  变量](https://docs.gitlab.com/ee/ci/variables/#add-a-cicd-variable-to-a-project)功能进行安全存储。你可以在[此页面](https://devtools.nestjs.com/settings/manage-api-keys)生成
  API 密钥。
</CalloutInfo>

最后，更新 `main.ts` 文件中的 `publishOptions` 对象，以使用 GitLab 提供的环境变量：

```ts filename='main.ts'
const publishOptions = {
  apiKey: process.env.DEVTOOLS_API_KEY,
  repository: process.env.CI_PROJECT_NAME,
  owner: process.env.CI_PROJECT_ROOT_NAMESPACE,
  sha: process.env.CI_COMMIT_SHA,
  target: process.env.CI_MERGE_REQUEST_DIFF_BASE_SHA,
  trigger: process.env.CI_MERGE_REQUEST_DIFF_BASE_SHA ? 'pull' : 'push',
  branch:
    process.env.CI_COMMIT_BRANCH ??
    process.env.CI_MERGE_REQUEST_SOURCE_BRANCH_NAME,
}
```

## 其他 CI/CD 工具

Nest Devtools 的 CI/CD 集成可以与任意 CI/CD 工具配合使用（例如 [Bitbucket Pipelines](https://bitbucket.org/product/features/pipelines)、[CircleCI](https://circleci.com/) 等），不限于本文所介绍的工具。

对于其他工具，你需要根据其提供的环境变量，相应地配置 `publishOptions` 对象。大部分所需信息都可以通过 CI/CD 工具的内置环境变量获取，可参考 [CircleCI 内置环境变量列表](https://circleci.com/docs/variables/#built-in-environment-variables)和 [Bitbucket 变量文档](https://support.atlassian.com/bitbucket-cloud/docs/variables-and-secrets/)。

在配置用于发布依赖关系图的流水线时，建议采用以下触发条件：

- `push` 事件 —— 仅当当前分支为部署环境（如 `master`、`main`、`staging`、`production` 等）时触发。
- `pull request` 事件 —— 始终触发，或仅当**目标分支**为部署环境时触发。
