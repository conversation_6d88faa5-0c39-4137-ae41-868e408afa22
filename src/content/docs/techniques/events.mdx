# 事件机制

[`@nestjs/event-emitter`](https://www.npmjs.com/package/@nestjs/event-emitter) 是一个为 NestJS 提供事件机制的模块，它基于简洁的观察者模式实现，使你能够在应用中方便地订阅和监听事件。这种事件机制天然具备解耦能力：同一个事件可以被多个互不依赖的监听器响应，从而提升系统的灵活性与可维护性。

该模块底层基于强大的 [eventemitter2](https://github.com/EventEmitter2/EventEmitter2) 库构建。

## 快速开始

首先，安装所需依赖：

```bash
npm install @nestjs/event-emitter
```

接着，在应用的根模块中引入并初始化 `EventEmitterModule`：

```ts filename='app.module.ts'
import { Module } from '@nestjs/common'
import { EventEmitterModule } from '@nestjs/event-emitter'

@Module({
  imports: [EventEmitterModule.forRoot()],
})
export class AppModule {}
```

调用 `forRoot()` 方法将自动创建一个事件发射器实例，并在 `onApplicationBootstrap` 生命周期钩子中注册所有已声明的事件监听器。这样可以确保监听器在所有模块加载完成后统一注册，避免初始化时序问题。

### 自定义配置

你还可以通过传入配置对象，自定义底层 `EventEmitter` 实例的行为。例如：

```ts
EventEmitterModule.forRoot({
  // 是否启用通配符支持（如 user.*）
  wildcard: false,
  // 事件命名空间的分隔符
  delimiter: '.',
  // 启用后可监听 newListener 事件
  newListener: false,
  // 启用后可监听 removeListener 事件
  removeListener: false,
  // 每个事件允许注册的最大监听器数量
  maxListeners: 10,
  // 超出监听器限制时，在内存泄漏警告中显示事件名称
  verboseMemoryLeak: false,
  // 当 error 事件没有监听器时，是否抑制抛出异常
  ignoreErrors: false,
})
```

这些配置参数直接传递给 eventemitter2，用于微调其事件发射与监听行为。

## 事件派发

要在应用中派发（触发）事件，需要通过依赖注入的方式，将 `EventEmitter2` 注入到类的构造函数中：

```ts
import { EventEmitter2 } from '@nestjs/event-emitter'

constructor(private eventEmitter: EventEmitter2) {}
```

然后，你就可以在类的方法中调用 `.emit()` 方法来派发事件。例如：

```ts
this.eventEmitter.emit(
  'order.created',
  new OrderCreatedEvent({
    orderId: 1,
    payload: {},
  })
)
```

在这个例子中，我们向事件通道 `order.created` 派发了一个 `OrderCreatedEvent` 实例。所有订阅该事件的监听器都会收到这条事件通知，并以此为基础执行相应逻辑。

## 事件监听

要声明事件监听器（event listener），只需在方法前使用 `@OnEvent()` 装饰器。例如：

```ts
@OnEvent('order.created')
handleOrderCreatedEvent(payload: OrderCreatedEvent) {
  // 处理并响应 "OrderCreatedEvent" 事件
}
```

<CalloutInfo type="warning">
  事件订阅器（event subscriber）**不能**设为请求作用域（request-scoped）。
</CalloutInfo>

`@OnEvent()` 的第一个参数用于指定要监听的事件，可以是 `string` 或 `symbol`，也可以在启用通配符支持时传入 `string | symbol | Array<string | symbol>`。

第二个参数是可选的监听器选项对象，类型如下：

```ts
export type OnEventOptions = OnOptions & {
  /**
   * 是否将该监听器插入到监听队列的前面（而非末尾）。
   *
   * @default false
   */
  prependListener?: boolean

  /**
   * 是否抑制事件处理过程中的异常抛出。
   *
   * @default true
   */
  suppressErrors?: boolean
}
```

<CalloutInfo>
  关于 `OnOptions` 类型的更多细节，请参考 [`eventemitter2`
  官方文档](https://github.com/EventEmitter2/EventEmitter2#emitteronevent-listener-options-objectboolean)。
</CalloutInfo>

例如，下面的监听器会异步处理事件：

```ts
@OnEvent('order.created', { async: true })
handleOrderCreatedEvent(payload: OrderCreatedEvent) {
  // 异步处理 "OrderCreatedEvent" 事件
}
```

### 命名空间与通配符支持

如需使用命名空间或通配符监听功能，需要在调用 `EventEmitterModule.forRoot()` 时启用 `wildcard` 选项。

启用后，事件名称可以使用分隔符（默认为 `.`）表示层级结构，例如 `order.created`，也可以使用数组形式如 `['order', 'created']`。分隔符可以通过配置项 `delimiter` 自定义。

使用通配符订阅事件示例如下：

```ts
@OnEvent('order.*')
handleOrderEvents(payload: OrderCreatedEvent | OrderRemovedEvent | OrderUpdatedEvent) {
  // 处理所有 "order" 命名空间下的事件
}
```

请注意，单个 `*` 仅匹配一级事件名称，例如：

- ✅ `order.*` 可匹配 `order.created`、`order.shipped`。
- ❌ 无法匹配 `order.delayed.out_of_stock`。

如需监听多级事件，请使用多级通配符 `**`，其语义与 `glob` 模式相似：

```ts
@OnEvent('**')
handleEverything(payload: any) {
  // 捕获所有事件
}
```

<CalloutInfo>
  `EventEmitter2` 还提供了诸如 `waitFor()`、`onAny()` 等实用方法，详见
  [官方文档](https://github.com/EventEmitter2/EventEmitter2)。
</CalloutInfo>

## 避免事件丢失

如果你在 `onApplicationBootstrap` 生命周期钩子之前或期间触发事件（例如在模块的构造函数或 `onModuleInit` 方法中），可能会遇到事件被遗漏的情况。这是因为此时 `EventSubscribersLoader` 可能尚未完成所有事件监听器的注册工作。

为避免这种问题，建议在模块的 `onApplicationBootstrap` 钩子中使用 `EventEmitterReadinessWatcher` 提供的 `waitUntilReady()` 方法。该方法会返回一个 `Promise`，并在**所有监听器完成注册之后**才会 `resolve`，从而确保接下来的事件都能被正确监听。

```ts
await this.eventEmitterReadinessWatcher.waitUntilReady()

// 等待监听器就绪后再触发事件
this.eventEmitter.emit(
  'order.created',
  new OrderCreatedEvent({ orderId: 1, payload: {} })
)
```

<CalloutInfo>
  仅当你**必须在 `onApplicationBootstrap` 钩子完成前**触发事件时，才需要使用
  `waitUntilReady()` 进行处理。
</CalloutInfo>

## 示例

查看完整示例代码：[GitHub 示例仓库](https://github.com/nestjs/nest/tree/master/sample/30-event-emitter)。
