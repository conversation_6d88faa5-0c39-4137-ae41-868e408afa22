# Cookie

**HTTP Cookie** 是一种由浏览器存储的小型数据片段，通常用于在客户端与服务端之间维持会话状态。当用户再次访问网站时，浏览器会自动将 Cookie 随请求一起发送给服务器，从而实现状态记忆。

## 在 Express（默认）中使用

NestJS 默认使用 Express，因此可直接集成 [cookie-parser](https://github.com/expressjs/cookie-parser) 中间件来处理 Cookie。首先，安装相关依赖：

```bash
npm install cookie-parser
npm install -D @types/cookie-parser # 如果使用 TypeScript
```

安装完成后，可在应用入口文件（通常是 `main.ts`）中将其注册为全局中间件：

```ts filename='main.ts'
import * as cookieParser from 'cookie-parser'

app.use(cookieParser())
```

`cookie-parser()` 接收两个可选参数：

- `secret`：用于签名 Cookie 的密钥，可以是字符串或字符串数组。如果不传该参数，则不会解析已签名的 Cookie。若传入数组，会尝试使用每个密钥依次解签。
- `options`：会作为第二个参数传递给底层的 `cookie.parse()` 方法。具体配置可参考 cookie 模块的文档。

中间件会自动解析请求头中的 Cookie 字段，并将解析结果挂载到 `req.cookies` 对象上。若设置了密钥，还会将已签名的 Cookie 解析到 `req.signedCookies` 中。

所谓「已签名的 Cookie」，是指值以 `s`: 前缀开头的 Cookie。中间件会自动验证这些值的签名：

- 若验证通过，对应键值会出现在 `req.signedCookies` 中；
- 若验证失败，该值会被设置为 `false`，而不会暴露原始（可能被篡改的）内容；
- 无论是否签名，原始 Cookie 都会保留在 `req.cookies` 中。

### 在控制器中访问 Cookie

一旦中间件配置完成，即可在控制器方法中通过 `Request` 对象读取 Cookie，例如：

```ts
import { Req } from '@nestjs/common'
import { Request } from 'express'

@Get()
findAll(@Req() request: Request) {
  console.log(request.cookies['cookieKey'])
  // 或 console.log(request.signedCookies)
}
```

### 设置 Cookie 到响应中

要在响应中写入 Cookie，可使用 Express 的 `Response#cookie()` 方法。如下所示：

```ts
import { Res } from '@nestjs/common'
import { Response } from 'express'

@Get()
findAll(@Res({ passthrough: true }) response: Response) {
  response.cookie('key', 'value')
}
```

<CalloutInfo type="warning">
  如果希望保留由 Nest 自动处理响应的能力，请务必将 `@Res()` 装饰器的
  `passthrough` 选项设置为
  `true`。更多信息请参考[响应处理方式](/controllers#library-specific-approach)。
</CalloutInfo>

## 在 Fastify 中使用

首先，安装所需的依赖包：

```bash
npm install @fastify/cookie
```

安装完成后，需要在应用初始化阶段注册 `@fastify/cookie` 插件：

```ts filename='main.ts'
import fastifyCookie from '@fastify/cookie'

const app = await NestFactory.create<NestFastifyApplication>(
  AppModule,
  new FastifyAdapter()
)

await app.register(fastifyCookie, {
  secret: 'my-secret', // 用于对 Cookie 进行签名
})
```

配置完成后，即可在控制器中访问客户端发送的 Cookie。例如：

```ts
import { Req } from '@nestjs/common'
import { FastifyRequest } from 'fastify'

@Get()
findAll(@Req() request: FastifyRequest) {
  console.log(request.cookies) // 或 request.cookies['cookieKey']
}
```

如果你需要向客户端设置 Cookie，可以通过 `FastifyReply#setCookie() `方法实现：

```ts
import { Res } from '@nestjs/common'
import { FastifyReply } from 'fastify'

@Get()
findAll(@Res({ passthrough: true }) response: FastifyReply) {
  response.setCookie('key', 'value')
}
```

如需了解 `setCookie()` 方法的更多选项和用法，建议查阅[官方文档](https://github.com/fastify/fastify-cookie#sending)。

<CalloutInfo type="warning">
  如果希望框架继续接管响应处理流程，必须将 `@Res()` 装饰器的 `passthrough`
  选项设为
  `true`，如上述示例所示。更多说明请参考[控制器章节](/controllers#library-specific-approach)。
</CalloutInfo>

## 创建通用的自定义装饰器（跨平台适用）

为了更方便地在控制器中访问请求携带的 Cookie 信息，我们可以定义一个[自定义装饰器](/custom-decorators)，实现声明式地提取 Cookie 值。

```ts
import { createParamDecorator, ExecutionContext } from '@nestjs/common'

// 自定义装饰器：用于获取请求中的 Cookie
export const Cookies = createParamDecorator(
  (data: string, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest()
    // 若指定了 Cookie 名称，则返回对应的值；否则返回所有 Cookie
    return data ? request.cookies?.[data] : request.cookies
  }
)
```

`@Cookies()` 装饰器的核心逻辑是从 `req.cookies` 中提取信息。
如果你不传入参数，它会返回所有的 Cookie；若传入某个 Cookie 名称，则返回对应的值。最终结果会被注入到装饰的参数上。

有了这个装饰器后，你就可以在控制器方法中像下面这样使用：

```ts
@Get()
findAll(@Cookies('name') name: string) {
  // 这里的 name 参数将自动接收到名为 'name' 的 Cookie 值
}
```
