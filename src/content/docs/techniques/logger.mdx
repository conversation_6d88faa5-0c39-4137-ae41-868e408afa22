# 日志记录器

Nest 内置了一个基于文本的日志记录器（`Logger` 类，位于 `@nestjs/common` 包中），用于在应用启动和运行过程中输出系统日志，例如异常捕获、调试信息等。你可以灵活控制日志系统的行为，常见的用法包括：

- 关闭日志输出
- 指定日志级别（仅显示错误、警告、调试信息等）
- 配置日志格式（如原始文本、JSON、彩色输出等）
- 自定义时间戳格式（如使用 ISO 8601）
- 替换默认日志记录器，或通过继承方式扩展其行为
- 结合依赖注入，提高日志系统在测试与模块化场景下的灵活性

Nest 提供了默认的日志工具，也支持你引入第三方日志库进行增强。例如，性能优异且功能丰富的 [Pino](https://github.com/pinojs/pino) 是当前社区广泛采用的日志解决方案之一，常用于文件输出、日志聚合或远程集中式日志服务的接入。

## 基础配置

在调用 `NestFactory.create()` 创建应用实例时，可以通过配置 `logger` 选项来自定义日志行为。

### 关闭日志：

```ts
const app = await NestFactory.create(AppModule, {
  logger: false,
})
```

### 仅启用部分日志级别：

```ts
const app = await NestFactory.create(AppModule, {
  logger: ['error', 'warn'],
})
```

可选的日志级别包括：`'log'`、`'fatal'`、`'error'`、`'warn'`、`'debug'`、`'verbose'`。你可以按需组合使用。

### 禁用彩色输出：

```ts
const app = await NestFactory.create(AppModule, {
  logger: new ConsoleLogger({
    colors: false,
  }),
})
```

### 设置日志前缀：

```ts
const app = await NestFactory.create(AppModule, {
  logger: new ConsoleLogger({
    prefix: 'MyApp', // 默认为 "Nest"
  }),
})
```

## `ConsoleLogger` 选项详解

以下为 `ConsoleLogger` 支持的所有配置项：

| 选项              | 描述                                                                                                                                                                                                                           | 默认值                                         |
| ----------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ---------------------------------------------- |
| `logLevels`       | 启用的日志级别列表。                                                                                                                                                                                                           | `['log', 'error', 'warn', 'debug', 'verbose']` |
| `timestamp`       | 是否打印每条日志与前一条日志之间的时间间隔（启用 `json` 时无效）。                                                                                                                                                             | `false`                                        |
| `prefix`          | 日志前缀（启用 `json` 时无效）。                                                                                                                                                                                               | `'Nest'`                                       |
| `json`            | 是否以 JSON 格式输出日志。                                                                                                                                                                                                     | `false`                                        |
| `colors`          | 是否启用彩色输出。默认在非 JSON 模式下为 `true`，在 JSON 模式下为 `false`。                                                                                                                                                    | `true`                                         |
| `context`         | 设置日志的上下文标签（可选）。                                                                                                                                                                                                 | `undefined`                                    |
| `compact`         | 是否将日志压缩为单行输出。若为数字，则最多将 n 个属性合并为一行（取决于 `breakLength` 设定）。                                                                                                                                 | `true`                                         |
| `maxArrayLength`  | 控制格式化时数组、集合等可迭代对象显示的最大元素数量。`null` 或 `Infinity` 表示显示所有元素。启用 `json` 且禁用 `colors` 且 `compact` 为 `true` 时无效。                                                                       | `100`                                          |
| `maxStringLength` | 控制日志中字符串的最大显示长度。超过该长度将被截断。启用 `json` 且禁用 `colors` 且 `compact` 为 `true` 时无效。                                                                                                                | `10000`                                        |
| `sorted`          | 是否对对象键进行排序。也可提供自定义排序函数。启用 `json` 且禁用 `colors` 且 `compact` 为 `true` 时无效。                                                                                                                      | `false`                                        |
| `depth`           | 对象格式化的递归深度。用于打印大型嵌套对象时的层级控制。传入 `null` 或 `Infinity` 可遍历全部属性。启用 `json` 且禁用 `colors` 且 `compact` 为 `true` 时无效。                                                                  | `5`                                            |
| `showHidden`      | 是否包含对象中的不可枚举属性、符号属性、WeakMap / WeakSet 内容及自定义原型属性。                                                                                                                                               | `false`                                        |
| `breakLength`     | 当日志内容超过指定长度时，是否拆分为多行显示。设为 `Infinity` 可强制输出为单行。该设置受 `compact` 影响：若 `compact` 为 `true`，默认即为 `Infinity`，否则为 `80`。启用 `json` 且禁用 `colors` 且 `compact` 为 `true` 时无效。 | `Infinity`                                     |

## JSON 日志记录

在现代应用开发中，采用 JSON 格式输出日志有助于提升可观测性，并便于与各类日志管理系统集成。在 NestJS 中，启用 JSON 日志记录非常简单：只需将 `ConsoleLogger` 的 `json` 选项设置为 `true`，然后将该实例作为应用的 `logger` 传入。

```ts
const app = await NestFactory.create(AppModule, {
  logger: new ConsoleLogger({
    json: true,
  }),
})
```

启用后，所有日志将以结构化的 JSON 格式输出，适配日志聚合器、云平台等外部系统。例如，AWS ECS（Elastic Container Service，弹性容器服务）等平台原生支持 JSON 日志格式，可实现如下功能：

- **日志过滤**：支持按日志等级、时间戳、自定义元数据等字段进行精准筛选。
- **搜索与分析**：结合日志查询工具，便于追踪应用行为、识别趋势与异常。

如果你使用 [NestJS Mau](https://mau.nestjs.com)，启用 JSON 日志后，还可在界面中以结构化方式查看日志，更直观地进行调试与性能分析。

<CalloutInfo>
  设置 `json: true` 后，`ConsoleLogger` 会自动禁用文本着色（即将 `colors` 设为
  `false`），确保输出为合法的 JSON 格式，不包含 ANSI
  控制字符。在开发环境中，你也可以手动将 `colors` 设为
  `true`，以便在本地终端中更清晰地阅读带颜色的日志。
</CalloutInfo>

以下是启用 JSON 日志记录后的输出示例（单行展示）：

```json
{
  "level": "log",
  "pid": 19096,
  "timestamp": 1607370779834,
  "message": "Starting Nest application...",
  "context": "NestFactory"
}
```

如需了解更多日志格式示例，可参考此 [Pull Request](https://github.com/nestjs/nest/pull/14121)。

## 在应用中使用日志记录器

通过前文介绍的技术手段，我们可以让 Nest 的系统日志与应用中自定义的事件日志在行为与格式上保持一致，提升整体日志的可读性与一致性。

推荐的实践方式是：在每个服务（Service）中使用 `@nestjs/common` 提供的 `Logger` 类进行日志记录。你可以在构造函数中传入服务名称，作为日志上下文使用。例如：

```ts
import { Logger, Injectable } from '@nestjs/common'

@Injectable()
class MyService {
  private readonly logger = new Logger(MyService.name)

  doSomething() {
    this.logger.log('正在执行某些操作...')
  }
}
```

默认情况下，`Logger` 会在输出中使用方括号标记上下文，如下所示：

```bash
[Nest] 19096   - 12/08/2019, 7:12:59 AM   [NestFactory] Starting Nest application...
```

如果你使用 `app.useLogger()` 注册了自定义日志记录器，Nest 内部将自动使用该自定义实现。这种机制实现了日志逻辑与具体实现的解耦 —— 只需调用一次 `app.useLogger()`，就能轻松替换默认日志行为。

举例来说，若你通过如下方式注入了自定义日志记录器：

```ts
app.useLogger(app.get(MyLogger))
```

之后在任何服务中调用 `this.logger.log()`，实际执行的将是 `MyLogger` 实例中的 `log` 方法。这种方式足以覆盖绝大多数使用场景。

如需实现更高级的自定义功能（如新增方法等），请继续阅读下一节。

## 为日志添加时间戳

如果希望在日志输出中附带时间戳，可在创建 `Logger` 实例时，传入可选配置 `{ timestamp: true }`：

```ts
import { Logger, Injectable } from '@nestjs/common'

@Injectable()
class MyService {
  private readonly logger = new Logger(MyService.name, { timestamp: true })

  doSomething() {
    this.logger.log('开始执行带时间戳的操作')
  }
}
```

生成的日志将包含时间戳信息，输出如下：

```bash
[Nest] 19096   - 04/19/2024, 7:12:59 AM   [MyService] Doing something with timestamp here +5ms
```

注意，日志末尾的 `+5ms` 表示与上一条日志消息之间的时间差。每次输出日志时，系统会自动计算与上一次记录的时间间隔。

## 自定义日志记录器

你可以通过为 `logger` 属性传入一个实现了 `LoggerService` 接口的对象，为 Nest 应用提供自定义日志记录器，用于系统日志输出。

例如，直接使用全局的 JavaScript `console` 对象（其本身已符合 `LoggerService` 接口）：

```ts
const app = await NestFactory.create(AppModule, {
  logger: console,
})
await app.listen(process.env.PORT ?? 3000)
```

实现一个自定义日志记录器也非常简单：只需定义一个类，并实现 `LoggerService` 接口中的各个方法。例如：

```ts
import { LoggerService, Injectable } from '@nestjs/common'

@Injectable()
export class MyLogger implements LoggerService {
  /**
   * 写入 log 级别日志。
   */
  log(message: any, ...optionalParams: any[]) {}

  /**
   * 写入 fatal 级别日志。
   */
  fatal(message: any, ...optionalParams: any[]) {}

  /**
   * 写入 error 级别日志。
   */
  error(message: any, ...optionalParams: any[]) {}

  /**
   * 写入 warn 级别日志。
   */
  warn(message: any, ...optionalParams: any[]) {}

  /**
   * 写入 debug 级别日志。
   */
  debug?(message: any, ...optionalParams: any[]) {}

  /**
   * 写入 verbose 级别日志。
   */
  verbose?(message: any, ...optionalParams: any[]) {}
}
```

然后将该日志记录器实例传入 `NestFactory.create()` 的配置项中：

```ts
const app = await NestFactory.create(AppModule, {
  logger: new MyLogger(),
})
await app.listen(process.env.PORT ?? 3000)
```

这种方式虽然简单直接，但不支持依赖注入，因此不利于在测试中替换或复用该记录器实例。更推荐的方式是使用依赖注入，详见下文的[依赖注入](#依赖注入)部分。

## 扩展内置日志记录器

大多数场景下，你无需从头编写日志记录器。可以通过继承内置的 `ConsoleLogger` 类，重写部分方法来自定义日志行为：

```ts
import { ConsoleLogger } from '@nestjs/common'

export class MyLogger extends ConsoleLogger {
  error(message: any, stack?: string, context?: string) {
    // 添加自定义处理逻辑
    super.error(...arguments)
  }
}
```

你可以在功能模块中使用这个扩展类，具体用法详见下方[应用日志记录](#)。

此外，也可以将扩展后的记录器实例传给 `logger` 配置项，或通过依赖注入机制，让 Nest 使用它来处理系统日志，如[自定义日志记录器](#自定义日志记录器)与[依赖注入](#依赖注入)部分所述。

## 依赖注入

如果你希望实现更灵活、功能更强的日志系统，建议充分利用 Nest 的依赖注入机制。举例来说，你可以将 `ConfigService` 注入自定义日志记录器，以实现配置的动态化，再将该记录器注入到其他控制器或提供者中统一使用。

要使自定义日志记录器支持依赖注入，请实现 `LoggerService` 接口，并将其注册为模块的提供者。典型做法如下：

1. 创建一个 `MyLogger` 类，可以继承内置的 `ConsoleLogger`，也可以完全自定义（参考前文示例）。务必确保其实现了 LoggerService 接口。
2. 创建一个 `LoggerModule` 并在其中注册 `MyLogger`：

```ts
import { Module } from '@nestjs/common'
import { MyLogger } from './my-logger.service'

@Module({
  providers: [MyLogger],
  exports: [MyLogger],
})
export class LoggerModule {}
```

通过以上方式，`MyLogger` 成为模块级提供者，可被其他模块注入使用。由于其处于模块上下文中，自然可以注入其他依赖（如 `ConfigService`）以实现更复杂的日志逻辑。

### 在全局启用自定义日志记录器

如果你希望让 Nest 在应用引导过程及系统级日志中也使用自定义日志记录器，还需要额外配置。

由于 `NestFactory.create()` 是在模块体系之外调用的，此时依赖注入尚未建立，因此必须确保 `LoggerModule` 被某个应用模块导入，这样 Nest 才能正确实例化 `MyLogger` 的单例。

随后，你可以通过以下方式将该单例日志记录器传递给应用：

```ts
const app = await NestFactory.create(AppModule, {
  bufferLogs: true,
})
app.useLogger(app.get(MyLogger))
await app.listen(process.env.PORT ?? 3000)
```

<CalloutInfo>
  此处设置 `bufferLogs: true`，可以确保在 `MyLogger`
  挂载完成之前的日志不会丢失，系统会先行缓冲日志信息，直到应用初始化完成或失败。如果初始化失败，Nest
  会自动回退到默认的 `ConsoleLogger` 输出错误日志。此外，还可以设置
  `autoFlushLogs: false`（默认值为 `true`），以手动控制日志刷新时机（调用
  `Logger.flush()`）。
</CalloutInfo>

这里通过 `app.get(MyLogger)` 获取自定义日志器的单例，这种方式相当于将其从依赖注入容器中「取出」，并交由 Nest 作为全局日志器使用。前提是 `MyLogger` 已被某个模块正确注册。

此外，在控制器、服务或守卫等类中，你也可以像普通依赖一样注入 `MyLogger`，从而确保系统日志与应用日志统一使用同一个记录器实例。

更多细节，请参见：[应用日志记录的用法](#)与[自定义日志记录器的注入](#注入自定义日志记录器)。

## 注入自定义日志记录器

你可以通过扩展 Nest 内置的日志记录器来实现自定义日志功能。以下是一个示例：

我们继承了 `ConsoleLogger` 并为其配置了[瞬态作用域](/fundamentals/injection-scopes)（Transient Scope），确保每个特性模块（feature module）都会获得一个独立的 `MyLogger` 实例。在这个例子中，我们没有重写 `log()`、`warn()` 等方法，但你可以根据实际需求进行拓展。

```ts
import { Injectable, Scope, ConsoleLogger } from '@nestjs/common'

@Injectable({ scope: Scope.TRANSIENT })
export class MyLogger extends ConsoleLogger {
  customLog() {
    this.log('Please feed the cat!')
  }
}
```

接下来，定义一个 `LoggerModule` 并导出该自定义日志记录器：

```ts
import { Module } from '@nestjs/common'
import { MyLogger } from './my-logger.service'

@Module({
  providers: [MyLogger],
  exports: [MyLogger],
})
export class LoggerModule {}
```

然后，在需要日志功能的特性模块中导入 `LoggerModule`，即可使用自定义的 `MyLogger`。由于我们继承自 `ConsoleLogger`，因此可以调用其内置方法，如 `setContext()` 来设置日志上下文：

```ts
import { Injectable } from '@nestjs/common'
import { MyLogger } from './my-logger.service'

@Injectable()
export class CatsService {
  private readonly cats: Cat[] = []

  constructor(private readonly myLogger: MyLogger) {
    // 由于使用了瞬态作用域，每个服务实例都有独立的 MyLogger，不会相互干扰
    this.myLogger.setContext('CatsService')
  }

  findAll(): Cat[] {
    // 使用内置方法记录日志
    this.myLogger.warn('About to return cats!')
    // 调用自定义方法
    this.myLogger.customLog()
    return this.cats
  }
}
```

最后，在 `main.ts` 中，你可以显式地让应用使用该自定义日志记录器的实例。值得注意的是：本例中虽然我们并未重写日志方法，因此设置 `useLogger()` 并非必要。但如果你在日志方法中添加了自定义逻辑，并希望 Nest 核心框架也使用这些定制行为，则必须进行如下配置：

```ts filename='main.ts'
const app = await NestFactory.create(AppModule, {
  bufferLogs: true,
})
app.useLogger(new MyLogger())
await app.listen(process.env.PORT ?? 3000)
```

<CalloutInfo title="可选提示">
  除了使用 `bufferLogs: true` 外，你也可以通过设置 `logger: false` 来临时禁用日志记录器。但需注意：如果设置了 `logger: false`，则在调用 `useLogger()` 之前，所有日志将被静默丢弃，可能会错过关键的初始化错误信息。

如果你不介意启动阶段仍由默认日志器输出一部分信息，也可以省略 `logger: false` 配置。

</CalloutInfo>

## 使用第三方日志记录器

在生产环境中，应用通常需要更强大的日志功能，例如多级过滤、结构化日志、日志轮转或集中化采集等。

Nest 提供的内置日志记录器主要用于框架内部日志以及开发阶段的基础调试。若需要更丰富的日志能力，建议集成成熟的第三方日志库，例如 [Winston](https://github.com/winstonjs/winston)。

Nest 完全兼容标准 Node.js 日志生态，因此你可以像在任何 Node.js 应用中一样，灵活地接入这些日志方案，提升日志系统的可控性与可观测性。
