# 装饰器（Decorators）

OpenAPI 模块提供的所有装饰器均以 `Api` 为前缀，以便与 NestJS 的核心装饰器进行区分，下表汇总了所有已导出的装饰器及其适用层级。

| 装饰器                    | 适用层级      |
| ------------------------- | ------------- |
| `@ApiBasicAuth()`         | 方法 / 控制器 |
| `@ApiBearerAuth()`        | 方法 / 控制器 |
| `@ApiBody()`              | 方法          |
| `@ApiConsumes()`          | 方法 / 控制器 |
| `@ApiCookieAuth()`        | 方法 / 控制器 |
| `@ApiExcludeController()` | 控制器        |
| `@ApiExcludeEndpoint()`   | 方法          |
| `@ApiExtension()`         | 方法          |
| `@ApiExtraModels()`       | 方法 / 控制器 |
| `@ApiHeader()`            | 方法 / 控制器 |
| `@ApiHideProperty()`      | 模型          |
| `@ApiOAuth2()`            | 方法 / 控制器 |
| `@ApiOperation()`         | 方法          |
| `@ApiParam()`             | 方法 / 控制器 |
| `@ApiProduces()`          | 方法 / 控制器 |
| `@ApiSchema()`            | 模型          |
| `@ApiProperty()`          | 模型          |
| `@ApiPropertyOptional()`  | 模型          |
| `@ApiQuery()`             | 方法 / 控制器 |
| `@ApiResponse()`          | 方法 / 控制器 |
| `@ApiSecurity()`          | 方法 / 控制器 |
| `@ApiTags()`              | 方法 / 控制器 |
| `@ApiCallbacks()`         | 方法 / 控制器 |
