# 全局路由前缀

若要为应用中的**所有路由**统一设置前缀，可以调用 `INestApplication` 实例的 `setGlobalPrefix()` 方法。

在 Nest 应用中，你可以通过 `INestApplication` 实例的 `setGlobalPrefix()` 方法，为**所有路由统一设置前缀**：

```ts
const app = await NestFactory.create(AppModule)
app.setGlobalPrefix('v1')
```

这样设置后，应用中的所有路由都会自动加上 `v1/` 前缀，例如原本的 `/users` 路由会变为 `/v1/users`。

## 排除特定路由

如果你希望某些路由不使用全局前缀，可以使用 `exclude` 选项显式排除：

```ts
app.setGlobalPrefix('v1', {
  exclude: [{ path: 'health', method: RequestMethod.GET }],
})
```

你也可以直接传入路径字符串，以排除该路径下的**所有请求方法**：

```ts
app.setGlobalPrefix('v1', { exclude: ['cats'] })
```

<CalloutInfo>
  `exclude` 选项中的 path 支持 `path-to-regexp` 提供的路径参数格式。
  请注意：**不支持使用星号通配符**（`*`），应使用路径参数（如
  `:id`）或命名通配符（如 `*splat`）的形式。
</CalloutInfo>
