# 支持 Nest

Nest 是一个遵循 MIT 协议的开源项目，它的持续演进离不开社区的热情支持。这个框架的诞生并非偶然，而是源自一段漫长而坚定的旅程——无数个深夜的代码敲打、下班后的坚持不懈，以及一个又一个被奉献给开源事业的周末。

Nest 不只是一个工具，更是开发者之间共建共享的成果，它凝聚了社区成员的智慧与热爱，也承载着为开发者打造更愉悦编码体验的初心。

## 为何需要你的支持？

与一些由大型企业驱动的开源项目不同，Nest 没有稳定的公司资金背书。它的成长与前进，完全依赖于社区每一位成员的慷慨支持与积极参与 ❤️。

Nest 的作者 [@KamilMyśliwiec](https://github.com/kamilmysliwiec) 希望能够将全部精力投入到框架的持续开发与生态建设中，致力于提供稳定、现代且开发者友好的解决方案。你的一份支持，不仅能帮助他专注于框架的改进与维护，也能为整个社区带来更丰富的学习资源，如深入的技术博客、实用的教学视频以及最新特性的最佳实践分享。

## 你可以如何支持？

如果你热爱 Nest，或者你的团队/企业正在依赖 Nest 进行生产开发，以下是一些支持方式：

- 在 [OpenCollective](https://opencollective.com/nest) 上成为捐助者或企业赞助者。
- 通过 [PayPal](https://paypal.me/kamilmysliwiec) 发起一次性捐赠。
- 若有定制合作或其它支持意愿，可直接联系作者：[<EMAIL>](mailto:<EMAIL>)。

每一份支持都是对开源精神的守护，也是推动 Nest 向更高质量迈进的重要力量。感谢你与我们一同见证和参与这段旅程！
