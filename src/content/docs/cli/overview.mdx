# 概述

[Nest 命令行工具](https://github.com/nestjs/nest-cli)（Nest CLI） 是官方提供的命令行工具，旨在帮助开发者便捷地初始化、开发和维护 Nest 应用。它支持项目脚手架生成、本地开发环境启动、生产构建打包等多项功能。Nest CLI 融合了框架推荐的架构规范，鼓励开发者构建结构清晰、可维护性高的应用。

## 安装

安装 Nest CLI 有多种方式，关键在于确保操作系统能够正确识别 `nest` 命令。最常见也是本文推荐的方式，是通过 `-g` 选项进行全局安装：

```bash
npm install -g @nestjs/cli
```

全局安装的优点是使用方便，但也有两点需要留意：

1. 你需要手动管理 CLI 的版本，确保其与你的项目兼容。
2. 所有项目将共用同一版本的 CLI，可能在多人协作或多项目开发中带来版本一致性问题。

为了解决这些问题，你也可以使用 [npx](https://github.com/npm/cli/blob/latest/docs/lib/content/commands/npx.md) —— 一个内置于 npm 的工具，可临时拉取并执行特定版本的 CLI，无需全局安装，即可保证每次执行都使用指定版本：

```bash
npx @nestjs/cli@latest
```

<CalloutInfo>
  使用 npx 是一种**更灵活**、**版本可控**的替代方案，尤其适用于 CI/CD 场景或对
  CLI 版本有特定要求的团队开发流程。如需了解更多，建议查阅 npx
  官方文档，或咨询你的 DevOps 团队。
</CalloutInfo>

## 基本工作流程

成功安装 Nest CLI 后，即可在命令行中使用 `nest` 命令。首先，可以运行以下命令查看 CLI 支持的所有命令及其简介：

```bash
nest --help
```

若需查看某个特定命令的详细用法，可使用以下格式。例如，将 `generate` 替换为 `new`、`add` 等命令名：

```bash
nest generate --help
```

### 创建并运行一个新项目

要创建一个全新的 Nest 应用，首先进入你希望创建项目的目录，然后依次执行以下命令：

```bash
# 创建项目
nest new my-nest-project

# 进入项目目录
cd my-nest-project

# 运行应用（在开发模式下）
npm run start:dev
```

此时，打开浏览器访问 `http://localhost:3000`，即可看到新项目已经成功运行。

默认情况下，Nest 会启用热重载功能：每当你修改项目源代码，服务会自动重新编译并刷新，无需手动重启。

<CalloutInfo>
  建议使用 [SWC 构建器](/recipes/swc) 替代默认 TypeScript
  编译器，以获得更快的构建速度 —— 实测可提升高达 10 倍性能。
</CalloutInfo>

## 项目结构

当你执行 `nest new` 命令时，Nest 会自动创建一个新目录，并生成一套基础文件，构建出一份标准化的项目结构。你可以在此结构之上继续开发，并按照本指南的说明逐步添加新组件。我们将这种由 `nest new` 创建的默认结构称为**标准模式**。

除了标准模式，Nest 还支持另一种适用于多项目和共享库管理的架构方式，称为**多包仓库**（Monorepo）模式。

这两种模式在使用 Nest 的大多数功能时基本一致，本指南中的内容同样适用于二者。它们的主要差异体现在**构建流程**以及对[库](/cli/libraries)（Library）的支持方式上。相比之下，多包仓库模式更适合复杂项目场景，因为它能有效简化多项目构建和维护的复杂度。

需要强调的是：你可以随时从标准模式切换到多包仓库模式。因此，在初学阶段，无需过于纠结于模式选择。

不论你选择哪种模式，都可以用于管理多个项目。下表简要对比了两者的关键差异：

| 功能                                              | 标准模式               | 多包仓库模式               |
| ------------------------------------------------- | ---------------------- | -------------------------- |
| 多项目管理方式                                    | 独立项目               | 单一代码仓库（Monorepo）   |
| `node_modules` 和 `package.json` 管理             | 各自维护               | 仓库统一管理               |
| 默认构建工具                                      | tsc                    | Webpack                    |
| 编译配置项                                        | 每个项目独立配置       | 仓库统一配置，可按项目覆盖 |
| 配置文件（如 `eslint.config.mjs`、`.prettierrc`） | 各自独立               | 仓库共享                   |
| `nest build` / `nest start` 命令行为              | 针对当前项目           | 默认针对仓库的**主项目**   |
| 库管理                                            | 手动集成（如通过 npm） | 原生支持（路径映射、打包） |

如果想进一步了解两种模式的具体区别，以便做出更合适的选择，建议阅读[工作空间](/cli/monorepo)与[库管理](/cli/libraries)相关章节。

## CLI 命令语法

所有 `nest` 命令都遵循统一的调用格式：

```bash
nest <commandOrAlias> <requiredArg> [optionalArg] [options]
```

例如：

```bash
nest new my-nest-project --dry-run
```

在这个示例中：

- `new` 是命令名称（或其别名），即 `commandOrAlias`。该命令的别名是 `n`。
- `my-nest-project` 是必需参数（requiredArg）。如果未提供该参数，CLI 会提示你输入。
- `--dry-run` 是一个选项（option），其作用是模拟命令执行但不实际生成文件。它的简写形式为 `-d`。

因此，下面的命令等效于上面的写法：

```bash
nest n my-nest-project -d
```

大多数命令和部分选项都支持别名，你可以运行 `nest new --help` 查看该命令的所有可用参数、选项及其别名，帮助你更清楚地理解命令结构和使用方式。

## 命令总览

你可以通过运行 `nest <command> --help` 来查看每个命令支持的选项。更多详细用法请参考[CLI 命令参考](/cli/usages)章节。

| 命令       | 别名 | 描述                                                   |
| ---------- | ---- | ------------------------------------------------------ |
| `new`      | `n`  | 创建一个基于标准模式结构的全新 Nest 应用。             |
| `generate` | `g`  | 利用原理图（Schematics）自动生成或修改项目文件。       |
| `build`    |      | 编译应用或工作区，将结果输出至指定目录。               |
| `start`    |      | 编译并启动应用（在工作区中，默认运行默认项目）。       |
| `add`      |      | 安装并集成一个已发布的 Nest 库，自动执行其安装原理图。 |
| `info`     | `i`  | 显示当前项目中已安装的 Nest 相关依赖及系统信息。       |

## 环境要求

Nest CLI 依赖于 Node.js 的[国际化](https://nodejs.org/api/intl.html)（ICU）支持模块。建议直接从 Node.js 官方网站下载并安装官方提供的二进制版本，以确保兼容性。

你可以通过以下命令检查当前 Node.js 是否启用了 ICU 支持：

```bash
node -p process.versions.icu
```

如果输出为 `undefined`，说明当前 Node.js 环境未启用国际化模块，可能会导致 Nest CLI 运行异常。
