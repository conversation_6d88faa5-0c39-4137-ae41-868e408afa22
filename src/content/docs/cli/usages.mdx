# CLI 命令参考

## `nest new`

用于创建一个全新的 Nest 应用项目（标准模式）。

```bash
nest new <name> [options]
nest n <name> [options]
```

### 描述

该命令会创建并初始化一个新的 Nest 项目，执行过程中会引导你选择一个包管理器，并完成以下操作：

- 以指定的 `<name>` 创建项目根目录。
- 在该目录下生成基础配置文件。
- 创建源码目录 `/src` 和端到端测试目录 `/test`。
- 在上述目录中生成应用的核心结构与测试用的样板文件。

该命令旨在快速构建具有良好默认结构的 Nest 项目，适合大多数标准开发场景。

### 参数说明

| 参数     | 描述               |
| -------- | ------------------ |
| `<name>` | 要创建的项目名称。 |

### 可选参数

| 选项                                  | 别名 | 描述                                                                                                                                                                            |
| ------------------------------------- | ---- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `--dry-run`                           | `-d` | 模拟执行，输出即将发生的变更但不实际写入文件系统。                                                                                                                              |
| `--skip-git`                          | `-g` | 跳过初始化 Git 仓库的步骤。                                                                                                                                                     |
| `--skip-install`                      | `-s` | 跳过依赖包的自动安装。                                                                                                                                                          |
| `--package-manager [package-manager]` | `-p` | 指定包管理器，支持 npm、yarn 或 pnpm。请确保所选工具已全局安装。                                                                                                                |
| `--language [language]`               | `-l` | 指定使用的编程语言，可选值为 TS（TypeScript）或 JS（JavaScript）。                                                                                                              |
| `--collection [collectionName]`       | `-c` | 指定使用的 schematics 集合，通常为某个已安装的 npm 包名。                                                                                                                       |
| `--strict`                            |      | 启用 TypeScript 的严格模式，包含以下编译选项：`strictNullChecks`、`noImplicitAny`、`strictBindCallApply`、`forceConsistentCasingInFileNames`、`noFallthroughCasesInSwitch` 等。 |

## `nest generate`

通过 Schematics 模板自动生成或修改文件结构。

```bash
nest generate <schematic> <name> [options]
nest g <schematic> <name> [options]
```

### 命令参数

| 参数          | 描述                                                                |
| ------------- | ------------------------------------------------------------------- |
| `<schematic>` | 要使用的原型名称，支持 `schematic` 或 `collection:schematic` 格式。 |
| `<name>`      | 要生成资源的名称。                                                  |

### 可用原型（Schematics）

Nest CLI 提供了一系列原型命令，用于快速生成常用的架构组件。下表列出了常用原型的名称、别名以及功能说明：

| 原型名称      | 别名  | 功能说明                                                                                                 |
| ------------- | ----- | -------------------------------------------------------------------------------------------------------- |
| `app`         |       | 创建一个新的应用程序模块。如果当前项目不是 Monorepo 模式，CLI 会自动将其转换为 Monorepo 结构。           |
| `library`     | `lib` | 创建一个新的共享库模块。同样会在非 Monorepo 模式下自动进行转换。                                         |
| `class`       | `cl`  | 生成一个通用的类文件。可用于工具类、常量类等。                                                           |
| `controller`  | `co`  | 生成一个控制器，用于处理路由请求。                                                                       |
| `decorator`   | `d`   | 生成一个自定义装饰器。                                                                                   |
| `filter`      | `f`   | 生成一个异常过滤器，用于处理全局或局部的异常捕获逻辑。                                                   |
| `gateway`     | `ga`  | 生成一个 WebSocket 网关。                                                                                |
| `guard`       | `gu`  | 生成一个守卫，用于路由访问控制。                                                                         |
| `interface`   | `itf` | 生成一个 TypeScript 接口。                                                                               |
| `interceptor` | `itc` | 生成一个拦截器，可用于修改请求或响应流程。                                                               |
| `middleware`  | `mi`  | 生成一个中间件，可用于请求前的预处理逻辑。                                                               |
| `module`      | `mo`  | 生成一个模块，用于组织相关组件。                                                                         |
| `pipe`        | `pi`  | 生成一个管道，用于处理参数转换或验证。                                                                   |
| `provider`    | `pr`  | 生成一个提供者，用于注入服务或其他依赖。                                                                 |
| `resolver`    | `r`   | 生成一个 GraphQL 解析器。                                                                                |
| `resource`    | `res` | 快速生成一个包含 CRUD 操作的资源模块。详见 [CRUD 生成器](/recipes/crud-generator)（仅支持 TypeScript）。 |
| `service`     | `s`   | 生成一个服务，通常用于封装业务逻辑。                                                                     |

### 可选参数

| 选项                            | 别名 | 描述                                                               |
| ------------------------------- | ---- | ------------------------------------------------------------------ |
| `--dry-run` / `-d`              |      | 以「模拟模式」运行命令，仅展示将会执行的变更，不实际修改文件系统。 |
| `--project [project]`           | `-p` | 指定文件应生成在哪个项目中，适用于多项目工作区。                   |
| `--flat`                        |      | 在当前目录下直接生成文件，而不额外创建子目录。                     |
| `--collection [collectionName]` | `-c` | 指定使用的 Schematics 集合，值为一个已安装的 npm 包名。            |
| `--spec`                        |      | 默认启用。为生成的文件创建对应的测试文件（`.spec.ts`）。           |
| `--no-spec`                     |      | 禁用测试文件的自动生成。                                           |

## `nest build`

用于将应用或工作区项目编译至指定的输出目录。

除了基本的编译功能，`build` 命令还具备以下特性：

- 当使用路径别名时，自动根据 `tsconfig-paths` 对模块路径进行重映射。
- 如果启用了 `@nestjs/swagger` CLI 插件，会自动为 DTO 添加 OpenAPI 装饰器。
- 如果启用了 `@nestjs/graphql` CLI 插件，会自动为 DTO 添加 GraphQL 装饰器。

```bash
nest build <name> [options]
```

### 参数说明

| 参数     | 描述               |
| -------- | ------------------ |
| `<name>` | 要构建的项目名称。 |

### 可选参数

| 选项                    | 别名 | 描述                                                                                                                                  |
| ----------------------- | ---- | ------------------------------------------------------------------------------------------------------------------------------------- |
| `--path [path]`         | `-p` | 指定 `tsconfig.json` 的路径。                                                                                                         |
| `--config [path]`       | `-c` | 指定 CLI 配置文件（`nest-cli.json`）的路径。                                                                                          |
| `--watch`               | `-w` | 启用监听模式（Live Reload）。当使用 tsc 编译器时，若在 `nest-cli.json` 中配置了 `manualRestart: true`，可通过终端输入 `rs` 手动重启。 |
|                         |
| `--builder [name]`      | `-b` | 指定编译器，支持 `tsc`、`swc` 或 `webpack`。                                                                                          |
| `--webpack`             |      | 使用 webpack 编译（已废弃，推荐改用 `--builder webpack`）。                                                                           |
| `--webpackPath`         |      | 指定 webpack 配置文件的路径。                                                                                                         |
| `--tsc`                 |      | 强制使用 tsc 编译器。                                                                                                                 |
| `--watchAssets`         |      | 在监听模式下，同时监控非 TypeScript 资源（如 .graphql 文件）。详细说明见：[多包仓库中的资源文件管理](/cli/monorepo#资源文件管理)。    |
| `--type-check`          |      | 使用 swc 编译器时启用类型检查。                                                                                                       |
| `--all`                 |      | 构建当前工作区下的所有项目（适用于 Monorepo 场景）。                                                                                  |
| `--preserveWatchOutput` |      | 使用 tsc 的监听模式时，保留终端中的上一次编译输出（不会清屏）。                                                                       |

## `nest start`

用于**编译并启动应用程序**，默认情况下会运行工作空间中的主项目。

```bash
nest start <name> [options]
```

### 参数说明

| 参数     | 描述                                               |
| -------- | -------------------------------------------------- |
| `<name>` | 指定要运行的项目名称。如果省略，则默认运行主项目。 |

### 可选参数

| 选项                    | 别名 | 描述                                                                                                                     |
| ----------------------- | ---- | ------------------------------------------------------------------------------------------------------------------------ |
| `--path [path]`         | `-p` | 指定 `tsconfig.json` 文件的路径。                                                                                        |
| `--config [path]`       | `-c` | 指定 CLI 配置文件（`nest-cli.json`）的路径。                                                                             |
| `--watch`               | `-w` | 启用监听模式（live reload），源文件变更后自动重新编译并重启应用。                                                        |
| `--builder [name]`      | `-b` | 指定构建器，可选值包括 `tsc`、`swc` 和 `webpack`。                                                                       |
| `--preserveWatchOutput` |      | 使用 tsc 构建器时，保留每次编译的控制台输出（避免清屏）。                                                                |
| `--watchAssets`         |      | 监听并同步非 TypeScript 文件（如静态资源）。适用于监听模式，详见[多包仓库中的资源文件管理](/cli/monorepo#资源文件管理)。 |
| `--debug [hostport]`    | `-d` | 以调试模式运行应用，相当于传入 Node.js 的 `--inspect` 参数。                                                             |
| `--webpack`             |      | 使用 webpack 作为构建器。（已废弃，建议使用 `--builder webpack` 替代）                                                   |
| `--webpackPath`         |      | 指定 webpack 配置文件的路径。                                                                                            |
| `--tsc`                 |      | 强制使用 tsc 编译器进行构建。                                                                                            |
| `--exec [binary]`       | `-e` | 指定用于执行编译后产物的二进制程序，默认使用 node。                                                                      |
| `--no-shell`            |      | 启动子进程时不通过 shell，行为类似于 Node.js 的 `child_process.spawn()` 方法。                                           |
| `--env-file`            |      | 从指定的文件中加载环境变量，内容会注入至 `process.env` 中供应用使用。                                                    |
| `-- [key=value]`        |      | 向应用传入额外的命令行参数，可在应用内部通过 `process.argv` 读取。                                                       |

## `nest add`

用于添加一个已封装为 **Nest 库**（library）的 npm 包，并自动执行其安装原型（schematic），以便完成必要的配置工作。

```bash
nest add <name> [options]
```

### 参数说明

| 参数     | 描述                      |
| -------- | ------------------------- |
| `<name>` | 要添加的库的 npm 包名称。 |

## `nest info`

用于展示当前 Nest 项目的环境信息，包括已安装的 Nest 相关依赖版本、Node.js 版本等。

```bash
nest info
```

```bash
 _   _             _      ___  _____  _____  _     _____
| \ | |           | |    |_  |/  ___|/  __ \| |   |_   _|
|  \| |  ___  ___ | |_     | |\ `--. | /  \/| |     | |
| . ` | / _ \/ __|| __|    | | `--. \| |    | |     | |
| |\  ||  __/\__ \| |_ /\__/ //\__/ /| \__/\| |_____| |_
\_| \_/ \___||___/ \__|\____/ \____/  \____/\_____/\___/

[系统信息]
操作系统版本：macOS High Sierra
NodeJS 版本：v20.18.0
[Nest 信息]
microservices 版本：10.0.0
websockets 版本：10.0.0
testing 版本：10.0.0
common 版本：10.0.0
core 版本：10.0.0
```
