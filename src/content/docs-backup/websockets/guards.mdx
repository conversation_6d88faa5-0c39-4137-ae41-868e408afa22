### 守卫（Guard）

WebSocket 守卫（Guard）与[常规 HTTP 应用守卫](/guards)在本质上没有区别。唯一的不同在于，这里不应抛出 `HttpException`，而是应该使用 `WsException`。

> info **提示** `WsException` 类由 `@nestjs/websockets` 包提供。

#### 绑定守卫（Guard）

下例演示了方法作用域的守卫用法。与基于 HTTP 的应用类似，你也可以使用网关作用域的守卫（即在网关类前添加 `@UseGuards()` 装饰器）。

```typescript
@@filename()
@UseGuards(AuthGuard)
@SubscribeMessage('events')
handleEvent(client: Client, data: unknown): WsResponse<unknown> {
  const event = 'events';
  return { event, data };
}
@@switch
@UseGuards(AuthGuard)
@SubscribeMessage('events')
handleEvent(client, data) {
  const event = 'events';
  return { event, data };
}
```
