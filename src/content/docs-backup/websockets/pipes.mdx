### 管道（Pipes）

[常规管道](/pipes)与 WebSocket 管道（pipes）在本质上没有区别。唯一的不同在于，抛出异常时不应使用 `HttpException`，而应使用 `WsException`。此外，所有管道只会应用于 `data` 参数（因为对 `client` 实例进行校验或转换没有意义）。

> info **提示** `WsException` 类由 `@nestjs/websockets` 包导出。

#### 绑定管道（Binding pipes）

下面的示例演示了如何手动实例化一个方法作用域的管道。与基于 HTTP 的应用程序类似，你也可以使用网关作用域的管道（即在网关类前加上 `@UsePipes()` 装饰器）。

```typescript
@@filename()
@UsePipes(new ValidationPipe({ exceptionFactory: (errors) => new WsException(errors) }))
@SubscribeMessage('events')
handleEvent(client: Client, data: unknown): WsResponse<unknown> {
  const event = 'events';
  return { event, data };
}
@@switch
@UsePipes(new ValidationPipe({ exceptionFactory: (errors) => new WsException(errors) }))
@SubscribeMessage('events')
handleEvent(client, data) {
  const event = 'events';
  return { event, data };
}
```
