### 独立应用（Standalone applications）

在 Nest 中有多种挂载应用的方式。你可以创建一个 Web 应用、一个微服务（Microservices），或者仅仅是一个纯粹的 Nest **独立应用（Standalone application）**（即没有任何网络监听器）。Nest 独立应用是对 Nest **IoC 容器（IoC container）** 的封装，该容器持有所有已实例化的类。我们可以通过独立应用对象，直接在任何已导入的模块（Module）中获取任何已存在实例的引用。因此，你可以在任何地方利用 Nest 框架的能力，比如脚本化的 **CRON 任务**，甚至可以基于它构建自己的 **命令行工具（CLI）**。

#### 快速开始

要创建一个 Nest 独立应用，请使用如下方式：

```typescript
@@filename()
async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule);
  // 你的应用逻辑 ...
}
bootstrap();
```

#### 从静态模块获取提供者

独立应用对象允许你获取在 Nest 应用中注册的任何实例的引用。假设我们在 `AppModule` 模块中导入了一个 `TasksModule` 模块，并且其中有一个 `TasksService` 提供者。这个类提供了一组我们希望在 CRON 任务中调用的方法。

```typescript
@@filename()
const tasksService = app.get(TasksService);
```

要访问 `TasksService` 实例，我们使用 `get()` 方法。`get()` 方法类似于一次**查询**，会在每个已注册模块中查找对应的实例。你可以传递任何提供者的令牌（token）给它。或者，为了进行严格的上下文检查，可以传递一个带有 `strict: true` 属性的选项对象。启用该选项后，你需要手动遍历特定模块，才能从选定的上下文中获取特定实例。

```typescript
@@filename()
const tasksService = app.select(TasksModule).get(TasksService, { strict: true });
```

下面是独立应用对象可用的实例引用获取方法的简要说明：

<table>
  <tbody>
    <tr>
      <td>
        <code>get()</code>
      </td>
      <td>获取应用上下文中可用的控制器或提供者（包括守卫、过滤器等）的实例。</td>
    </tr>
    <tr>
      <td>
        <code>select()</code>
      </td>
      <td>在模块图中导航，提取选定模块的特定实例（通常与严格模式一起使用，如上所述）。</td>
    </tr>
  </tbody>
</table>

> info **提示** 在非严格模式下，默认会选择根模块。若需选择其他模块，你需要手动逐步遍历模块图。

请注意，独立应用没有任何网络监听器，因此所有与 HTTP 相关的 Nest 特性（如中间件、拦截器、管道、守卫等）在此上下文中均不可用。

例如，即使你在应用中注册了全局拦截器，然后通过 `app.get()` 方法获取了某个控制器的实例，拦截器也不会被执行。

#### 从动态模块（Dynamic Module）获取提供者

当你在使用[动态模块](/fundamentals/dynamic-modules)时，应该将代表已注册动态模块的对象传递给 `app.select`。例如：

```typescript
@@filename()
export const dynamicConfigModule = ConfigModule.register({ folder: './config' });

@Module({
  imports: [dynamicConfigModule],
})
export class AppModule {}
```

然后你可以这样选择该模块：

```typescript
@@filename()
const configService = app.select(dynamicConfigModule).get(ConfigService, { strict: true });
```

#### 终止阶段

如果你希望 Node 应用在脚本执行完毕后自动关闭（例如用于运行 CRON 任务的脚本），需要在 `bootstrap` 函数的最后调用 `app.close()` 方法，如下所示：

```typescript
@@filename()
async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule);
  // 应用逻辑 ...
  await app.close();
}
bootstrap();
```

如[生命周期事件](/fundamentals/lifecycle-events)章节所述，这将触发生命周期钩子（Lifecycle Hook）。

#### 示例

可用的完整示例见 [这里](https://github.com/nestjs/nest/tree/master/sample/18-context)。
