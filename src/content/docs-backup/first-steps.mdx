import { FileTree } from '~/components/doc/FileTree'

### 快速上手

在本系列文章中，你将学习 Nest 的**核心基础知识**。为了帮助你熟悉 Nest 应用的基本构建模块，我们将通过构建一个基础的 CRUD 应用，涵盖入门阶段常见的功能特性。

#### 语言

我们热爱 [TypeScript](https://www.typescriptlang.org/)，但更重要的是 —— 我们热爱 [Node.js](https://nodejs.org/en/)。因此，Nest 兼容 TypeScript 和纯 JavaScript。Nest 充分利用了最新的语言特性，所以如果你想用原生 JavaScript，需要配合 [Babel](https://babeljs.io/) 编译器。

本教程中的示例主要使用 TypeScript，但你可以随时**切换代码片段**为原生 JavaScript 语法（只需点击每个代码片段右上角的语言切换按钮）。

#### 前置条件

请确保你的操作系统已安装 [Node.js](https://nodejs.org)（版本 >= 20）。

#### 项目初始化

使用 [Nest CLI](/cli/overview) 脚手架工具可以非常简单地创建新项目。确保已安装 [npm](https://www.npmjs.com/)，然后在终端中运行以下命令即可创建一个新的 Nest 项目：

```bash
$ npm i -g @nestjs/cli
$ nest new project-name
```

> info **提示** 若希望创建带有 TypeScript [更严格](https://www.typescriptlang.org/tsconfig#strict)特性的项目，可在 `nest new` 命令中添加 `--strict` 参数。

上述命令会创建 `project-name` 目录，自动安装依赖和部分基础文件，并生成 `src/` 目录及多个核心文件。

<FileTree />

以下是这些核心文件的简要说明：

|                          |                                                               |
| ------------------------ | ------------------------------------------------------------- |
| `app.controller.ts`      | 基础控制器，包含一个路由。                                    |
| `app.controller.spec.ts` | 控制器的单元测试文件。                                        |
| `app.module.ts`          | 应用的根模块。                                                |
| `app.service.ts`         | 基础服务，包含一个方法。                                      |
| `main.ts`                | 应用入口文件，使用核心函数 `NestFactory` 创建 Nest 应用实例。 |

`main.ts` 文件包含一个异步函数，用于 **引导启动（bootstrap）** 我们的应用：

```typescript
@@filename(main)

import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
@@switch
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
```

要创建 Nest 应用实例，我们使用核心类 `NestFactory`。`NestFactory` 提供了一些静态方法用于创建应用实例。`create()` 方法会返回一个应用对象，实现了 `INestApplication` 接口。该对象提供了一系列方法，后续章节会详细介绍。在上面的 `main.ts` 示例中，我们仅启动了 HTTP 监听器，让应用能够接收 HTTP 请求。

通过 Nest CLI 脚手架创建的项目，初始结构鼓励开发者将每个模块放在独立目录中，遵循最佳实践。

> info **提示** 默认情况下，如果应用创建过程中发生错误，程序会以代码 `1` 退出。如果你希望抛出异常而不是直接退出，可以关闭 `abortOnError` 选项（例如：`NestFactory.create(AppModule, { abortOnError: false })`）。

<app-banner-courses></app-banner-courses>

#### 平台

Nest 致力于成为平台无关的框架。平台无关性让开发者能够创建可复用的逻辑模块，并在多种类型的应用中复用。从技术上讲，只要有适配器，Nest 就能与任何 Node HTTP 框架协作。目前，Nest 原生支持两种 HTTP 平台：[express](https://expressjs.com/) 和 [fastify](https://www.fastify.io)。你可以根据需求选择最适合的方案。

|                    |                                                                                                                                                                                    |
| ------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `platform-express` | [Express](https://expressjs.com/) 是知名的极简 Web 框架，经过大量生产环境验证，社区资源丰富。`@nestjs/platform-express` 包为默认选项。大多数用户无需额外配置即可直接使用 Express。 |
| `platform-fastify` | [Fastify](https://www.fastify.io/) 是高性能、低开销的框架，专注于极致效率和速度。如何使用可参考[这里](/techniques/performance)。                                                   |

无论选择哪种平台，都会暴露各自的应用接口，分别为 `NestExpressApplication` 和 `NestFastifyApplication`。

当你在 `NestFactory.create()` 方法中传入类型参数时，如下例所示，`app` 对象会拥有该平台专属的方法。但除非你需要访问底层平台 API，否则**无需**指定类型。

```typescript
const app = await NestFactory.create<NestExpressApplication>(AppModule)
```

#### 运行应用

安装完成后，在终端运行以下命令即可启动应用，监听 HTTP 请求：

```bash
$ npm run start
```

> info **提示** 若想加快开发速度（构建速度提升约 20 倍），可通过在 `start` 脚本中添加 `-b swc` 参数，使用 [SWC 构建器](/recipes/swc)：`npm run start -- -b swc`。

该命令会启动 HTTP 服务器，监听 `src/main.ts` 文件中定义的端口。应用启动后，在浏览器中访问 `http://localhost:3000/`，你将看到 `Hello World!`。

如需监听文件变更并自动重载服务，可运行：

```bash
$ npm run start:dev
```

该命令会自动监控文件变更，实时编译并重启服务器。

#### 代码规范与格式化

[CLI](/cli/overview) 力求为大规模开发提供可靠的工作流。因此，生成的 Nest 项目默认集成了代码**检查（linter）**和**格式化（formatter）**工具，分别为 [eslint](https://eslint.org/) 和 [prettier](https://prettier.io/)。

> info **提示** 不清楚格式化工具和检查工具的区别？可在[这里](https://prettier.io/docs/en/comparison.html)了解。

为确保最大稳定性和可扩展性，我们使用基础 [`eslint`](https://www.npmjs.com/package/eslint) 和 [`prettier`](https://www.npmjs.com/package/prettier) CLI 包。这一配置便于与官方 IDE 插件集成。

对于不依赖 IDE 的无头环境（如持续集成、Git hooks 等），Nest 项目也内置了可直接使用的 `npm` 脚本。

```bash
# 使用 eslint 检查并自动修复
$ npm run lint

# 使用 prettier 格式化代码
$ npm run format
```
