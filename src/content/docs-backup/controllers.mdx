### 控制器（Controller）

控制器负责处理传入的 **请求（request）**，并将 **响应（response）** 发送回客户端。

<figure>
  <img class="illustrative-image" src="/assets/Controllers_1.png" />
</figure>

控制器的作用是为应用程序处理特定的请求。**路由（routing）** 机制决定了每个请求由哪个控制器处理。通常，一个控制器会包含多个路由，每个路由可以执行不同的操作。

要创建一个基础控制器，我们使用类和 **装饰器（decorator）**。装饰器为类添加必要的元数据，使 Nest 能够创建请求与控制器之间的路由映射。

> info **提示** 如果你想快速创建带有内置 [校验（validation）](https://docs.nestjs.com/techniques/validation) 的 CRUD 控制器，可以使用 CLI 的 [CRUD 生成器（CRUD generator）](https://docs.nestjs.com/recipes/crud-generator#crud-generator)：`nest g resource [name]`。

#### 路由（Routing）

在下面的示例中，我们将使用 `@Controller()` 装饰器，它是定义基础控制器所 **必需** 的。我们会为其指定一个可选的路由前缀 `cats`。在 `@Controller()` 装饰器中使用路径前缀，可以帮助我们将相关路由分组，减少重复代码。例如，如果我们希望将所有与猫（cat）实体相关的路由统一在 `/cats` 路径下，只需在 `@Controller()` 装饰器中指定 `cats` 前缀，这样就无需在每个路由中重复该部分路径。

```typescript
@@filename(cats.controller)
import { Controller, Get } from '@nestjs/common';

@Controller('cats')
export class CatsController {
  @Get()
  findAll(): string {
    return 'This action returns all cats';
  }
}
@@switch
import { Controller, Get } from '@nestjs/common';

@Controller('cats')
export class CatsController {
  @Get()
  findAll() {
    return 'This action returns all cats';
  }
}
```

> info **提示** 你可以通过 CLI 执行 `$ nest g controller [name]` 命令来创建控制器。

`@Get()` HTTP 请求方法装饰器放在 `findAll()` 方法前，告诉 Nest 为该方法创建一个特定的 HTTP 路由处理器。这个端点由 HTTP 请求方法（这里是 GET）和路由路径共同决定。那么，路由路径是什么？路由路径由控制器声明的（可选）前缀和方法装饰器中指定的路径拼接而成。由于我们为控制器设置了 `cats` 前缀，并且方法装饰器没有指定额外路径，所以 Nest 会将 `GET /cats` 请求映射到该处理器。

如上所述，路由路径包括控制器的可选路径前缀 **以及** 方法装饰器中指定的路径字符串。例如，控制器前缀为 `cats`，方法装饰器为 `@Get('breed')`，则最终路由为 `GET /cats/breed`。

在上面的示例中，当有 GET 请求到达该端点时，Nest 会将请求路由到用户自定义的 `findAll()` 方法。需要注意的是，这里的方法名完全可以自定义。虽然我们必须声明一个方法来绑定路由，但 Nest 并不会对方法名赋予特殊含义。

该方法会返回 200 状态码以及相应的响应内容（这里是字符串）。为什么会这样？为了解释这一点，我们需要先介绍 Nest 提供的两种 **不同** 响应处理方式：

<table>
  <tbody>
    <tr>
      <td>标准方式（推荐）</td>
      <td>
        使用这种内置方式时，当请求处理器返回 JavaScript 对象或数组时，Nest 会<strong>自动</strong>
        将其序列化为 JSON。当返回 JavaScript 原始类型（如 <code>string</code>、<code>number</code>、
        <code>boolean</code>）时，Nest
        会直接发送该值，而不会尝试序列化。这让响应处理变得非常简单：只需返回值，Nest
        会自动处理剩下的事情。
        <br />
        此外，响应的<strong>状态码</strong>默认总是 200，POST
        请求除外（默认为201）。我们可以通过在处理器级别添加 <code>@HttpCode(...)</code> 装饰器（详见
        <a href="controllers#status-code">状态码</a>）轻松更改这一行为。
      </td>
    </tr>
    <tr>
      <td>库特定方式</td>
      <td>
        我们可以使用特定库（如 Express）的{' '}
        <a href="https://expressjs.com/en/api.html#res" rel="nofollow" target="_blank">
          响应对象
        </a>
        ，通过在方法参数中使用 <code>@Res()</code> 装饰器注入（如{' '}
        <code>findAll(@Res() response)</code>
        ）。采用这种方式，你可以使用该对象提供的原生响应处理方法。例如，在 Express 中，可以通过{' '}
        <code>response.status(200).send()</code> 构造响应。
      </td>
    </tr>
  </tbody>
</table>

> warning **警告** 当处理器使用了 `@Res()` 或 `@Next()` 时，Nest 会检测到你选择了库特定方式。如果两种方式同时使用，标准方式会在该路由下被**自动禁用**，无法正常工作。如果你想同时使用（例如只注入响应对象用于设置 cookie/headers，其余交由框架处理），必须在 `@Res({ passthrough: true })` 装饰器中设置 `passthrough` 选项为 `true`。

<app-banner-devtools></app-banner-devtools>

#### 请求对象（Request object）

处理器通常需要访问客户端的 **请求** 详情。Nest 提供了对底层平台（默认是 Express）[请求对象](https://expressjs.com/en/api.html#req) 的访问方式。你可以通过在处理器参数中使用 `@Req()` 装饰器让 Nest 注入该对象。

```typescript
@@filename(cats.controller)
import { Controller, Get, Req } from '@nestjs/common';
import { Request } from 'express';

@Controller('cats')
export class CatsController {
  @Get()
  findAll(@Req() request: Request): string {
    return 'This action returns all cats';
  }
}
@@switch
import { Controller, Bind, Get, Req } from '@nestjs/common';

@Controller('cats')
export class CatsController {
  @Get()
  @Bind(Req())
  findAll(request) {
    return 'This action returns all cats';
  }
}
```

> info **提示** 如果你想使用 `express` 类型定义（如上例中的 `request: Request`），请确保安装了 `@types/express` 包。

请求对象代表 HTTP 请求，包含查询字符串、参数、HTTP 头、请求体等属性（详细见 [这里](https://expressjs.com/en/api.html#req)）。大多数情况下，你无需手动访问这些属性，而是可以直接使用如 `@Body()`、`@Query()` 等专用装饰器。下表列出了常用装饰器及其对应的平台对象：

<table>
  <tbody>
    <tr>
      <td>
        <code>@Request(), @Req()</code>
      </td>
      <td>
        <code>req</code>
      </td>
    </tr>
    <tr>
      <td>
        <code>@Response(), @Res()</code>
        <span class="table-code-asterisk">*</span>
      </td>
      <td>
        <code>res</code>
      </td>
    </tr>
    <tr>
      <td>
        <code>@Next()</code>
      </td>
      <td>
        <code>next</code>
      </td>
    </tr>
    <tr>
      <td>
        <code>@Session()</code>
      </td>
      <td>
        <code>req.session</code>
      </td>
    </tr>
    <tr>
      <td>
        <code>@Param(key?: string)</code>
      </td>
      <td>
        <code>req.params</code> / <code>req.params[key]</code>
      </td>
    </tr>
    <tr>
      <td>
        <code>@Body(key?: string)</code>
      </td>
      <td>
        <code>req.body</code> / <code>req.body[key]</code>
      </td>
    </tr>
    <tr>
      <td>
        <code>@Query(key?: string)</code>
      </td>
      <td>
        <code>req.query</code> / <code>req.query[key]</code>
      </td>
    </tr>
    <tr>
      <td>
        <code>@Headers(name?: string)</code>
      </td>
      <td>
        <code>req.headers</code> / <code>req.headers[name]</code>
      </td>
    </tr>
    <tr>
      <td>
        <code>@Ip()</code>
      </td>
      <td>
        <code>req.ip</code>
      </td>
    </tr>
    <tr>
      <td>
        <code>@HostParam()</code>
      </td>
      <td>
        <code>req.hosts</code>
      </td>
    </tr>
  </tbody>
</table>

<sup>* </sup>为兼容不同底层 HTTP 平台（如 Express 和 Fastify）的类型定义，Nest 提供了 `@Res()` 和
`@Response()` 装饰器。`@Res()` 只是 `@Response()` 的别名。两者都直接暴露底层平台的原生 `response`
对象接口。使用它们时，建议同时引入底层库的类型定义（如
`@types/express`），以充分利用类型提示。需要注意的是，当你在方法处理器中注入 `@Res()` 或
`@Response()` 时，Nest 会进入**库特定模式**，此时你需要自行负责响应的发送（如调用 `res.json(...)` 或
`res.send(...)`），否则 HTTP 服务会挂起。

> info **提示** 想了解如何自定义装饰器，请参见[本章节](/custom-decorators)。

#### 资源（Resource）

前文我们定义了一个用于获取猫资源的端点（**GET** 路由）。通常我们还需要提供一个用于创建新记录的端点。下面是 **POST** 处理器的示例：

```typescript
@@filename(cats.controller)
import { Controller, Get, Post } from '@nestjs/common';

@Controller('cats')
export class CatsController {
  @Post()
  create(): string {
    return 'This action adds a new cat';
  }

  @Get()
  findAll(): string {
    return 'This action returns all cats';
  }
}
@@switch
import { Controller, Get, Post } from '@nestjs/common';

@Controller('cats')
export class CatsController {
  @Post()
  create() {
    return 'This action adds a new cat';
  }

  @Get()
  findAll() {
    return 'This action returns all cats';
  }
}
```

就是这么简单。Nest 为所有标准 HTTP 方法都提供了装饰器：`@Get()`、`@Post()`、`@Put()`、`@Delete()`、`@Patch()`、`@Options()` 和 `@Head()`。此外，`@All()` 可以定义一个同时处理所有方法的端点。

#### 路由通配符（Route wildcards）

NestJS 也支持基于模式的路由。例如，星号（`*`）可以作为通配符，用于匹配路径末尾的任意字符组合。如下例所示，`findAll()` 方法会匹配所有以 `abcd/` 开头的路由，无论后面跟着多少字符。

```typescript
@Get('abcd/*')
findAll() {
  return 'This route uses a wildcard';
}
```

`'abcd/*'` 路由路径会匹配 `abcd/`、`abcd/123`、`abcd/abc` 等。连字符（`-`）和点（`.`）在字符串路径中会被按字面意义解析。

这种方式在 Express 和 Fastify 中都适用。但在最新版本的 Express（v5）中，路由系统变得更严格。在纯 Express 中，必须使用命名通配符才能使路由生效，例如 `abcd/*splat`，其中 `splat` 只是通配符参数的名称，没有特殊含义，可以任意命名。不过，由于 Nest 提供了 Express 的兼容层，仍然可以直接使用星号（`*`）作为通配符。

当在**路由中间**使用星号时，Express 要求使用命名通配符（例如 `ab{{ '{' }}*splat&#125;cd`），而 Fastify 则完全不支持这种用法。

#### 状态码（Status code）

如前所述，响应的默认 **状态码（status code）** 总是 **200**，POST 请求除外（默认为 **201**）。你可以通过在处理器级别使用 `@HttpCode(...)` 装饰器轻松更改这一行为。

```typescript
@Post()
@HttpCode(204)
create() {
  return 'This action adds a new cat';
}
```

> info **提示** 需要从 `@nestjs/common` 包中引入 `HttpCode`。

有时，状态码并不是固定的，而是取决于多种因素。这种情况下，可以使用库特定的 **响应（response）**（通过 `@Res()` 注入）对象（或在出错时抛出异常）。

#### 响应头（Response headers）

要指定自定义响应头，可以使用 `@Header()` 装饰器，或直接通过库特定响应对象（如 `res.header()`）设置。

```typescript
@Post()
@Header('Cache-Control', 'no-store')
create() {
  return 'This action adds a new cat';
}
```

> info **提示** 需要从 `@nestjs/common` 包中引入 `Header`。

#### 重定向（Redirection）

要将响应重定向到特定 URL，可以使用 `@Redirect()` 装饰器，或直接通过库特定响应对象（如 `res.redirect()`）实现。

`@Redirect()` 接受两个参数，`url` 和 `statusCode`，均为可选。若省略，`statusCode` 默认为 `302`（`Found`）。

```typescript
@Get()
@Redirect('https://nestjs.com', 301)
```

> info **提示** 有时你可能希望动态确定 HTTP 状态码或重定向 URL。此时可以返回一个符合 `HttpRedirectResponse` 接口（来自 `@nestjs/common`）的对象。

返回值会覆盖传递给 `@Redirect()` 装饰器的参数。例如：

```typescript
@Get('docs')
@Redirect('https://docs.nestjs.com', 302)
getDocs(@Query('version') version) {
  if (version && version === '5') {
    return { url: 'https://docs.nestjs.com/v5/' };
  }
}
```

#### 路由参数（Route parameters）

静态路径的路由无法满足需要在请求中携带**动态数据**的场景（如 `GET /cats/1` 获取 id 为 1 的猫）。要定义带参数的路由，可以在路由路径中添加参数 **标记**，以捕获 URL 中的动态值。如下例所示，这些参数可以通过 `@Param()` 装饰器在方法参数中获取。

> info **提示** 带参数的路由应声明在所有静态路径之后，以避免参数化路径拦截原本属于静态路径的流量。

```typescript
@@filename()
@Get(':id')
findOne(@Param() params: any): string {
  console.log(params.id);
  return `This action returns a #${params.id} cat`;
}
@@switch
@Get(':id')
@Bind(Param())
findOne(params) {
  console.log(params.id);
  return `This action returns a #${params.id} cat`;
}
```

`@Param()` 装饰器用于修饰方法参数（如上例中的 `params`），使**路由**参数作为该参数的属性在方法体内可用。你可以通过 `params.id` 访问 `id` 参数。也可以为装饰器传递具体参数名，直接在方法体内引用该参数。

> info **提示** 需要从 `@nestjs/common` 包中引入 `Param`。

```typescript
@@filename()
@Get(':id')
findOne(@Param('id') id: string): string {
  return `This action returns a #${id} cat`;
}
@@switch
@Get(':id')
@Bind(Param('id'))
findOne(id) {
  return `This action returns a #${id} cat`;
}
```

#### 子域名路由（Sub-domain routing）

`@Controller` 装饰器可以通过 `host` 选项，要求传入请求的 HTTP host 必须匹配特定值。

```typescript
@Controller({ host: 'admin.example.com' })
export class AdminController {
  @Get()
  index(): string {
    return 'Admin page'
  }
}
```

> warning **警告** 由于 **Fastify** 不支持嵌套路由，如果你需要使用子域名路由，建议使用默认的 Express 适配器。

与路由 `path` 类似，`hosts` 选项也可以使用参数标记来捕获主机名中的动态值。如下例所示，这些参数可以通过 `@HostParam()` 装饰器在方法参数中获取。

```typescript
@Controller({ host: ':account.example.com' })
export class AccountController {
  @Get()
  getInfo(@HostParam('account') account: string) {
    return account
  }
}
```

#### 状态共享（State sharing）

对于来自其他编程语言的开发者来说，可能会惊讶于在 Nest 中，几乎所有内容都是在请求间共享的。这包括数据库连接池、带有全局状态的单例服务等。需要理解的是，Node.js 并不采用请求/响应多线程无状态模型（Multi-Threaded Stateless Model），即每个请求由独立线程处理。因此，在 Nest 中使用单例实例是完全**安全**的。

当然，在某些特定场景下，可能需要基于请求的控制器生命周期。例如 GraphQL 应用中的每请求缓存、请求追踪或多租户实现。你可以在[这里](/fundamentals/injection-scopes)了解如何控制注入作用域。

#### 异步处理（Asynchronicity）

我们热爱现代 JavaScript，尤其是其对**异步**数据处理的强调。因此，Nest 完全支持 `async` 函数。每个 `async` 函数都必须返回一个 `Promise`，这样 Nest 就能自动解析其最终值。如下例所示：

```typescript
@@filename(cats.controller)
@Get()
async findAll(): Promise<any[]> {
  return [];
}
@@switch
@Get()
async findAll() {
  return [];
}
```

这段代码完全有效。但 Nest 更进一步，允许路由处理器返回 RxJS [可观察流（observable stream）](https://rxjs-dev.firebaseapp.com/guide/observable)。Nest 会自动订阅流，并在流结束时解析最终值。

```typescript
@@filename(cats.controller)
@Get()
findAll(): Observable<any[]> {
  return of([]);
}
@@switch
@Get()
findAll() {
  return of([]);
}
```

两种方式都可以，你可以根据实际需求选择。

#### 请求载荷（Request payloads）

在前面的示例中，POST 路由处理器没有接收任何客户端参数。现在我们通过添加 `@Body()` 装饰器来实现。

在继续之前（如果你使用 TypeScript），需要先定义 **DTO（数据传输对象，Data Transfer Object）** 的结构。DTO 是一个对象，用于指定通过网络传输的数据结构。我们可以用 TypeScript 接口或简单类来定义 DTO，但这里推荐使用**类**。为什么？因为类属于 JavaScript ES6 标准，编译后依然保留为真实实体；而接口在转译时会被移除，Nest 在运行时无法引用。像 **管道（Pipe）** 这样的特性需要在运行时访问变量的元类型，这只有类能做到。

让我们创建 `CreateCatDto` 类：

```typescript
@@filename(create-cat.dto)
export class CreateCatDto {
  name: string;
  age: number;
  breed: string;
}
```

它只包含三个基础属性。之后我们可以在 `CatsController` 中使用新建的 DTO：

```typescript
@@filename(cats.controller)
@Post()
async create(@Body() createCatDto: CreateCatDto) {
  return 'This action adds a new cat';
}
@@switch
@Post()
@Bind(Body())
async create(createCatDto) {
  return 'This action adds a new cat';
}
```

> info **提示** 我们的 `ValidationPipe` 可以过滤掉不应被方法处理器接收的属性。比如在 `CreateCatDto` 示例中，白名单属性为 `name`、`age` 和 `breed`，不在白名单内的属性会被自动剔除。详细内容见[这里](https://docs.nestjs.com/techniques/validation#stripping-properties)。

#### 查询参数（Query parameters）

在路由中处理查询参数时，可以使用 `@Query()` 装饰器从请求中提取。下面通过实际示例演示：

假设我们希望根据查询参数 `age` 和 `breed` 过滤猫列表。首先在 `CatsController` 中定义查询参数：

```typescript
@@filename(cats.controller)
@Get()
async findAll(@Query('age') age: number, @Query('breed') breed: string) {
  return `This action returns all cats filtered by age: ${age} and breed: ${breed}`;
}
```

在本例中，`@Query()` 装饰器用于从查询字符串中提取 `age` 和 `breed` 的值。例如，以下请求：

```plaintext
GET /cats?age=2&breed=Persian
```

会使 `age` 的值为 `2`，`breed` 的值为 `Persian`。

如果你的应用需要处理更复杂的查询参数（如嵌套对象或数组）：

```plaintext
?filter[where][name]=John&filter[where][age]=30
?item[]=1&item[]=2
```

则需要配置你的 HTTP 适配器（Express 或 Fastify）使用合适的查询解析器。在 Express 中，可以使用 `extended` 解析器，支持丰富的查询对象：

```typescript
const app = await NestFactory.create<NestExpressApplication>(AppModule)
app.set('query parser', 'extended')
```

在 Fastify 中，可以使用 `querystringParser` 选项：

```typescript
const app = await NestFactory.create<NestFastifyApplication>(
  AppModule,
  new FastifyAdapter({
    querystringParser: (str) => qs.parse(str),
  })
)
```

> info **提示** `qs` 是一个支持嵌套和数组的查询字符串解析器。可通过 `npm install qs` 安装。

#### 错误处理（Handling errors）

关于错误处理（如异常处理），请参见[专门章节](/exception-filters)。

#### 完整资源示例（Full resource sample）

下面的示例展示了如何使用多种装饰器创建一个基础控制器。该控制器提供了若干方法，用于访问和操作内部数据。

```typescript
@@filename(cats.controller)
import { Controller, Get, Query, Post, Body, Put, Param, Delete } from '@nestjs/common';
import { CreateCatDto, UpdateCatDto, ListAllEntities } from './dto';

@Controller('cats')
export class CatsController {
  @Post()
  create(@Body() createCatDto: CreateCatDto) {
    return 'This action adds a new cat';
  }

  @Get()
  findAll(@Query() query: ListAllEntities) {
    return `This action returns all cats (limit: ${query.limit} items)`;
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return `This action returns a #${id} cat`;
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateCatDto: UpdateCatDto) {
    return `This action updates a #${id} cat`;
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return `This action removes a #${id} cat`;
  }
}
@@switch
import { Controller, Get, Query, Post, Body, Put, Param, Delete, Bind } from '@nestjs/common';

@Controller('cats')
export class CatsController {
  @Post()
  @Bind(Body())
  create(createCatDto) {
    return 'This action adds a new cat';
  }

  @Get()
  @Bind(Query())
  findAll(query) {
    console.log(query);
    return `This action returns all cats (limit: ${query.limit} items)`;
  }

  @Get(':id')
  @Bind(Param('id'))
  findOne(id) {
    return `This action returns a #${id} cat`;
  }

  @Put(':id')
  @Bind(Param('id'), Body())
  update(id, updateCatDto) {
    return `This action updates a #${id} cat`;
  }

  @Delete(':id')
  @Bind(Param('id'))
  remove(id) {
    return `This action removes a #${id} cat`;
  }
}
```

> info **提示** Nest CLI 提供了生成器（schematic），可以自动创建**所有样板代码**，无需手动编写，大大提升开发体验。详细内容见[这里](/recipes/crud-generator)。

#### 快速上手（Getting up and running）

即使 `CatsController` 已经定义完成，Nest 也不会自动识别并创建其实例。

控制器必须始终属于某个模块，因此我们需要在 `@Module()` 装饰器的 `controllers` 数组中注册它。由于目前只定义了根模块 `AppModule`，我们可以直接在其中注册 `CatsController`：

```typescript
@@filename(app.module)
import { Module } from '@nestjs/common';
import { CatsController } from './cats/cats.controller';

@Module({
  controllers: [CatsController],
})
export class AppModule {}
```

我们通过 `@Module()` 装饰器为模块类添加元数据，Nest 就能据此确定需要挂载哪些控制器。

#### 库特定方式（Library-specific approach）

前文介绍了标准的 Nest 响应处理方式。另一种方式是使用库特定的 [响应对象](https://expressjs.com/en/api.html#res)。要注入特定响应对象，可以使用 `@Res()` 装饰器。下面通过重写 `CatsController` 展示两种方式的区别：

```typescript
@@filename()
import { Controller, Get, Post, Res, HttpStatus } from '@nestjs/common';
import { Response } from 'express';

@Controller('cats')
export class CatsController {
  @Post()
  create(@Res() res: Response) {
    res.status(HttpStatus.CREATED).send();
  }

  @Get()
  findAll(@Res() res: Response) {
     res.status(HttpStatus.OK).json([]);
  }
}
@@switch
import { Controller, Get, Post, Bind, Res, Body, HttpStatus } from '@nestjs/common';

@Controller('cats')
export class CatsController {
  @Post()
  @Bind(Res(), Body())
  create(res, createCatDto) {
    res.status(HttpStatus.CREATED).send();
  }

  @Get()
  @Bind(Res())
  findAll(res) {
     res.status(HttpStatus.OK).json([]);
  }
}
```

这种方式虽然可用，并且提供了更大的灵活性（如可操作响应头、访问库特定特性），但应谨慎使用。一般来说，这种方式不够直观，并且有一些缺点。主要问题是代码会变得依赖于底层平台，不同库的响应对象 API 可能不同。此外，测试也会更复杂，需要模拟响应对象等。

另外，采用这种方式会失去与 Nest 标准响应处理相关的特性（如拦截器、`@HttpCode()`、`@Header()` 装饰器等）的兼容性。为了解决这个问题，可以启用 `passthrough` 选项，如下所示：

```typescript
@@filename()
@Get()
findAll(@Res({ passthrough: true }) res: Response) {
  res.status(HttpStatus.OK);
  return [];
}
@@switch
@Get()
@Bind(Res({ passthrough: true }))
findAll(res) {
  res.status(HttpStatus.OK);
  return [];
}
```

采用这种方式，可以在需要时与原生响应对象交互（如根据条件设置 cookie 或 header），其余部分仍交由框架处理。
