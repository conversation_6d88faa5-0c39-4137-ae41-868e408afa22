### 执行上下文（Execution Context）

Nest 提供了若干实用类，帮助开发者轻松编写可在多种应用上下文（如基于 Nest 的 HTTP 服务器、微服务（Microservices）以及 WebSocket 应用上下文）下运行的应用程序。这些工具类能够提供当前执行上下文的信息，使我们能够构建通用的 [守卫](/guards)、[异常过滤器](/exception-filters) 和 [拦截器](/interceptors)，让它们可以适配各种控制器、方法和执行环境。

本章将介绍其中的两个类：`ArgumentsHost` 和 `ExecutionContext`。

#### ArgumentsHost 类

`ArgumentsHost` 类提供了一组方法，用于获取传递给处理器（handler）的参数。它允许我们根据不同的上下文（如 HTTP、RPC（微服务）或 WebSocket）来获取对应的参数。框架会在需要访问参数的场景下，自动提供 `ArgumentsHost` 实例，通常以 `host` 作为参数名。例如，在 [异常过滤器](https://docs.nestjs.com/exception-filters#arguments-host) 的 `catch()` 方法中会接收到一个 `ArgumentsHost` 实例。

`ArgumentsHost` 本质上是对处理器参数的抽象。例如，在 HTTP 服务器应用（使用 `@nestjs/platform-express` 时），`host` 对象封装了 Express 的 `[request, response, next]` 数组，其中 `request` 是请求对象，`response` 是响应对象，`next` 是控制请求-响应流程的函数。而在 [GraphQL](/graphql/quick-start) 应用中，`host` 对象则包含 `[root, args, context, info]` 数组。

#### 当前应用上下文

当我们需要编写可在多种应用上下文下运行的通用 [守卫](/guards)、[异常过滤器](/exception-filters) 和 [拦截器](/interceptors) 时，需要一种方式来判断当前方法所处的应用类型。可以通过 `ArgumentsHost` 的 `getType()` 方法实现：

```typescript
if (host.getType() === 'http') {
  // 仅在常规 HTTP 请求（REST）上下文中执行的逻辑
} else if (host.getType() === 'rpc') {
  // 仅在微服务（Microservice）请求上下文中执行的逻辑
} else if (host.getType<GqlContextType>() === 'graphql') {
  // 仅在 GraphQL 请求上下文中执行的逻辑
}
```

> info **提示** `GqlContextType` 需从 `@nestjs/graphql` 包中导入。

通过获取应用类型，我们可以编写更通用的组件，如下所示。

#### 处理器参数获取

要获取传递给处理器的参数数组，可以使用 host 对象的 `getArgs()` 方法：

```typescript
const [req, res, next] = host.getArgs()
```

你也可以通过 `getArgByIndex()` 方法按索引获取特定参数：

```typescript
const request = host.getArgByIndex(0)
const response = host.getArgByIndex(1)
```

上述示例中，我们通过索引获取了请求和响应对象，但这种方式通常不推荐，因为它会让应用与特定的执行上下文耦合。更推荐的做法是，使用 host 对象的上下文切换工具方法，切换到适合当前应用的上下文。常用的上下文切换方法如下：

```typescript
/**
 * 切换到 RPC 上下文。
 */
switchToRpc(): RpcArgumentsHost;
/**
 * 切换到 HTTP 上下文。
 */
switchToHttp(): HttpArgumentsHost;
/**
 * 切换到 WebSocket 上下文。
 */
switchToWs(): WsArgumentsHost;
```

我们可以用 `switchToHttp()` 方法重写前面的示例。`host.switchToHttp()` 会返回一个适用于 HTTP 应用上下文的 `HttpArgumentsHost` 对象。该对象提供了两个常用方法，方便我们提取所需对象。此处还可以结合 Express 类型断言，获取原生的 Express 类型对象：

```typescript
const ctx = host.switchToHttp()
const request = ctx.getRequest<Request>()
const response = ctx.getResponse<Response>()
```

同理，`WsArgumentsHost` 和 `RpcArgumentsHost` 也分别提供了获取微服务和 WebSocket 上下文对象的方法。以下是 `WsArgumentsHost` 的方法：

```typescript
export interface WsArgumentsHost {
  /**
   * 获取数据对象。
   */
  getData<T>(): T
  /**
   * 获取客户端对象。
   */
  getClient<T>(): T
}
```

`RpcArgumentsHost` 的方法如下：

```typescript
export interface RpcArgumentsHost {
  /**
   * 获取数据对象。
   */
  getData<T>(): T

  /**
   * 获取上下文对象。
   */
  getContext<T>(): T
}
```

#### ExecutionContext 类

`ExecutionContext` 继承自 `ArgumentsHost`，并在此基础上提供了更多关于当前执行流程的详细信息。与 `ArgumentsHost` 类似，Nest 会在你需要的地方自动提供 `ExecutionContext` 实例，比如 [守卫](/guards#execution-context) 的 `canActivate()` 方法和 [拦截器](/interceptors#execution-context) 的 `intercept()` 方法。它提供了如下方法：

```typescript
export interface ExecutionContext extends ArgumentsHost {
  /**
   * 返回当前处理器所属控制器类的类型。
   */
  getClass<T>(): Type<T>
  /**
   * 返回即将在请求管道中被调用的处理器（方法）引用。
   */
  getHandler(): Function
}
```

`getHandler()` 方法返回即将被调用的处理器方法的引用。`getClass()` 方法返回该处理器所属的 `控制器（Controller）` 类的类型。例如，在 HTTP 上下文中，如果当前处理的是一个绑定到 `CatsController` 的 `create()` 方法的 `POST` 请求，`getHandler()` 会返回 `create()` 方法的引用，`getClass()` 会返回 `CatsController` **类**（注意是类而不是实例）。

```typescript
const methodKey = ctx.getHandler().name // "create"
const className = ctx.getClass().name // "CatsController"
```

能够同时访问当前类和处理器方法的引用极大提升了灵活性。最重要的是，这让我们可以在守卫或拦截器中访问通过 `Reflector#createDecorator` 或内置 `@SetMetadata()` 装饰器设置的元数据。我们将在下文介绍相关用法。

<app-banner-enterprise></app-banner-enterprise>

#### 反射与元数据（Reflection and metadata）

Nest 支持通过 `Reflector#createDecorator` 方法自定义装饰器，或通过内置的 `@SetMetadata()` 装饰器，为路由处理器附加**自定义元数据（Metadata）**。本节将对比这两种方式，并演示如何在守卫或拦截器中访问这些元数据。

要使用 `Reflector#createDecorator` 创建强类型装饰器，需要指定类型参数。例如，下面创建了一个接收字符串数组参数的 `Roles` 装饰器：

```ts
@@filename(roles.decorator)
import { Reflector } from '@nestjs/core';

export const Roles = Reflector.createDecorator<string[]>();
```

这里的 `Roles` 装饰器是一个接收 `string[]` 类型参数的函数。

现在，我们可以直接在处理器上使用该装饰器：

```typescript
@@filename(cats.controller)
@Post()
@Roles(['admin'])
async create(@Body() createCatDto: CreateCatDto) {
  this.catsService.create(createCatDto);
}
@@switch
@Post()
@Roles(['admin'])
@Bind(Body())
async create(createCatDto) {
  this.catsService.create(createCatDto);
}
```

如上，我们将 `Roles` 装饰器的元数据附加到了 `create()` 方法上，表示只有拥有 `admin` 角色的用户才能访问该路由。

要访问路由的角色（自定义元数据），我们再次使用 `Reflector` 辅助类。`Reflector` 可以像普通服务一样注入到类中：

```typescript
@@filename(roles.guard)
@Injectable()
export class RolesGuard {
  constructor(private reflector: Reflector) {}
}
@@switch
@Injectable()
@Dependencies(Reflector)
export class CatsService {
  constructor(reflector) {
    this.reflector = reflector;
  }
}
```

> info **提示** `Reflector` 类需从 `@nestjs/core` 包中导入。

现在，可以通过 `get()` 方法读取处理器上的元数据：

```typescript
const roles = this.reflector.get(Roles, context.getHandler())
```

`Reflector#get` 方法允许我们通过传入两个参数（装饰器引用和上下文对象）来获取元数据。在本例中，第一个参数是 `Roles` 装饰器（见上文 `roles.decorator.ts`），第二个参数是 `context.getHandler()`，即当前处理的路由处理器函数引用。

另外，我们也可以在控制器类上应用元数据，使其作用于该控制器下的所有路由：

```typescript
@@filename(cats.controller)
@Roles(['admin'])
@Controller('cats')
export class CatsController {}
@@switch
@Roles(['admin'])
@Controller('cats')
export class CatsController {}
```

在这种情况下，如果要提取控制器上的元数据，只需将 `context.getClass()` 作为第二个参数传递（即以控制器类作为元数据提取的上下文），而不是 `context.getHandler()`：

```typescript
@@filename(roles.guard)
const roles = this.reflector.get(Roles, context.getClass());
```

由于可以在多个层级（如控制器和方法）上设置元数据，有时我们需要同时提取并合并多个上下文的元数据。`Reflector` 类为此提供了两个实用方法，可以一次性提取控制器和方法上的元数据，并以不同方式进行合并。

来看一个同时在控制器和方法上都设置了 `Roles` 元数据的场景：

```typescript
@@filename(cats.controller)
@Roles(['user'])
@Controller('cats')
export class CatsController {
  @Post()
  @Roles(['admin'])
  async create(@Body() createCatDto: CreateCatDto) {
    this.catsService.create(createCatDto);
  }
}
@@switch
@Roles(['user'])
@Controller('cats')
export class CatsController {}
  @Post()
  @Roles(['admin'])
  @Bind(Body())
  async create(createCatDto) {
    this.catsService.create(createCatDto);
  }
}
```

如果你的意图是将 `'user'` 作为默认角色，并在某些方法上进行覆盖，可以使用 `getAllAndOverride()` 方法：

```typescript
const roles = this.reflector.getAllAndOverride(Roles, [context.getHandler(), context.getClass()])
```

在上述代码中，守卫运行在 `create()` 方法上下文时，`roles` 的值将为 `['admin']`。

如果你希望将多个层级的元数据合并（该方法会合并数组和对象），可以使用 `getAllAndMerge()` 方法：

```typescript
const roles = this.reflector.getAllAndMerge(Roles, [context.getHandler(), context.getClass()])
```

此时，`roles` 的值将为 `['user', 'admin']`。

这两个合并方法的第一个参数为元数据键，第二个参数为元数据目标上下文数组（即 `getHandler()` 和/或 `getClass()` 的调用结果）。

#### 低阶用法（Low-level approach）

如前所述，除了使用 `Reflector#createDecorator`，你还可以用内置的 `@SetMetadata()` 装饰器为处理器附加元数据。

```typescript
@@filename(cats.controller)
@Post()
@SetMetadata('roles', ['admin'])
async create(@Body() createCatDto: CreateCatDto) {
  this.catsService.create(createCatDto);
}
@@switch
@Post()
@SetMetadata('roles', ['admin'])
@Bind(Body())
async create(createCatDto) {
  this.catsService.create(createCatDto);
}
```

> info **提示** `@SetMetadata()` 装饰器需从 `@nestjs/common` 包中导入。

如上，我们将 `roles` 元数据（`roles` 为元数据键，`['admin']` 为对应值）附加到了 `create()` 方法上。虽然这样做可以实现功能，但并不推荐直接在路由中使用 `@SetMetadata()`。更好的做法是自定义装饰器，如下所示：

```typescript
@@filename(roles.decorator)
import { SetMetadata } from '@nestjs/common';

export const Roles = (...roles: string[]) => SetMetadata('roles', roles);
@@switch
import { SetMetadata } from '@nestjs/common';

export const Roles = (...roles) => SetMetadata('roles', roles);
```

这种方式更简洁、可读性更高，也更接近 `Reflector#createDecorator` 的用法。不同之处在于，`@SetMetadata` 允许你完全控制元数据的键和值，并且可以创建接收多个参数的装饰器。

有了自定义的 `@Roles()` 装饰器后，我们就可以用它来装饰 `create()` 方法：

```typescript
@@filename(cats.controller)
@Post()
@Roles('admin')
async create(@Body() createCatDto: CreateCatDto) {
  this.catsService.create(createCatDto);
}
@@switch
@Post()
@Roles('admin')
@Bind(Body())
async create(createCatDto) {
  this.catsService.create(createCatDto);
}
```

要访问路由的角色（自定义元数据），我们依然使用 `Reflector` 辅助类：

```typescript
@@filename(roles.guard)
@Injectable()
export class RolesGuard {
  constructor(private reflector: Reflector) {}
}
@@switch
@Injectable()
@Dependencies(Reflector)
export class CatsService {
  constructor(reflector) {
    this.reflector = reflector;
  }
}
```

> info **提示** `Reflector` 类需从 `@nestjs/core` 包中导入。

此时，读取处理器元数据的方法如下：

```typescript
const roles = this.reflector.get<string[]>('roles', context.getHandler())
```

这里我们传入元数据**键**（本例为 `'roles'`）作为第一个参数，其他用法与 `Reflector#createDecorator` 示例一致。
