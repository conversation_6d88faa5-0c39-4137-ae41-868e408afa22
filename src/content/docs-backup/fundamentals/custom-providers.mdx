### 自定义提供者（Custom Provider）

在前面的章节中，我们已经介绍了 **依赖注入（Dependency Injection，DI）** 及其在 Nest 中的应用。例如，[基于构造函数的依赖注入](https://docs.nestjs.com/providers#dependency-injection) 用于将实例（通常是服务提供者）注入到类中。你可能已经注意到，依赖注入机制在 Nest 核心中扮演着至关重要的角色。到目前为止，我们只探索了一种主要模式。随着应用变得更加复杂，你可能需要充分利用 DI 系统的全部特性，下面我们将更详细地进行探讨。

#### DI 基础

依赖注入是一种 [控制反转（Inversion of Control，IoC）](https://en.wikipedia.org/wiki/Inversion_of_control) 技术，即将依赖项的实例化过程交由 IoC 容器（在本例中为 NestJS 运行时系统）管理，而不是在代码中手动创建。让我们通过 [提供者章节](https://docs.nestjs.com/providers) 的示例来分析其工作原理。

首先，我们定义一个提供者。`@Injectable()` 装饰器将 `CatsService` 类标记为可由 Nest IoC 容器管理的提供者。

```typescript
@@filename(cats.service)
import { Injectable } from '@nestjs/common';
import { Cat } from './interfaces/cat.interface';

@Injectable()
export class CatsService {
  private readonly cats: Cat[] = [];

  findAll(): Cat[] {
    return this.cats;
  }
}
@@switch
import { Injectable } from '@nestjs/common';

@Injectable()
export class CatsService {
  constructor() {
    this.cats = [];
  }

  findAll() {
    return this.cats;
  }
}
```

然后，我们请求 Nest 将该提供者注入到控制器类中：

```typescript
@@filename(cats.controller)
import { Controller, Get } from '@nestjs/common';
import { CatsService } from './cats.service';
import { Cat } from './interfaces/cat.interface';

@Controller('cats')
export class CatsController {
  constructor(private catsService: CatsService) {}

  @Get()
  async findAll(): Promise<Cat[]> {
    return this.catsService.findAll();
  }
}
@@switch
import { Controller, Get, Bind, Dependencies } from '@nestjs/common';
import { CatsService } from './cats.service';

@Controller('cats')
@Dependencies(CatsService)
export class CatsController {
  constructor(catsService) {
    this.catsService = catsService;
  }

  @Get()
  async findAll() {
    return this.catsService.findAll();
  }
}
```

最后，我们在 Nest IoC 容器中注册该提供者：

```typescript
@@filename(app.module)
import { Module } from '@nestjs/common';
import { CatsController } from './cats/cats.controller';
import { CatsService } from './cats/cats.service';

@Module({
  controllers: [CatsController],
  providers: [CatsService],
})
export class AppModule {}
```

那么，底层究竟发生了什么？整个过程有三个关键步骤：

1. 在 `cats.service.ts` 中，`@Injectable()` 装饰器声明 `CatsService` 类为可由 Nest IoC 容器管理的类。
2. 在 `cats.controller.ts` 中，`CatsController` 通过构造函数注入声明依赖于 `CatsService` 注入令牌（Injection Token）：

```typescript
  constructor(private catsService: CatsService)
```

3. 在 `app.module.ts` 中，我们将 `CatsService` 注入令牌与 `cats.service.ts` 文件中的 `CatsService` 类关联。稍后我们会在<a href="/fundamentals/custom-providers#standard-providers">下文</a>详细介绍这种关联（也称为 _注册_）。

当 Nest IoC 容器实例化 `CatsController` 时，会首先查找其依赖项\*。当发现依赖 `CatsService` 时，会根据注册步骤（上文第 3 步）查找 `CatsService` 注入令牌，并返回对应的 `CatsService` 类。假设采用默认的 `SINGLETON` 作用域，Nest 会创建 `CatsService` 的实例、缓存并返回它，或者如果已缓存则直接返回现有实例。

\*此处为简化说明，实际依赖分析过程更为复杂，发生在应用引导（bootstrapping）期间。依赖分析（即“创建依赖图”）是**递归传递**的。例如，如果 `CatsService` 本身还有依赖项，也会被自动解析。依赖图确保依赖项按正确顺序解析 —— 本质上是“自底向上”。这一机制让开发者无需手动管理复杂的依赖关系。

<app-banner-courses></app-banner-courses>

#### 标准提供者（Standard Provider）

让我们进一步了解 `@Module()` 装饰器。在 `app.module` 中，我们声明：

```typescript
@Module({
  controllers: [CatsController],
  providers: [CatsService],
})
```

`providers` 属性接收一个提供者数组。到目前为止，我们通过类名列表传递这些提供者。实际上，`providers: [CatsService]` 是更完整语法的简写形式：

```typescript
providers: [
  {
    provide: CatsService,
    useClass: CatsService,
  },
]
```

通过这种显式写法，我们可以更清楚地理解注册过程。这里，我们将 `CatsService` 注入令牌与 `CatsService` 类关联。简写语法只是为了简化最常见的用例，即通过类名请求实例。

#### 自定义提供者（Custom Provider）

如果你的需求超出了 _标准提供者_ 的能力怎么办？例如：

- 希望自定义实例，而不是让 Nest 实例化（或返回缓存的）类
- 希望在第二个依赖项中复用已有类
- 希望为测试覆盖真实类，使用 mock 版本

Nest 允许你通过自定义提供者来满足这些场景。它提供了多种定义自定义提供者的方式。下面我们逐一介绍。

> info **提示** 如果你遇到依赖解析问题，可以设置 `NEST_DEBUG` 环境变量，在启动时获取额外的依赖解析日志。

#### 值提供者：`useValue`

`useValue` 语法适用于注入常量值、将外部库放入 Nest 容器，或用 mock 对象替换真实实现。例如，想在测试时让 Nest 使用 mock 版 `CatsService`：

```typescript
import { CatsService } from './cats.service'

const mockCatsService = {
  /* mock 实现
  ...
  */
}

@Module({
  imports: [CatsModule],
  providers: [
    {
      provide: CatsService,
      useValue: mockCatsService,
    },
  ],
})
export class AppModule {}
```

在此示例中，`CatsService` 注入令牌会解析为 `mockCatsService` mock 对象。`useValue` 需要一个值——这里是一个与 `CatsService` 类接口兼容的字面量对象。由于 TypeScript 的[结构类型](https://www.typescriptlang.org/docs/handbook/type-compatibility.html)，你可以使用任何接口兼容的对象，包括字面量对象或通过 `new` 实例化的类。

#### 非类注入令牌

到目前为止，我们一直用类名作为提供者的注入令牌（即 `provide` 属性的值）。这与[基于构造函数的注入](https://docs.nestjs.com/providers#dependency-injection)模式相匹配，即令牌也是类名。（如需回顾令牌概念，可参考<a href="/fundamentals/custom-providers#di-fundamentals">DI 基础</a>）。有时，我们希望用字符串或 symbol 作为 DI 令牌。例如：

```typescript
import { connection } from './connection'

@Module({
  providers: [
    {
      provide: 'CONNECTION',
      useValue: connection,
    },
  ],
})
export class AppModule {}
```

在此示例中，我们用字符串类型的令牌（`'CONNECTION'`）与已存在的 `connection` 对象关联。

> warning **注意** 除了字符串，还可以用 JavaScript [symbol](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Symbol) 或 TypeScript [enum](https://www.typescriptlang.org/docs/handbook/enums.html) 作为令牌。

我们之前已经看到如何用标准[构造函数注入](https://docs.nestjs.com/providers#dependency-injection)模式注入提供者。该模式**要求**依赖项用类名声明。`'CONNECTION'` 自定义提供者用的是字符串令牌。要注入此类提供者，需要用 `@Inject()` 装饰器。该装饰器接收一个参数——令牌。

```typescript
@@filename()
@Injectable()
export class CatsRepository {
  constructor(@Inject('CONNECTION') connection: Connection) {}
}
@@switch
@Injectable()
@Dependencies('CONNECTION')
export class CatsRepository {
  constructor(connection) {}
}
```

> info **提示** `@Inject()` 装饰器需从 `@nestjs/common` 包导入。

虽然上例直接用字符串 `'CONNECTION'`，但为了代码整洁，建议将令牌定义在单独文件（如 `constants.ts`），像管理 symbol 或 enum 一样集中管理和导入。

#### 类提供者：`useClass`

`useClass` 语法允许你动态决定令牌应解析为哪个类。例如，假设有一个抽象（或默认）`ConfigService` 类。根据当前环境，Nest 可提供不同的配置服务实现。如下：

```typescript
const configServiceProvider = {
  provide: ConfigService,
  useClass:
    process.env.NODE_ENV === 'development' ? DevelopmentConfigService : ProductionConfigService,
}

@Module({
  providers: [configServiceProvider],
})
export class AppModule {}
```

此代码中，我们先用字面量对象定义 `configServiceProvider`，再传给模块装饰器的 `providers` 属性。这只是代码组织方式，功能等同于前文示例。

此外，我们用 `ConfigService` 类名作为令牌。依赖 `ConfigService` 的类会注入所提供的实现类（`DevelopmentConfigService` 或 `ProductionConfigService`），覆盖其他地方声明的默认实现（如用 `@Injectable()` 装饰器声明的 `ConfigService`）。

#### 工厂提供者：`useFactory`

`useFactory` 语法允许**动态**创建提供者。实际提供者由工厂函数返回的值决定。工厂函数可以很简单，也可以很复杂。简单工厂无需依赖其他提供者，复杂工厂则可注入其他依赖。后者可通过以下机制实现：

1. 工厂函数可接收（可选）参数。
2. `inject` 属性（可选）接收一个提供者数组，Nest 会在实例化时解析并按顺序传递给工厂函数。这些依赖项也可标记为可选。如下例所示：

```typescript
@@filename()
const connectionProvider = {
  provide: 'CONNECTION',
  useFactory: (optionsProvider: MyOptionsProvider, optionalProvider?: string) => {
    const options = optionsProvider.get();
    return new DatabaseConnection(options);
  },
  inject: [MyOptionsProvider, { token: 'SomeOptionalProvider', optional: true }],
  //       \______________/             \__________________/
  //        此依赖项为必需                此依赖项可解析为 undefined
};

@Module({
  providers: [
    connectionProvider,
    MyOptionsProvider, // 基于类的提供者
    // { provide: 'SomeOptionalProvider', useValue: 'anything' },
  ],
})
export class AppModule {}
@@switch
const connectionProvider = {
  provide: 'CONNECTION',
  useFactory: (optionsProvider, optionalProvider) => {
    const options = optionsProvider.get();
    return new DatabaseConnection(options);
  },
  inject: [MyOptionsProvider, { token: 'SomeOptionalProvider', optional: true }],
  //       \______________/            \__________________/
  //        此依赖项为必需               此依赖项可解析为 undefined
};

@Module({
  providers: [
    connectionProvider,
    MyOptionsProvider, // 基于类的提供者
    // { provide: 'SomeOptionalProvider', useValue: 'anything' },
  ],
})
export class AppModule {}
```

#### 别名提供者：`useExisting`

`useExisting` 语法允许为已有提供者创建别名。这样可以通过不同令牌访问同一个提供者。如下例，字符串令牌 `'AliasedLoggerService'` 是类令牌 `LoggerService` 的别名。假设有两个依赖项分别依赖 `'AliasedLoggerService'` 和 `LoggerService`，如果都为 `SINGLETON` 作用域，则解析为同一实例。

```typescript
@Injectable()
class LoggerService {
  /* 实现细节 */
}

const loggerAliasProvider = {
  provide: 'AliasedLoggerService',
  useExisting: LoggerService,
}

@Module({
  providers: [LoggerService, loggerAliasProvider],
})
export class AppModule {}
```

#### 非服务类提供者

虽然提供者通常用于提供服务，但不限于此。提供者可以提供**任意**值。例如，根据当前环境提供配置对象数组：

```typescript
const configFactory = {
  provide: 'CONFIG',
  useFactory: () => {
    return process.env.NODE_ENV === 'development' ? devConfig : prodConfig
  },
}

@Module({
  providers: [configFactory],
})
export class AppModule {}
```

#### 导出自定义提供者

与其他提供者一样，自定义提供者的作用域仅限于声明它的模块。若要让其他模块可见，必须导出。可以用其令牌或完整提供者对象导出。

以下示例展示了用令牌导出：

```typescript
@@filename()
const connectionFactory = {
  provide: 'CONNECTION',
  useFactory: (optionsProvider: OptionsProvider) => {
    const options = optionsProvider.get();
    return new DatabaseConnection(options);
  },
  inject: [OptionsProvider],
};

@Module({
  providers: [connectionFactory],
  exports: ['CONNECTION'],
})
export class AppModule {}
@@switch
const connectionFactory = {
  provide: 'CONNECTION',
  useFactory: (optionsProvider) => {
    const options = optionsProvider.get();
    return new DatabaseConnection(options);
  },
  inject: [OptionsProvider],
};

@Module({
  providers: [connectionFactory],
  exports: ['CONNECTION'],
})
export class AppModule {}
```

或者，用完整提供者对象导出：

```typescript
@@filename()
const connectionFactory = {
  provide: 'CONNECTION',
  useFactory: (optionsProvider: OptionsProvider) => {
    const options = optionsProvider.get();
    return new DatabaseConnection(options);
  },
  inject: [OptionsProvider],
};

@Module({
  providers: [connectionFactory],
  exports: [connectionFactory],
})
export class AppModule {}
@@switch
const connectionFactory = {
  provide: 'CONNECTION',
  useFactory: (optionsProvider) => {
    const options = optionsProvider.get();
    return new DatabaseConnection(options);
  },
  inject: [OptionsProvider],
};

@Module({
  providers: [connectionFactory],
  exports: [connectionFactory],
})
export class AppModule {}
```
