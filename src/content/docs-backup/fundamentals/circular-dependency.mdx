### 循环依赖（Circular Dependency）

当两个类相互依赖时，就会出现循环依赖（Circular Dependency）。例如，A 类需要依赖 B 类，而 B 类同样需要依赖 A 类。在 Nest 中，循环依赖可能发生在模块（Module）之间，也可能发生在提供者（Provider）之间。

虽然在开发中应尽量避免循环依赖（Circular Dependency），但有时难以完全规避。针对这种情况，Nest 提供了两种解决提供者（Provider）之间循环依赖的方法。本章将分别介绍：**前向引用（forward referencing）** 技术，以及通过 **ModuleRef 类（ModuleRef）** 从依赖注入（Dependency Injection）容器中获取提供者实例的方式。

我们还将介绍如何解决模块（Module）之间的循环依赖问题。

> warning **警告** 使用 "barrel files"/index.ts 文件进行分组导入时，也可能导致循环依赖。对于模块和提供者类，建议不要使用 barrel 文件。例如，在同一目录下，`cats/cats.controller` 不应通过 `cats` 来间接导入 `cats/cats.service` 文件。更多细节请参见 [此 GitHub 讨论](https://github.com/nestjs/nest/issues/1181#issuecomment-430197191)。

#### 前向引用（forward reference）

**前向引用（forward reference）** 允许 Nest 通过 `forwardRef()` 工具函数引用尚未定义的类。例如，如果 `CatsService` 和 `CommonService` 互相依赖，双方都可以通过 `@Inject()` 和 `forwardRef()` 工具来解决循环依赖。否则，Nest 无法实例化它们，因为缺少必要的元数据。示例代码如下：

```typescript
@@filename(cats.service)
@Injectable()
export class CatsService {
  constructor(
    @Inject(forwardRef(() => CommonService))
    private commonService: CommonService,
  ) {}
}
@@switch
@Injectable()
@Dependencies(forwardRef(() => CommonService))
export class CatsService {
  constructor(commonService) {
    this.commonService = commonService;
  }
}
```

> info **提示** `forwardRef()` 函数需从 `@nestjs/common` 包中导入。

这就完成了关系中的一方。接下来，对 `CommonService` 也做同样处理：

```typescript
@@filename(common.service)
@Injectable()
export class CommonService {
  constructor(
    @Inject(forwardRef(() => CatsService))
    private catsService: CatsService,
  ) {}
}
@@switch
@Injectable()
@Dependencies(forwardRef(() => CatsService))
export class CommonService {
  constructor(catsService) {
    this.catsService = catsService;
  }
}
```

> warning **警告** 实例化顺序是不确定的。请确保你的代码不依赖于哪个构造函数先被调用。如果循环依赖涉及 `Scope.REQUEST` 的提供者，可能会导致依赖未定义。详细信息可参考 [这里](https://github.com/nestjs/nest/issues/5778)。

#### ModuleRef 类替代方案

除了使用 `forwardRef()`，你还可以重构代码，利用 `ModuleRef` 类在循环依赖关系的一方获取提供者实例。关于 `ModuleRef` 工具类的更多内容，请参见[这里](/fundamentals/module-ref)。

#### 模块前向引用（Module forward reference）

为了解决模块（Module）之间的循环依赖，同样可以在双方的模块关联中使用 `forwardRef()` 工具函数。例如：

```typescript
@@filename(common.module)
@Module({
  imports: [forwardRef(() => CatsModule)],
})
export class CommonModule {}
```

这就完成了关系中的一方。接下来，对 `CatsModule` 也做同样处理：

```typescript
@@filename(cats.module)
@Module({
  imports: [forwardRef(() => CommonModule)],
})
export class CatsModule {}
```
