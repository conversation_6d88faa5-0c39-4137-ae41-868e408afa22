### 模块引用（Module Reference）

Nest 提供了 `ModuleRef` 类，用于在内部的提供者（Provider）列表中导航，并通过注入令牌（Injection Token）作为查找键获取任意提供者的引用。`ModuleRef` 类还支持动态实例化静态和作用域提供者（Scoped Provider）。你可以像普通依赖注入一样，将 `ModuleRef` 注入到类中：

```typescript
@@filename(cats.service)
@Injectable()
export class CatsService {
  constructor(private moduleRef: ModuleRef) {}
}
@@switch
@Injectable()
@Dependencies(ModuleRef)
export class CatsService {
  constructor(moduleRef) {
    this.moduleRef = moduleRef;
  }
}
```

> info **提示** `ModuleRef` 类需从 `@nestjs/core` 包中导入。

#### 获取实例

`ModuleRef` 实例（下文简称"模块引用"）拥有 `get()` 方法。默认情况下，该方法会返回在当前模块中注册并已实例化的提供者、控制器（Controller）或可注入对象（如守卫、拦截器等），查找方式为注入令牌或类名。如果未找到实例，则会抛出异常。

```typescript
@@filename(cats.service)
@Injectable()
export class CatsService implements OnModuleInit {
  private service: Service;
  constructor(private moduleRef: ModuleRef) {}

  onModuleInit() {
    this.service = this.moduleRef.get(Service);
  }
}
@@switch
@Injectable()
@Dependencies(ModuleRef)
export class CatsService {
  constructor(moduleRef) {
    this.moduleRef = moduleRef;
  }

  onModuleInit() {
    this.service = this.moduleRef.get(Service);
  }
}
```

> warning **警告** 你无法通过 `get()` 方法获取作用域提供者（如瞬态或请求作用域）。请参考下文<a href="https://docs.nestjs.com/fundamentals/module-ref#resolving-scoped-providers">作用域提供者解析</a>部分。关于作用域控制，详见[此处](/fundamentals/injection-scopes)。

如果需要从全局上下文获取提供者（例如该提供者在其他模块中被注入），可在 `get()` 方法的第二个参数中传入 `{{ '{' }} strict: false {{ '}' }}` 选项：

```typescript
this.moduleRef.get(Service, { strict: false })
```

#### 解析作用域提供者

如需动态解析作用域提供者（瞬态或请求作用域），请使用 `resolve()` 方法，并传入提供者的注入令牌：

```typescript
@@filename(cats.service)
@Injectable()
export class CatsService implements OnModuleInit {
  private transientService: TransientService;
  constructor(private moduleRef: ModuleRef) {}

  async onModuleInit() {
    this.transientService = await this.moduleRef.resolve(TransientService);
  }
}
@@switch
@Injectable()
@Dependencies(ModuleRef)
export class CatsService {
  constructor(moduleRef) {
    this.moduleRef = moduleRef;
  }

  async onModuleInit() {
    this.transientService = await this.moduleRef.resolve(TransientService);
  }
}
```

`resolve()` 方法会返回该提供者在其专属依赖注入（Dependency Injection）子树中的唯一实例。每个子树拥有唯一的上下文标识符（context identifier）。因此，如果多次调用该方法并比较返回的实例引用，会发现它们并不相等。

```typescript
@@filename(cats.service)
@Injectable()
export class CatsService implements OnModuleInit {
  constructor(private moduleRef: ModuleRef) {}

  async onModuleInit() {
    const transientServices = await Promise.all([
      this.moduleRef.resolve(TransientService),
      this.moduleRef.resolve(TransientService),
    ]);
    console.log(transientServices[0] === transientServices[1]); // false
  }
}
@@switch
@Injectable()
@Dependencies(ModuleRef)
export class CatsService {
  constructor(moduleRef) {
    this.moduleRef = moduleRef;
  }

  async onModuleInit() {
    const transientServices = await Promise.all([
      this.moduleRef.resolve(TransientService),
      this.moduleRef.resolve(TransientService),
    ]);
    console.log(transientServices[0] === transientServices[1]); // false
  }
}
```

如果希望多次 `resolve()` 调用返回同一个实例，并确保它们共享同一个依赖注入子树，可以为 `resolve()` 方法传入上下文标识符。可通过 `ContextIdFactory` 类的 `create()` 方法生成唯一标识符：

```typescript
@@filename(cats.service)
@Injectable()
export class CatsService implements OnModuleInit {
  constructor(private moduleRef: ModuleRef) {}

  async onModuleInit() {
    const contextId = ContextIdFactory.create();
    const transientServices = await Promise.all([
      this.moduleRef.resolve(TransientService, contextId),
      this.moduleRef.resolve(TransientService, contextId),
    ]);
    console.log(transientServices[0] === transientServices[1]); // true
  }
}
@@switch
@Injectable()
@Dependencies(ModuleRef)
export class CatsService {
  constructor(moduleRef) {
    this.moduleRef = moduleRef;
  }

  async onModuleInit() {
    const contextId = ContextIdFactory.create();
    const transientServices = await Promise.all([
      this.moduleRef.resolve(TransientService, contextId),
      this.moduleRef.resolve(TransientService, contextId),
    ]);
    console.log(transientServices[0] === transientServices[1]); // true
  }
}
```

> info **提示** `ContextIdFactory` 类需从 `@nestjs/core` 包中导入。

#### 注册 `REQUEST` 提供者

手动生成的上下文标识符（通过 `ContextIdFactory.create()`）代表的依赖注入子树中，`REQUEST` 提供者为 `undefined`，因为它们并非由 Nest 依赖注入系统自动实例化和管理。

如需为手动创建的依赖注入子树注册自定义 `REQUEST` 对象，可使用 `ModuleRef#registerRequestByContextId()` 方法：

```typescript
const contextId = ContextIdFactory.create();
this.moduleRef.registerRequestByContextId(/* YOUR_REQUEST_OBJECT */, contextId);
```

#### 获取当前子树

有时你可能希望在**请求上下文**中解析请求作用域的提供者实例。假设 `CatsService` 是请求作用域，并且你希望解析同样为请求作用域的 `CatsRepository` 实例。为了让它们共享同一个依赖注入子树，必须获取当前的上下文标识符，而不是像上文那样新建一个。你可以通过注入请求对象（使用 `@Inject()` 装饰器）来实现：

```typescript
@@filename(cats.service)
@Injectable()
export class CatsService {
  constructor(
    @Inject(REQUEST) private request: Record<string, unknown>,
  ) {}
}
@@switch
@Injectable()
@Dependencies(REQUEST)
export class CatsService {
  constructor(request) {
    this.request = request;
  }
}
```

> info **提示** 关于请求提供者（Request Provider）可参考[这里](https://docs.nestjs.com/fundamentals/injection-scopes#request-provider)。

此时，可通过 `ContextIdFactory` 类的 `getByRequest()` 方法，基于请求对象生成上下文标识符，并传递给 `resolve()` 方法：

```typescript
const contextId = ContextIdFactory.getByRequest(this.request)
const catsRepository = await this.moduleRef.resolve(CatsRepository, contextId)
```

#### 动态实例化自定义类

如需动态实例化**未被注册为提供者**的类，可使用模块引用的 `create()` 方法：

```typescript
@@filename(cats.service)
@Injectable()
export class CatsService implements OnModuleInit {
  private catsFactory: CatsFactory;
  constructor(private moduleRef: ModuleRef) {}

  async onModuleInit() {
    this.catsFactory = await this.moduleRef.create(CatsFactory);
  }
}
@@switch
@Injectable()
@Dependencies(ModuleRef)
export class CatsService {
  constructor(moduleRef) {
    this.moduleRef = moduleRef;
  }

  async onModuleInit() {
    this.catsFactory = await this.moduleRef.create(CatsFactory);
  }
}
```

这种方式可以让你在框架容器之外按需实例化不同的类。

<app-banner-devtools></app-banner-devtools>
