### 动态模块（Dynamic Modules）

[模块章节](/modules) 介绍了 Nest 模块（Module）的基础知识，并简要提及了[动态模块](https://docs.nestjs.com/modules#dynamic-modules)。本章节将深入讲解动态模块。学习完本章后，你将能够很好地理解什么是动态模块，以及如何、何时使用它们。

#### 简介

在文档的 **概览** 部分，大多数应用代码示例都使用了常规的、即静态模块（Static Module）。模块用于定义一组组件，如[提供者](/providers)和[控制器](/controllers)，它们作为应用整体的一个模块化部分协同工作。模块为这些组件提供了执行上下文（Execution Context）或作用域。例如，在模块中定义的提供者对该模块的其他成员可见，无需导出即可访问。当需要让某个提供者在模块外部可见时，需先从其宿主模块导出，然后在消费模块中导入。

让我们通过一个熟悉的例子来说明。

首先，我们定义一个 `UsersModule`，用于提供并导出 `UsersService`。`UsersModule` 是 `UsersService` 的**宿主模块**：

```typescript
import { Module } from '@nestjs/common'
import { UsersService } from './users.service'

@Module({
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
```

接下来，定义一个 `AuthModule`，它导入了 `UsersModule`，这样 `UsersModule` 导出的提供者在 `AuthModule` 内部也可用：

```typescript
import { Module } from '@nestjs/common'
import { AuthService } from './auth.service'
import { UsersModule } from '../users/users.module'

@Module({
  imports: [UsersModule],
  providers: [AuthService],
  exports: [AuthService],
})
export class AuthModule {}
```

通过这些结构，我们可以在 `AuthModule` 中的 `AuthService` 注入 `UsersService`：

```typescript
import { Injectable } from '@nestjs/common'
import { UsersService } from '../users/users.service'

@Injectable()
export class AuthService {
  constructor(private usersService: UsersService) {}
  /*
    实现细节，使用 this.usersService
  */
}
```

我们将这种方式称为**静态模块绑定**。Nest 需要将模块组装在一起的所有信息，都已在宿主模块和消费模块中声明。让我们来拆解下这个过程发生了什么。Nest 通过以下步骤让 `UsersService` 在 `AuthModule` 内可用：

1. 实例化 `UsersModule`，包括递归导入 `UsersModule` 所依赖的其他模块，并解析所有依赖（参见[自定义提供者](https://docs.nestjs.com/fundamentals/custom-providers)）。
2. 实例化 `AuthModule`，并将 `UsersModule` 导出的提供者提供给 `AuthModule` 内的组件（就像这些提供者直接在 `AuthModule` 中声明一样）。
3. 在 `AuthService` 中注入 `UsersService` 的实例。

#### 动态模块的使用场景

使用静态模块绑定时，消费模块**无法影响**宿主模块中提供者的配置方式。为什么这很重要？考虑这样一种情况：我们有一个通用模块，需要在不同场景下有不同的行为。这类似于许多系统中的"插件"概念，即通用功能在被消费前需要一些配置。

以 Nest 中的**配置模块（Configuration Module）**为例。许多应用希望通过配置模块将配置信息外部化，这样可以在不同部署环境下动态更改应用设置：比如开发环境用开发数据库，测试环境用测试数据库等。通过将配置参数的管理交给配置模块，应用源码就能与配置参数解耦。

问题在于，配置模块本身是通用的（类似"插件"），需要由消费模块进行定制。这时就需要用到*动态模块*。利用动态模块特性，我们可以让配置模块变为**动态**，使消费模块在导入时通过 API 控制其定制方式。

换句话说，动态模块为模块间的导入提供了 API，可以在导入时自定义该模块的属性和行为，而不是像静态绑定那样一成不变。

<app-banner-devtools></app-banner-devtools>

#### 配置模块示例

本节将使用[配置章节](https://docs.nestjs.com/techniques/configuration#service)中的基础示例代码。完整代码可在[此处](https://github.com/nestjs/nest/tree/master/sample/25-dynamic-modules)获取。

我们的需求是让 `ConfigModule` 能接受一个 `options` 对象进行定制。基础示例中 `.env` 文件的位置是写死在项目根目录下的。假设我们希望这个位置可配置，比如将所有 `.env` 文件放在项目根目录下的 `config` 文件夹（即与 `src` 同级）。你希望在不同项目中使用 `ConfigModule` 时可以选择不同的文件夹。

动态模块让我们可以在导入模块时传递参数，从而改变其行为。我们先从消费模块的视角出发，倒推实现方式。首先，回顾下*静态*导入 `ConfigModule` 的例子（即无法影响被导入模块行为的方式）。请注意 `@Module()` 装饰器中的 `imports` 数组：

```typescript
import { Module } from '@nestjs/common'
import { AppController } from './app.controller'
import { AppService } from './app.service'
import { ConfigModule } from './config/config.module'

@Module({
  imports: [ConfigModule],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
```

再来看下*动态模块*导入的方式，此时我们传递了一个配置对象。对比这两种方式在 `imports` 数组上的区别：

```typescript
import { Module } from '@nestjs/common'
import { AppController } from './app.controller'
import { AppService } from './app.service'
import { ConfigModule } from './config/config.module'

@Module({
  imports: [ConfigModule.register({ folder: './config' })],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
```

来看下上面动态模块示例发生了什么：

1. `ConfigModule` 是一个普通类，所以我们可以推断它有一个**静态方法** `register()`。之所以是静态方法，是因为我们是通过类名调用的，而不是类的实例。注意：这个方法名可以自定义，但通常建议用 `forRoot()` 或 `register()`。
2. `register()` 方法由我们自己定义，可以接受任意参数。这里我们用一个简单的 `options` 对象。
3. 可以推断 `register()` 方法必须返回类似模块的东西，因为它的返回值出现在 `imports` 列表中，而这个列表通常只包含模块类名。

实际上，`register()` 方法返回的是一个**动态模块（Dynamic Module）**。动态模块就是在运行时创建的模块，拥有与静态模块完全相同的属性，只是多了一个 `module` 属性。我们先回顾下静态模块声明，关注下传递给装饰器的模块选项：

```typescript
@Module({
  imports: [DogsModule],
  controllers: [CatsController],
  providers: [CatsService],
  exports: [CatsService]
})
```

动态模块返回的对象接口与上面完全一致，只是多了一个 `module` 属性。`module` 属性用于指定模块名，应该与类名一致，如下例所示。

> info **提示** 对于动态模块，模块选项对象中，除了 `module` 属性外，其他属性都是可选的。

那静态的 `register()` 方法到底做了什么？它的职责就是返回一个符合 `DynamicModule` 接口的对象。调用它时，实际上就是给 `imports` 列表提供了一个模块，就像静态方式直接写模块类名一样。也就是说，动态模块 API 只是返回一个模块，只不过这些属性是通过代码动态指定的，而不是写死在 `@Module` 装饰器里。

还有几点细节需要补充：

1. 现在我们可以说，`@Module()` 装饰器的 `imports` 属性不仅可以接受模块类名（如 `imports: [UsersModule]`），还可以接受返回动态模块的函数（如 `imports: [ConfigModule.register(...)]`）。
2. 动态模块本身也可以导入其他模块。虽然本例没有用到，但如果动态模块依赖其他模块的提供者，可以通过 `imports` 属性导入。和静态模块声明方式完全一致。

理解了这些，我们来看下动态 `ConfigModule` 的声明应该是什么样子。

```typescript
import { DynamicModule, Module } from '@nestjs/common'
import { ConfigService } from './config.service'

@Module({})
export class ConfigModule {
  static register(): DynamicModule {
    return {
      module: ConfigModule,
      providers: [ConfigService],
      exports: [ConfigService],
    }
  }
}
```

现在应该能看出各部分如何协同工作了。调用 `ConfigModule.register(...)` 会返回一个 `DynamicModule` 对象，其属性本质上与之前通过 `@Module` 装饰器声明的静态模块一致。

> info **提示** 记得从 `@nestjs/common` 导入 `DynamicModule`。

不过目前我们的动态模块还没实现**可配置**的能力。接下来我们来实现这个功能。

#### 模块配置

要定制 `ConfigModule` 的行为，最直接的方式就是在静态 `register()` 方法中传递 `options` 对象。再看下消费模块的 `imports` 属性：

```typescript
import { Module } from '@nestjs/common'
import { AppController } from './app.controller'
import { AppService } from './app.service'
import { ConfigModule } from './config/config.module'

@Module({
  imports: [ConfigModule.register({ folder: './config' })],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
```

这样就能把 `options` 对象传递给动态模块了。那如何在 `ConfigModule` 内部使用这个 `options` 呢？我们知道 `ConfigModule` 主要是提供并导出一个可注入的服务 —— ConfigService`。实际上需要用到 `options`的是`ConfigService`。假设我们已经能把 `register()`里的`options`传递给`ConfigService`，那就可以根据 `options`的属性定制服务行为了。（**注意**：目前我们还没实现参数传递，这里先写死`options`，后面会完善。）

```typescript
import { Injectable } from '@nestjs/common'
import * as dotenv from 'dotenv'
import * as fs from 'fs'
import * as path from 'path'
import { EnvConfig } from './interfaces'

@Injectable()
export class ConfigService {
  private readonly envConfig: EnvConfig

  constructor() {
    const options = { folder: './config' }

    const filePath = `${process.env.NODE_ENV || 'development'}.env`
    const envFile = path.resolve(__dirname, '../../', options.folder, filePath)
    this.envConfig = dotenv.parse(fs.readFileSync(envFile))
  }

  get(key: string): string {
    return this.envConfig[key]
  }
}
```

现在 `ConfigService` 已经可以根据 `options` 指定的文件夹查找 `.env` 文件了。

剩下的任务就是把 `register()` 传入的 `options` 注入到 `ConfigService`。当然，我们会用**依赖注入（Dependency Injection）**来实现。这是关键点，请务必理解。`ConfigModule` 提供了 `ConfigService`，而 `ConfigService` 依赖于运行时才提供的 `options` 对象。因此，我们需要先把 `options` 绑定到 Nest IoC 容器，然后让 Nest 注入到 `ConfigService`。回忆下[自定义提供者章节](https://docs.nestjs.com/fundamentals/custom-providers#non-service-based-providers)提到，提供者不仅可以是服务，也可以是任意值，所以用依赖注入处理简单对象完全没问题。

我们先在静态 `register()` 方法里把 `options` 绑定到 IoC 容器。只需把 `options` 作为一个提供者定义即可，这样就能注入到 `ConfigService` 了。请看 `providers` 数组：

```typescript
import { DynamicModule, Module } from '@nestjs/common'
import { ConfigService } from './config.service'

@Module({})
export class ConfigModule {
  static register(options: Record<string, any>): DynamicModule {
    return {
      module: ConfigModule,
      providers: [
        {
          provide: 'CONFIG_OPTIONS',
          useValue: options,
        },
        ConfigService,
      ],
      exports: [ConfigService],
    }
  }
}
```

最后一步，就是在 `ConfigService` 里注入 `'CONFIG_OPTIONS'` 提供者。注意，使用非类 token 时，需要用 `@Inject()` 装饰器（详见[自定义提供者章节](https://docs.nestjs.com/fundamentals/custom-providers#non-class-based-provider-tokens)）。

```typescript
import * as dotenv from 'dotenv'
import * as fs from 'fs'
import * as path from 'path'
import { Injectable, Inject } from '@nestjs/common'
import { EnvConfig } from './interfaces'

@Injectable()
export class ConfigService {
  private readonly envConfig: EnvConfig

  constructor(@Inject('CONFIG_OPTIONS') private options: Record<string, any>) {
    const filePath = `${process.env.NODE_ENV || 'development'}.env`
    const envFile = path.resolve(__dirname, '../../', options.folder, filePath)
    this.envConfig = dotenv.parse(fs.readFileSync(envFile))
  }

  get(key: string): string {
    return this.envConfig[key]
  }
}
```

最后补充一点：上面为了简单直接用了字符串 `'CONFIG_OPTIONS'` 作为注入令牌，最佳实践是将其定义为常量（或 `Symbol`），并单独放在一个文件里，然后导入。例如：

```typescript
export const CONFIG_OPTIONS = 'CONFIG_OPTIONS'
```

#### 示例

本章完整代码示例可见[这里](https://github.com/nestjs/nest/tree/master/sample/25-dynamic-modules)。

#### 社区命名规范

你可能见过一些 `@nestjs/` 包中有 `forRoot`、`register`、`forFeature` 等方法，不禁好奇它们的区别。虽然没有硬性规定，但 `@nestjs/` 官方包通常遵循如下约定：

- 使用 `register` 时，期望为调用模块单独配置一个动态模块。例如，Nest 的 `@nestjs/axios`：`HttpModule.register({ baseUrl: 'someUrl' })`。如果在另一个模块中用 `HttpModule.register({ baseUrl: 'somewhere else' })`，则会有不同的配置。你可以为任意多个模块这样做。

- 使用 `forRoot` 时，期望只配置一次动态模块，并在多个地方复用该配置（即使你可能没意识到，因为它被抽象了）。这就是为什么像 `GraphQLModule.forRoot()`、`TypeOrmModule.forRoot()` 只调用一次。

- 使用 `forFeature` 时，期望复用 `forRoot` 配置，但需要针对调用模块做一些特定修改（比如指定该模块可访问哪些仓库，或日志上下文等）。

这些方法通常还有对应的异步版本，如 `registerAsync`、`forRootAsync`、`forFeatureAsync`，它们的含义类似，只是配置方式用到了 Nest 的依赖注入。

#### 可配置模块构建器（ConfigurableModuleBuilder）

手动创建高度可配置、支持异步方法（如 `registerAsync`、`forRootAsync` 等）的动态模块对新手来说较为复杂。为此，Nest 提供了 `ConfigurableModuleBuilder` 类，能让你用极少的代码构建模块"蓝图"。

比如，我们将上面的 `ConfigModule` 用 `ConfigurableModuleBuilder` 重写。首先，定义一个接口，描述 `ConfigModule` 需要的选项：

```typescript
export interface ConfigModuleOptions {
  folder: string
}
```

然后，在 `config.module.ts` 同级新建 `config.module-definition.ts` 文件，利用 `ConfigurableModuleBuilder` 构建模块定义：

```typescript
@@filename(config.module-definition)
import { ConfigurableModuleBuilder } from '@nestjs/common';
import { ConfigModuleOptions } from './interfaces/config-module-options.interface';

export const { ConfigurableModuleClass, MODULE_OPTIONS_TOKEN } =
  new ConfigurableModuleBuilder<ConfigModuleOptions>().build();
@@switch
import { ConfigurableModuleBuilder } from '@nestjs/common';

export const { ConfigurableModuleClass, MODULE_OPTIONS_TOKEN } =
  new ConfigurableModuleBuilder().build();
```

接下来，修改 `config.module.ts`，让其继承自动生成的 `ConfigurableModuleClass`：

```typescript
import { Module } from '@nestjs/common'
import { ConfigService } from './config.service'
import { ConfigurableModuleClass } from './config.module-definition'

@Module({
  providers: [ConfigService],
  exports: [ConfigService],
})
export class ConfigModule extends ConfigurableModuleClass {}
```

将 `ConfigurableModuleClass` 作为父类进行扩展后，`ConfigModule` 不仅具备了之前自定义实现的 `register` 方法，还自动拥有了 `registerAsync` 方法。这样，使用者就可以通过异步工厂等方式，异步地配置该模块：

```typescript
@Module({
  imports: [
    ConfigModule.register({ folder: './config' }),
    // 或者：
    // ConfigModule.registerAsync({
    //   useFactory: () => {
    //     return {
    //       folder: './config',
    //     }
    //   },
    //   inject: [...其他依赖...]
    // }),
  ],
})
export class AppModule {}
```

最后，更新 `ConfigService`，注入自动生成的模块选项提供者，而不是之前的 `'CONFIG_OPTIONS'`：

```typescript
@Injectable()
export class ConfigService {
  constructor(@Inject(MODULE_OPTIONS_TOKEN) private options: ConfigModuleOptions) { ... }
}
```

#### 自定义方法名

`ConfigurableModuleClass` 默认提供 `register` 及其异步版本 `registerAsync`。如需自定义方法名，可用 `ConfigurableModuleBuilder#setClassMethodName`：

```typescript
@@filename(config.module-definition)
export const { ConfigurableModuleClass, MODULE_OPTIONS_TOKEN } =
  new ConfigurableModuleBuilder<ConfigModuleOptions>().setClassMethodName('forRoot').build();
@@switch
export const { ConfigurableModuleClass, MODULE_OPTIONS_TOKEN } =
  new ConfigurableModuleBuilder().setClassMethodName('forRoot').build();
```

这样会生成带有 `forRoot` 和 `forRootAsync` 方法的类。例如：

```typescript
@Module({
  imports: [
    ConfigModule.forRoot({ folder: './config' }), // 注意这里用的是 "forRoot"
    // 或者：
    // ConfigModule.forRootAsync({
    //   useFactory: () => {
    //     return {
    //       folder: './config',
    //     }
    //   },
    //   inject: [...其他依赖...]
    // }),
  ],
})
export class AppModule {}
```

#### 自定义选项工厂类

由于 `registerAsync` 方法（或 `forRootAsync` 等，具体取决于配置）允许使用者传递一个用于生成模块配置的提供者定义，因此库的使用者可以提供一个类来构建配置对象。

```typescript
@Module({
  imports: [
    ConfigModule.registerAsync({
      useClass: ConfigModuleOptionsFactory,
    }),
  ],
})
export class AppModule {}
```

默认情况下，这个类必须实现一个 `create()` 方法，用于返回模块的配置对象。不过，如果你的库采用了不同的命名规范，可以通过 `ConfigurableModuleBuilder#setFactoryMethodName` 方法自定义工厂方法名，例如 `createConfigOptions`：

```typescript
@@filename(config.module-definition)
export const { ConfigurableModuleClass, MODULE_OPTIONS_TOKEN } =
  new ConfigurableModuleBuilder<ConfigModuleOptions>().setFactoryMethodName('createConfigOptions').build();
@@switch
export const { ConfigurableModuleClass, MODULE_OPTIONS_TOKEN } =
  new ConfigurableModuleBuilder().setFactoryMethodName('createConfigOptions').build();
```

此时，`ConfigModuleOptionsFactory` 类必须实现 `createConfigOptions` 方法（而不是 `create`）：

```typescript
@Module({
  imports: [
    ConfigModule.registerAsync({
      useClass: ConfigModuleOptionsFactory, // 该类必须实现 "createConfigOptions" 方法
    }),
  ],
})
export class AppModule {}
```

#### 额外选项

有些情况下，模块可能需要额外的选项来决定其行为（比如 `isGlobal` 标志，或直接叫 `global`），但这些选项又不应该包含在 `MODULE_OPTIONS_TOKEN` 提供者中（因为它们与模块内注册的服务/提供者无关，例如 `ConfigService` 并不需要知道其宿主模块是否被注册为全局模块）。

此时，可以使用 `ConfigurableModuleBuilder#setExtras` 方法。示例如下：

```typescript
export const { ConfigurableModuleClass, MODULE_OPTIONS_TOKEN } =
  new ConfigurableModuleBuilder<ConfigModuleOptions>()
    .setExtras(
      {
        isGlobal: true,
      },
      (definition, extras) => ({
        ...definition,
        global: extras.isGlobal,
      })
    )
    .build()
```

在上面的例子中，`setExtras` 方法的第一个参数是包含"额外"属性默认值的对象。第二个参数是一个函数，接收自动生成的模块定义（包含 `provider`、`exports` 等）和 `extras` 对象（代表额外属性，可能由使用者指定或采用默认值）。该函数返回修改后的模块定义。在本例中，我们将 `extras.isGlobal` 属性赋值给模块定义的 `global` 属性（该属性决定模块是否为全局模块，详见[这里](/modules#dynamic-modules)）。

在消费该模块时，可以像下面这样传递额外的 `isGlobal` 标志：

```typescript
@Module({
  imports: [
    ConfigModule.register({
      isGlobal: true,
      folder: './config',
    }),
  ],
})
export class AppModule {}
```

但由于 `isGlobal` 被声明为"额外"属性，它不会出现在 `MODULE_OPTIONS_TOKEN` 提供者中：

```typescript
@Injectable()
export class ConfigService {
  constructor(@Inject(MODULE_OPTIONS_TOKEN) private options: ConfigModuleOptions) {
    // "options" 对象不会有 "isGlobal" 属性
    // ...
  }
}
```

#### 扩展自动生成的方法

如有需要，可以扩展自动生成的静态方法（如 `register`、`registerAsync`）：

```typescript
import { Module } from '@nestjs/common'
import { ConfigService } from './config.service'
import {
  ConfigurableModuleClass,
  ASYNC_OPTIONS_TYPE,
  OPTIONS_TYPE,
} from './config.module-definition'

@Module({
  providers: [ConfigService],
  exports: [ConfigService],
})
export class ConfigModule extends ConfigurableModuleClass {
  static register(options: typeof OPTIONS_TYPE): DynamicModule {
    return {
      // 你的自定义逻辑
      ...super.register(options),
    }
  }

  static registerAsync(options: typeof ASYNC_OPTIONS_TYPE): DynamicModule {
    return {
      // 你的自定义逻辑
      ...super.registerAsync(options),
    }
  }
}
```

注意要从模块定义文件导出 `OPTIONS_TYPE` 和 `ASYNC_OPTIONS_TYPE` 类型：

```typescript
export const { ConfigurableModuleClass, MODULE_OPTIONS_TOKEN, OPTIONS_TYPE, ASYNC_OPTIONS_TYPE } =
  new ConfigurableModuleBuilder<ConfigModuleOptions>().build()
```
