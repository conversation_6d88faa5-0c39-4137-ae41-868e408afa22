### 测试（Testing）

自动化测试被认为是现代软件开发不可或缺的重要环节。自动化让开发过程中能够快速、便捷地重复执行单个测试或测试套件，有助于确保发布版本达到质量和性能目标。自动化测试不仅提升了测试覆盖率，还为开发者提供了更快的反馈循环。它既提升了开发者个人的生产力，也确保在关键的开发生命周期节点（如源代码提交、功能集成和版本发布）能够及时运行测试。

这些测试通常涵盖多种类型，包括单元测试（Unit Testing）、端到端测试（End-to-End Testing，e2e）、集成测试（Integration Testing）等。虽然自动化测试的好处毋庸置疑，但搭建测试环境有时会比较繁琐。Nest 致力于推广开发最佳实践，包括高效的测试，因此内置了如下特性，帮助开发者和团队构建并自动化测试。Nest：

- 自动为组件生成默认的单元测试和为应用生成端到端测试脚手架
- 提供默认工具（如可构建隔离模块/应用加载器的测试运行器（Test Runner））
- 开箱即用地集成了 [Jest](https://github.com/facebook/jest) 和 [Supertest](https://github.com/visionmedia/supertest)，同时对测试工具保持无关性
- 使 Nest 的依赖注入（Dependency Injection）系统在测试环境中可用，便于模拟（Mock）组件

如前所述，你可以使用任何你喜欢的**测试框架（Testing Framework）**，Nest 并不会强制指定特定工具。只需替换所需的部分（如测试运行器），即可继续享受 Nest 提供的现成测试能力。

#### 安装

首先，安装所需的包：

```bash
$ npm i --save-dev @nestjs/testing
```

#### 单元测试（Unit Testing）

在下方示例中，我们将测试两个类：`CatsController` 和 `CatsService`。如前所述，[Jest](https://github.com/facebook/jest) 是默认的测试框架。它既作为测试运行器，也提供断言函数和测试替身（Test Double）工具，便于模拟（Mock）、监视（Spy）等。在下面的基础测试中，我们手动实例化这些类，并确保控制器和服务能够满足其 API 合同。

```typescript
@@filename(cats.controller.spec)
import { CatsController } from './cats.controller';
import { CatsService } from './cats.service';

describe('CatsController', () => {
  let catsController: CatsController;
  let catsService: CatsService;

  beforeEach(() => {
    catsService = new CatsService();
    catsController = new CatsController(catsService);
  });

  describe('findAll', () => {
    it('should return an array of cats', async () => {
      const result = ['test'];
      jest.spyOn(catsService, 'findAll').mockImplementation(() => result);

      expect(await catsController.findAll()).toBe(result);
    });
  });
});
@@switch
import { CatsController } from './cats.controller';
import { CatsService } from './cats.service';

describe('CatsController', () => {
  let catsController;
  let catsService;

  beforeEach(() => {
    catsService = new CatsService();
    catsController = new CatsController(catsService);
  });

  describe('findAll', () => {
    it('should return an array of cats', async () => {
      const result = ['test'];
      jest.spyOn(catsService, 'findAll').mockImplementation(() => result);

      expect(await catsController.findAll()).toBe(result);
    });
  });
});
```

> info **提示** 建议将测试文件放在被测试类附近。测试文件应以 `.spec` 或 `.test` 结尾。

由于上述示例较为简单，实际上并未测试任何 Nest 特有的内容。事实上，我们甚至没有使用依赖注入，而是直接将 `CatsService` 的实例传递给 `catsController`。这种测试方式 —— 即手动实例化被测类 ——通 常被称为**隔离测试（Isolated Testing）**，它与框架无关。接下来我们将介绍一些更高级的能力，帮助你测试更充分利用 Nest 特性的应用。

#### 测试工具（Testing utilities）

`@nestjs/testing` 包提供了一套实用工具，帮助你实现更健壮的测试流程。我们来用内置的 `Test` 类重写前面的示例：

```typescript
@@filename(cats.controller.spec)
import { Test } from '@nestjs/testing';
import { CatsController } from './cats.controller';
import { CatsService } from './cats.service';

describe('CatsController', () => {
  let catsController: CatsController;
  let catsService: CatsService;

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
        controllers: [CatsController],
        providers: [CatsService],
      }).compile();

    catsService = moduleRef.get(CatsService);
    catsController = moduleRef.get(CatsController);
  });

  describe('findAll', () => {
    it('should return an array of cats', async () => {
      const result = ['test'];
      jest.spyOn(catsService, 'findAll').mockImplementation(() => result);

      expect(await catsController.findAll()).toBe(result);
    });
  });
});
@@switch
import { Test } from '@nestjs/testing';
import { CatsController } from './cats.controller';
import { CatsService } from './cats.service';

describe('CatsController', () => {
  let catsController;
  let catsService;

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
        controllers: [CatsController],
        providers: [CatsService],
      }).compile();

    catsService = moduleRef.get(CatsService);
    catsController = moduleRef.get(CatsController);
  });

  describe('findAll', () => {
    it('should return an array of cats', async () => {
      const result = ['test'];
      jest.spyOn(catsService, 'findAll').mockImplementation(() => result);

      expect(await catsController.findAll()).toBe(result);
    });
  });
});
```

`Test` 类可以为应用提供一个执行上下文（Execution Context），本质上模拟了完整的 Nest 运行时（Runtime），同时为你提供了便捷的钩子来管理类实例，包括模拟（Mock）和覆盖（Override）。`Test` 类的 `createTestingModule()` 方法接收一个模块元数据对象作为参数（与传递给 `@Module()` 装饰器的对象相同）。该方法会返回一个 `测试模块（TestingModule）` 实例，进而提供若干方法。对于单元测试（Unit Testing）来说，最重要的是 `compile()` 方法。它会引导（Bootstrapping）模块及其依赖（类似于在 `main.ts` 中用 `NestFactory.create()` 启动应用），并返回一个可用于测试的模块。

> info **提示** `compile()` 方法是**异步**的，因此必须使用 `await`。模块编译完成后，你可以通过 `get()` 方法获取其声明的任何**静态**实例（控制器和提供者）。

`测试模块（TestingModule）` 继承自 [模块引用（Module Reference）](/fundamentals/module-ref) 类，因此也具备动态解析作用域提供者（如瞬态或请求作用域）的能力。可以通过 `resolve()` 方法实现（`get()` 方法只能获取静态实例）。

```typescript
const moduleRef = await Test.createTestingModule({
  controllers: [CatsController],
  providers: [CatsService],
}).compile()

catsService = await moduleRef.resolve(CatsService)
```

> warning **警告** `resolve()` 方法会返回该提供者在其**依赖注入（Dependency Injection）容器子树**中的唯一实例。每个子树都有唯一的上下文标识符。因此，如果多次调用该方法并比较实例引用，会发现它们并不相等。

> info **提示** 你可以在[这里](/fundamentals/module-ref)了解更多模块引用相关特性。

除了使用生产环境的提供者外，你还可以为测试目的覆盖（Override）为[自定义提供者（Custom Provider）](/fundamentals/custom-providers)。例如，你可以模拟数据库服务，而不是连接真实数据库。我们将在下一节详细介绍覆盖，但在单元测试中同样适用。

<app-banner-courses></app-banner-courses>

#### 自动模拟（Auto mocking）

Nest 还允许你定义一个模拟工厂（mock factory），用于为所有缺失的依赖自动生成模拟对象（Mock）。当某个类依赖项较多，手动为每个依赖编写模拟对象既耗时又繁琐时，这一特性尤为有用。要使用该功能，需要在 `createTestingModule()` 方法后链式调用 `useMocker()` 方法，并传入一个用于生成依赖模拟对象的工厂函数。该工厂函数可以接收一个可选的 token（实例令牌，即任何有效的 Nest 提供者（Provider）令牌），并返回一个模拟实现。下面的示例展示了如何结合 [`jest-mock`](https://www.npmjs.com/package/jest-mock) 创建通用模拟器，以及如何使用 `jest.fn()` 为 `CatsService` 创建特定的模拟对象。

```typescript
// ...
import { ModuleMocker, MockFunctionMetadata } from 'jest-mock'

const moduleMocker = new ModuleMocker(global)

describe('CatsController', () => {
  let controller: CatsController

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      controllers: [CatsController],
    })
      .useMocker((token) => {
        const results = ['test1', 'test2']
        if (token === CatsService) {
          return { findAll: jest.fn().mockResolvedValue(results) }
        }
        if (typeof token === 'function') {
          const mockMetadata = moduleMocker.getMetadata(token) as MockFunctionMetadata<any, any>
          const Mock = moduleMocker.generateFromMetadata(mockMetadata)
          return new Mock()
        }
      })
      .compile()

    controller = moduleRef.get(CatsController)
  })
})
```

你也可以像获取自定义提供者（Custom Provider）一样，从测试容器中获取这些模拟对象，例如 `moduleRef.get(CatsService)`。

> info **提示** 你也可以直接传入通用模拟工厂，例如 [`@golevelup/ts-jest`](https://github.com/golevelup/nestjs/tree/master/packages/testing) 提供的 `createMock` 方法。

> info **提示** `REQUEST` 和 `INQUIRER` 这两个提供者无法被自动模拟，因为它们在上下文中已被预定义。不过，你可以通过自定义提供者语法或 `.overrideProvider` 方法对其进行覆盖。

#### 端到端测试（End-to-End Testing）

与关注单个模块（Module）和类的单元测试（Unit Testing）不同，端到端测试（End-to-End Testing，e2e）关注的是类和模块之间更高层次的交互——更接近最终用户实际与生产系统交互的方式。随着应用规模的增长，手动测试每个 API 端点的端到端行为变得愈发困难。自动化的端到端测试有助于我们确保系统整体行为的正确性，并满足项目需求。进行端到端测试时，我们会采用与前文**单元测试**类似的配置。此外，Nest 还让你可以轻松集成 [Supertest](https://github.com/visionmedia/supertest) 库来模拟 HTTP 请求。

```typescript
@@filename(cats.e2e-spec)
import * as request from 'supertest';
import { Test } from '@nestjs/testing';
import { CatsModule } from '../../src/cats/cats.module';
import { CatsService } from '../../src/cats/cats.service';
import { INestApplication } from '@nestjs/common';

describe('Cats', () => {
  let app: INestApplication;
  let catsService = { findAll: () => ['test'] };

  beforeAll(async () => {
    const moduleRef = await Test.createTestingModule({
      imports: [CatsModule],
    })
      .overrideProvider(CatsService)
      .useValue(catsService)
      .compile();

    app = moduleRef.createNestApplication();
    await app.init();
  });

  it(`/GET cats`, () => {
    return request(app.getHttpServer())
      .get('/cats')
      .expect(200)
      .expect({
        data: catsService.findAll(),
      });
  });

  afterAll(async () => {
    await app.close();
  });
});
@@switch
import * as request from 'supertest';
import { Test } from '@nestjs/testing';
import { CatsModule } from '../../src/cats/cats.module';
import { CatsService } from '../../src/cats/cats.service';
import { INestApplication } from '@nestjs/common';

describe('Cats', () => {
  let app: INestApplication;
  let catsService = { findAll: () => ['test'] };

  beforeAll(async () => {
    const moduleRef = await Test.createTestingModule({
      imports: [CatsModule],
    })
      .overrideProvider(CatsService)
      .useValue(catsService)
      .compile();

    app = moduleRef.createNestApplication();
    await app.init();
  });

  it(`/GET cats`, () => {
    return request(app.getHttpServer())
      .get('/cats')
      .expect(200)
      .expect({
        data: catsService.findAll(),
      });
  });

  afterAll(async () => {
    await app.close();
  });
});
```

> info **提示** 如果你使用 [Fastify](/techniques/performance) 作为 HTTP 适配器，则需要稍作不同的配置，并且 Fastify 内置了测试能力：
>
> ```ts
> let app: NestFastifyApplication
>
> beforeAll(async () => {
>   app = moduleRef.createNestApplication<NestFastifyApplication>(new FastifyAdapter())
>
>   await app.init()
>   await app.getHttpAdapter().getInstance().ready()
> })
>
> it(`/GET cats`, () => {
>   return app
>     .inject({
>       method: 'GET',
>       url: '/cats',
>     })
>     .then((result) => {
>       expect(result.statusCode).toEqual(200)
>       expect(result.payload).toEqual(/* expectedPayload */)
>     })
> })
>
> afterAll(async () => {
>   await app.close()
> })
> ```

在本例中，我们基于前文介绍的一些概念进行了扩展。除了前面用到的 `compile()` 方法外，这里还使用了 `createNestApplication()` 方法来实例化一个完整的 Nest 运行时环境（Runtime）。

有一点需要注意：当你的应用通过 `compile()` 方法编译时，此时 `HttpAdapterHost#httpAdapter` 仍然是未定义的。原因是在编译阶段还没有创建 HTTP 适配器或服务器。如果你的测试依赖于 `httpAdapter`，应当使用 `createNestApplication()` 方法来创建应用实例，或者在初始化依赖关系图时重构项目以避免这种依赖。

下面我们来详细拆解这个示例：

我们将正在运行的应用引用保存在 `app` 变量中，以便后续用于模拟 HTTP 请求。

我们通过 Supertest 的 `request()` 函数来模拟 HTTP 测试。为了让这些 HTTP 请求路由到正在运行的 Nest 应用，我们将 Nest 底层的 HTTP 监听器（通常由 Express 平台提供）传递给 `request()`。因此有了 `request(app.getHttpServer())` 这样的写法。调用 `request()` 后会返回一个包装过的 HTTP 服务器（HTTP Server），它已连接到 Nest 应用，并暴露出用于模拟真实 HTTP 请求的方法。例如，`request(...).get('/cats')` 会向 Nest 应用发起一个与实际网络请求 `get '/cats'` 完全一致的请求。

在本例中，我们还为 `CatsService` 提供了一个替代（测试替身，test-double）实现，该实现仅返回一个硬编码的值，便于我们进行断言。可以通过 `overrideProvider()` 方法来提供这种替代实现。同理，Nest 还提供了 `overrideModule()`、`overrideGuard()`、`overrideInterceptor()`、`overrideFilter()` 和 `overridePipe()` 等方法，分别用于覆盖模块、守卫、拦截器、过滤器和管道。

除了 `overrideModule()` 以外，每个覆盖方法都会返回一个对象，该对象包含 3 个与[自定义提供者（Custom Provider）](/fundamentals/custom-providers)类似的方法：

- `useClass`：你可以提供一个类，Nest 会实例化该类来替换原有对象（如提供者、守卫等）。
- `useValue`：你可以提供一个实例，直接覆盖原有对象。
- `useFactory`：你可以提供一个工厂函数，返回的实例将用于覆盖原有对象。

而 `overrideModule()` 方法则返回一个包含 `useModule()` 方法的对象，你可以用它来指定一个模块以覆盖原有模块，例如：

```typescript
const moduleRef = await Test.createTestingModule({
  imports: [AppModule],
})
  .overrideModule(CatsModule)
  .useModule(AlternateCatsModule)
  .compile()
```

每种覆盖方法最终都会返回 `测试模块（TestingModule）` 实例，因此可以采用[链式调用（fluent style）](https://en.wikipedia.org/wiki/Fluent_interface)的方式组合多个方法。最后应调用 `compile()`，让 Nest 实例化并初始化模块。

此外，有时你可能希望在测试运行时（例如在 CI 服务器上）自定义日志记录器。可以使用 `setLogger()` 方法，并传入一个实现了 `LoggerService` 接口的对象，来指定 `TestModuleBuilder` 在测试期间的日志行为（默认情况下，仅 "error" 日志会输出到控制台）。

编译后的模块还提供了若干实用方法，具体如下表所示：

<table>
  <tr>
    <td>
      <code>createNestApplication()</code>
    </td>
    <td>
      基于给定模块创建并返回一个 Nest 应用（<code>INestApplication</code>{' '}
      实例）。注意，你需要手动调用 <code>init()</code> 方法进行初始化。
    </td>
  </tr>
  <tr>
    <td>
      <code>createNestMicroservice()</code>
    </td>
    <td>
      基于给定模块创建并返回一个 Nest 微服务（<code>INestMicroservice</code> 实例）。
    </td>
  </tr>
  <tr>
    <td>
      <code>get()</code>
    </td>
    <td>
      获取应用上下文中可用的控制器或提供者（包括守卫、过滤器等）的静态实例。该方法继承自{' '}
      <a href="/fundamentals/module-ref">模块引用（Module Reference）</a> 类。
    </td>
  </tr>
  <tr>
    <td>
      <code>resolve()</code>
    </td>
    <td>
      获取应用上下文中动态创建的作用域实例（如请求作用域或瞬态）的控制器或提供者。该方法同样继承自{' '}
      <a href="/fundamentals/module-ref">模块引用（Module Reference）</a> 类。
    </td>
  </tr>
  <tr>
    <td>
      <code>select()</code>
    </td>
    <td>
      在模块依赖图中导航，可用于从选定模块中获取特定实例（结合 <code>get()</code> 方法的 strict 模式{' '}
      <code>strict: true</code> 一起使用）。
    </td>
  </tr>
</table>

> info **提示** 建议将端到端（e2e）测试文件放在 `test` 目录下，文件名应以 `.e2e-spec` 结尾。

#### 全局注册增强器的覆盖（Overriding globally registered enhancers）

如果你有全局注册的守卫（Guard）、管道（Pipe）、拦截器（Interceptor）或过滤器（Filter），想要在测试中覆盖它们，需要额外做一些配置。回顾一下，原始注册方式如下：

```typescript
providers: [
  {
    provide: APP_GUARD,
    useClass: JwtAuthGuard,
  },
],
```

这里通过 `APP_*` 令牌将守卫注册为"多重"提供者（multi-provider）。要想在测试中替换 `JwtAuthGuard`，需要将注册方式改为使用已存在的提供者：

```typescript
providers: [
  {
    provide: APP_GUARD,
    useExisting: JwtAuthGuard,
    // ^^^^^^^^ 注意这里用 'useExisting' 替代 'useClass'
  },
  JwtAuthGuard,
],
```

> info **提示** 将 `useClass` 改为 `useExisting`，即可让 Nest 通过已注册的提供者进行引用，而不是自动实例化。

这样，`JwtAuthGuard` 就作为常规提供者暴露给 Nest，在创建 `测试模块（TestingModule）` 时可以被覆盖：

```typescript
const moduleRef = await Test.createTestingModule({
  imports: [AppModule],
})
  .overrideProvider(JwtAuthGuard)
  .useClass(MockAuthGuard)
  .compile()
```

此时，所有测试请求都会使用 `MockAuthGuard`。

#### 测试请求作用域实例（Testing request-scoped instances）

[请求作用域（Request-scoped）](/fundamentals/injection-scopes) 提供者会为每个传入的**请求**单独创建实例。请求处理完成后，这些实例会被垃圾回收。这带来一个问题：我们无法直接访问为某次测试请求生成的依赖注入子树。

如前文所述，可以通过 `resolve()` 方法获取动态实例。同时，正如[这里](https://docs.nestjs.com/fundamentals/module-ref#resolving-scoped-providers)所述，我们可以传递唯一的上下文标识符（context identifier），以控制依赖注入容器子树的生命周期。那么在测试场景下，如何利用这一点？

解决方案是：提前生成一个上下文标识符，并强制 Nest 在所有传入请求中都使用该 ID。这样，我们就能访问为某次测试请求生成的实例。

具体做法如下，使用 `jest.spyOn()` 对 `ContextIdFactory` 进行模拟：

```typescript
const contextId = ContextIdFactory.create()
jest.spyOn(ContextIdFactory, 'getByRequest').mockImplementation(() => contextId)
```

现在，我们可以通过 `contextId` 访问后续任意请求生成的依赖注入子树：

```typescript
catsService = await moduleRef.resolve(CatsService, contextId)
```
