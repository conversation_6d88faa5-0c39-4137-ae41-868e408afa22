### CLI 命令参考

#### nest new

创建一个新的（标准模式）Nest 项目。

```bash
$ nest new <name> [options]
$ nest n <name> [options]
```

##### 描述

创建并初始化一个新的 Nest 项目，并提示选择包管理器。

- 使用给定的 `<name>` 创建一个文件夹
- 在该文件夹中生成配置文件
- 创建用于源代码（`/src`）和端到端测试（`/test`）的子文件夹
- 在子文件夹中填充应用组件和测试的默认文件

##### 参数

| 参数     | 描述         |
| -------- | ------------ |
| `<name>` | 新项目的名称 |

##### 选项

| 选项                                  | 描述                                                                                                                                                                 |
| ------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `--dry-run`                           | 仅报告将要进行的更改，但不会实际修改文件系统。<br/>别名：`-d`                                                                                                        |
| `--skip-git`                          | 跳过 git 仓库初始化。<br/>别名：`-g`                                                                                                                                 |
| `--skip-install`                      | 跳过依赖包安装。<br/>别名：`-s`                                                                                                                                      |
| `--package-manager [package-manager]` | 指定包管理器。可选 `npm`、`yarn` 或 `pnpm`。包管理器需已全局安装。<br/>别名：`-p`                                                                                    |
| `--language [language]`               | 指定编程语言（`TS` 或 `JS`）。<br/>别名：`-l`                                                                                                                        |
| `--collection [collectionName]`       | 指定原型生成器（Schematics）集合。使用已安装 npm 包的包名。<br/>别名：`-c`                                                                                           |
| `--strict`                            | 启用以下 TypeScript 编译器严格模式选项：`strictNullChecks`、`noImplicitAny`、`strictBindCallApply`、`forceConsistentCasingInFileNames`、`noFallthroughCasesInSwitch` |

#### nest generate

基于原型生成器（Schematics）生成和/或修改文件。

```bash
$ nest generate <schematic> <name> [options]
$ nest g <schematic> <name> [options]
```

##### 参数

| 参数          | 描述                                                             |
| ------------- | ---------------------------------------------------------------- |
| `<schematic>` | 要生成的 `schematic` 或 `collection:schematic`。可用原型见下表。 |
| `<name>`      | 生成组件的名称。                                                 |

##### 可用原型（Schematics）

| 名称          | 别名 | 描述                                                                                    |
| ------------- | ---- | --------------------------------------------------------------------------------------- |
| `app`         |      | 在多包仓库结构（Monorepo）中生成新应用（会自动转换为多包仓库结构）。                    |
| `library`     | lib  | 在多包仓库结构中生成新库（会自动转换为多包仓库结构）。                                  |
| `class`       | cl   | 生成新类。                                                                              |
| `controller`  | co   | 生成控制器（Controller）声明。                                                          |
| `decorator`   | d    | 生成自定义装饰器（Decorator）。                                                         |
| `filter`      | f    | 生成异常过滤器（Exception Filter）声明。                                                |
| `gateway`     | ga   | 生成网关（Gateway）声明。                                                               |
| `guard`       | gu   | 生成守卫（Guard）声明。                                                                 |
| `interface`   | itf  | 生成接口。                                                                              |
| `interceptor` | itc  | 生成拦截器（Interceptor）声明。                                                         |
| `middleware`  | mi   | 生成中间件（Middleware）声明。                                                          |
| `module`      | mo   | 生成模块（Module）声明。                                                                |
| `pipe`        | pi   | 生成管道（Pipe）声明。                                                                  |
| `provider`    | pr   | 生成提供者（Provider）声明。                                                            |
| `resolver`    | r    | 生成解析器（Resolver）声明。                                                            |
| `resource`    | res  | 生成新的 CRUD 资源。详情见 [CRUD（资源）生成器](/recipes/crud-generator)（仅支持 TS）。 |
| `service`     | s    | 生成服务声明。                                                                          |

##### 选项

| 选项                            | 描述                                                                       |
| ------------------------------- | -------------------------------------------------------------------------- |
| `--dry-run`                     | 仅报告将要进行的更改，但不会实际修改文件系统。<br/>别名：`-d`              |
| `--project [project]`           | 元素应添加到的项目。<br/>别名：`-p`                                        |
| `--flat`                        | 不为元素生成文件夹。                                                       |
| `--collection [collectionName]` | 指定原型生成器（Schematics）集合。使用已安装 npm 包的包名。<br/>别名：`-c` |
| `--spec`                        | 强制生成测试（spec）文件（默认开启）。                                     |
| `--no-spec`                     | 禁用测试（spec）文件生成。                                                 |

#### nest build

将应用或工作空间编译到输出文件夹中。

此外，`build` 命令还负责：

- 通过 `tsconfig-paths` 映射路径（如果使用路径别名）
- 如果启用 `@nestjs/swagger` CLI 插件，则为数据传输对象添加 OpenAPI 装饰器注解
- 如果启用 `@nestjs/graphql` CLI 插件，则为数据传输对象添加 GraphQL 装饰器注解

```bash
$ nest build <name> [options]
```

##### 参数

| 参数     | 描述                        |
| -------- | --------------------------- |
| `<name>` | 要构建的项目（Project）名称 |

##### 选项

| 选项                    | 描述                                                                                                                           |
| ----------------------- | ------------------------------------------------------------------------------------------------------------------------------ |
| `--path [path]`         | `tsconfig` 文件路径。<br/>别名：`-p`                                                                                           |
| `--config [path]`       | `nest-cli` 配置文件路径。<br/>别名：`-c`                                                                                       |
| `--watch`               | 以监听模式（live-reload）运行。<br/>如果使用 `tsc` 编译，可输入 `rs` 重启应用（需设置 `manualRestart: true`）。<br/>别名：`-w` |
| `--builder [name]`      | 指定用于编译的构建器（可选 `tsc`、`swc` 或 `webpack`）。<br/>别名：`-b`                                                        |
| `--webpack`             | 使用 webpack 进行编译（已弃用：请改用 `--builder webpack`）。                                                                  |
| `--webpackPath`         | webpack 配置文件路径。                                                                                                         |
| `--tsc`                 | 强制使用 `tsc` 进行编译。                                                                                                      |
| `--watchAssets`         | 监听非 TS 文件（如 `.graphql` 等资源文件）。详见 [资源文件](cli/monorepo#assets)。                                             |
| `--type-check`          | 启用类型检查（SWC 编译时可用）。                                                                                               |
| `--all`                 | 构建多包仓库结构（Monorepo）中的所有项目。                                                                                     |
| `--preserveWatchOutput` | 在监听模式下保留旧的控制台输出，不清屏（仅适用于 `tsc` 监听模式）。                                                            |

#### nest start

编译并运行应用（或工作空间中的默认项目）。

```bash
$ nest start <name> [options]
```

##### 参数

| 参数     | 描述                        |
| -------- | --------------------------- |
| `<name>` | 要运行的项目（Project）名称 |

##### 选项

| 选项                    | 描述                                                                                 |
| ----------------------- | ------------------------------------------------------------------------------------ |
| `--path [path]`         | `tsconfig` 文件路径。<br/>别名：`-p`                                                 |
| `--config [path]`       | `nest-cli` 配置文件路径。<br/>别名：`-c`                                             |
| `--watch`               | 以监听模式（live-reload）运行。<br/>别名：`-w`                                       |
| `--builder [name]`      | 指定用于编译的构建器（可选 `tsc`、`swc` 或 `webpack`）。<br/>别名：`-b`              |
| `--preserveWatchOutput` | 在监听模式下保留旧的控制台输出，不清屏（仅适用于 `tsc` 监听模式）。                  |
| `--watchAssets`         | 以监听模式运行，监听非 TS 文件（如资源文件）。详见 [资源文件](cli/monorepo#assets)。 |
| `--debug [hostport]`    | 以调试模式运行（等同于 --inspect 标志）。<br/>别名：`-d`                             |
| `--webpack`             | 使用 webpack 进行编译（已弃用：请改用 `--builder webpack`）。                        |
| `--webpackPath`         | webpack 配置文件路径。                                                               |
| `--tsc`                 | 强制使用 `tsc` 进行编译。                                                            |
| `--exec [binary]`       | 指定运行的二进制文件（默认：`node`）。<br/>别名：`-e`                                |
| `--no-shell`            | 不在 shell 中生成子进程（详见 Node 的 `child_process.spawn()` 方法文档）。           |
| `--env-file`            | 从当前目录下的文件加载环境变量，使其可通过 `process.env` 获取。                      |
| `-- [key=value]`        | 可通过 `process.argv` 引用的命令行参数。                                             |

#### nest add

导入已打包为 **Nest 库（nest library）** 的库，并运行其安装原型（schematic）。

```bash
$ nest add <name> [options]
```

##### 参数

| 参数     | 描述           |
| -------- | -------------- |
| `<name>` | 要导入的库名称 |

#### nest info

显示已安装的 nest 包及其他有用的系统信息。例如：

```bash
$ nest info
```

```bash
 _   _             _      ___  _____  _____  _     _____
| \ | |           | |    |_  |/  ___|/  __ \| |   |_   _|
|  \| |  ___  ___ | |_     | |\ `--. | /  \/| |     | |
| . ` | / _ \/ __|| __|    | | `--. \| |    | |     | |
| |\  ||  __/\__ \| |_ /\__/ //\__/ /| \__/\| |_____| |_
\_| \_/ \___||___/ \__|\____/ \____/  \____/\_____/\___/

[系统信息]
操作系统版本：macOS High Sierra
NodeJS 版本：v20.18.0
[Nest 信息]
microservices 版本：10.0.0
websockets 版本：10.0.0
testing 版本：10.0.0
common 版本：10.0.0
core 版本：10.0.0
```
