### 库（Libraries）

许多应用程序需要解决相同的通用问题，或者在不同场景下复用模块化组件。Nest 提供了多种方式来应对这些需求，每种方式都在不同层面上解决问题，帮助实现不同的架构和组织目标。

Nest 的[模块](/modules)非常适合在单个应用程序内提供执行上下文（Execution Context），以便共享组件。模块还可以通过 [npm](https://npmjs.com) 进行打包，创建可在不同项目中安装和复用的库。这种方式非常适合分发可配置、可复用的库，使不同、松散关联或无关联的组织（例如分发/安装第三方库）都能使用。

对于在组织内部（如公司或项目组范围内）共享代码，采用更轻量的组件共享方式会更为高效。Monorepo 正是为此而生。在单体仓库中，**库**为代码共享提供了一种简单、轻量的方式。在 Nest 单体仓库结构下，使用库可以轻松组装共享组件的应用程序。实际上，这种方式鼓励将单体应用拆分为模块化组件，并推动开发流程专注于模块化组件的构建与组合。

#### Nest 库

Nest 库是指与应用程序不同的 Nest 项目类型，它本身无法独立运行。库必须被导入到某个应用程序中，其代码才能被执行。本节介绍的库相关内置支持仅适用于**Monorepo**（标准模式项目可通过 npm 包实现类似功能）。

例如，一个组织可能会开发一个 `AuthModule`，用于通过实现公司统一的身份验证策略来管理所有内部应用的身份验证。与其为每个应用单独构建该模块，或将代码打包为 npm 包并要求每个项目单独安装，不如在单体仓库中将该模块定义为库。这样组织后，所有库模块的使用者都能实时获取 `AuthModule` 的最新版本。此方式对于组件开发、组装的协作，以及端到端测试的简化都具有显著优势。

#### 创建库

任何适合复用的功能都可以被管理为库。决定哪些内容应当作为库，哪些应当作为应用的一部分，是一个架构设计决策。创建库不仅仅是将现有应用中的代码复制到一个新的库中。当代码被打包为库时，库的代码必须与应用解耦。这通常需要在前期投入**更多**时间，并且会促使你做出一些在紧耦合代码中不会遇到的设计决策。但这种额外的努力会带来回报，因为库可以在多个应用之间实现更快速的组装和复用。

要开始创建库，可以运行以下命令：

```bash
$ nest g library my-library
```

当你运行该命令时，`library` 原型生成器（Schematics）会提示你为库输入一个前缀（也称为别名）：

```bash
What prefix would you like to use for the library (default: @app)?
```

这会在你的工作空间中创建一个名为 `my-library` 的新项目。
库类型项目（Library-type project）与应用类型项目（Application-type project）一样，都是通过原型生成器生成到一个命名文件夹中。所有库都统一管理在多包仓库结构根目录下的 `libs` 文件夹中。Nest 在首次创建库时会自动生成 `libs` 文件夹。

为库生成的文件与为应用生成的文件略有不同。以下是在执行上述命令后 `libs` 文件夹的内容：

<div class="file-tree">
  <div class="item">libs</div>
  <div class="children">
    <div class="item">my-library</div>
    <div class="children">
      <div class="item">src</div>
      <div class="children">
        <div class="item">index.ts</div>
        <div class="item">my-library.module.ts</div>
        <div class="item">my-library.service.ts</div>
      </div>
      <div class="item">tsconfig.lib.json</div>
    </div>
  </div>
</div>

`nest-cli.json` 文件会在 `"projects"` 键下为该库新增一项：

```javascript
...
{
    "my-library": {
      "type": "library",
      "root": "libs/my-library",
      "entryFile": "index",
      "sourceRoot": "libs/my-library/src",
      "compilerOptions": {
        "tsConfigPath": "libs/my-library/tsconfig.lib.json"
      }
}
...
```

`nest-cli.json` 中库与应用的元数据有两个区别：

- `"type"` 属性被设置为 `"library"`，而不是 `"application"`
- `"entryFile"` 属性被设置为 `"index"`，而不是 `"main"`

这些差异使构建过程能够正确处理库。例如，库会通过 `index.js` 文件导出其功能。

与应用类型项目一样，每个库都有自己的 `tsconfig.lib.json` 文件，并且会继承根（整个多包仓库结构）下的 `tsconfig.json` 文件。你可以根据需要修改该文件，为库提供特定的编译选项。

你可以通过 CLI 命令构建该库：

```bash
$ nest build my-library
```

#### 使用库

在自动生成的配置文件就位后，使用库就变得非常简单。那么，如何在 `my-project` 应用中从 `my-library` 库导入 `MyLibraryService` 呢？

首先，需要注意的是，使用库模块与使用其他任何 Nest 模块是一样的。Monorepo 所做的工作，就是以一种让库导入和构建生成变得透明的方式来管理路径。要使用 `MyLibraryService`，我们需要导入其声明模块。可以如下修改 `my-project/src/app.module.ts`，导入 `MyLibraryModule`：

```typescript
import { Module } from '@nestjs/common'
import { AppController } from './app.controller'
import { AppService } from './app.service'
import { MyLibraryModule } from '@app/my-library'

@Module({
  imports: [MyLibraryModule],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
```

请注意，上述 ES 模块的 `import` 语句中使用了 `@app` 路径别名，这正是我们在前面通过 `nest g library` 命令指定的 `prefix`。在底层，Nest 通过 tsconfig 路径映射（path mapping）来实现这一点。当添加库时，Nest 会自动更新全局（monorepo）`tsconfig.json` 文件中的 `"paths"` 配置项，如下所示：

```javascript
"paths": {
    "@app/my-library": [
        "libs/my-library/src"
    ],
    "@app/my-library/*": [
        "libs/my-library/src/*"
    ]
}
```

简而言之，monorepo 与库功能的结合，让在应用中引入库模块变得简单直观。

同样的机制也支持构建和部署由多个库组成的应用。一旦导入了 `MyLibraryModule`，运行 `nest build` 命令时，所有模块解析都会自动处理，并将应用与所有库依赖一同打包，便于部署。monorepo 的默认编译器是 **webpack**，因此最终的分发文件会将所有已转译的 JavaScript 文件打包为一个单一文件。当然，你也可以按照 [这里](https://docs.nestjs.com/cli/monorepo#global-compiler-options) 的说明切换为 `tsc` 编译器。
