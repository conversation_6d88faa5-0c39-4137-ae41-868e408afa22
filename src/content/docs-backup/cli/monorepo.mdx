### 工作空间（Workspaces）

Nest 提供了两种代码组织模式：

- **标准模式（standard mode）**：适用于构建以单个项目为中心的应用程序。这类应用拥有各自的依赖和配置，无需针对模块共享或复杂构建进行优化。标准模式是默认模式。
- **多包仓库结构（monorepo mode）**：该模式将代码工件视为轻量级多包仓库结构（Monorepo）的一部分，更适合开发团队或多项目环境。它自动化部分构建流程，便于创建和组合模块化组件，促进代码复用，简化集成测试，方便共享如 `eslint` 规则等项目级工件和配置策略，并且比 GitHub 子模块等替代方案更易用。多包仓库结构模式引入了 **工作空间（Workspace）** 的概念，通过 `nest-cli.json` 文件来协调多包仓库中各组件的关系。

需要注意的是，几乎所有 Nest 的功能都与代码组织模式无关。**唯一**的区别在于项目的组合方式以及构建产物的生成方式。其他所有功能，包括命令行工具（CLI）、核心模块和附加模块，在两种模式下均可正常使用。

此外，你可以随时从 **标准模式** 切换到 **多包仓库结构模式**，因此可以等到明确需要某种模式带来的优势时再做决定。

#### 标准模式（Standard mode）

当你运行 `nest new` 命令时，Nest 命令行工具（Nest CLI）会使用内置原型生成器（schematic）为你创建一个新的 **项目（Project）**。具体操作如下：

1. 创建一个新文件夹，名称对应你在 `nest new` 命令中提供的 `name` 参数
2. 在该文件夹中生成一套默认文件，构成最小化的基础 Nest 应用。你可以在 [typescript-starter](https://github.com/nestjs/typescript-starter) 仓库中查看这些文件。
3. 额外生成如 `nest-cli.json`、`package.json` 和 `tsconfig.json` 等文件，用于配置和启用编译、测试及服务等工具。

从这里开始，你可以修改起始文件，添加新组件，安装依赖（如执行 `npm install`），并按照文档的其他部分继续开发你的应用。

#### Monorepo 模式

要启用 monorepo 模式，首先需要从 _标准模式_ 结构开始，然后添加 **项目（Project）**。一个项目可以是完整的 **应用（Application）**（通过命令 `nest generate app` 添加到工作空间（Workspace）），也可以是 **库（Library）**（通过命令 `nest generate library` 添加到工作空间）。我们将在下文详细介绍这两类项目组件。此处需要注意的关键点是，**向现有标准模式结构中添加项目的行为**，会将其**转换为 monorepo 模式**。下面我们来看一个示例。

如果我们运行：

```bash
$ nest new my-project
```

此时我们构建了一个 _标准模式_ 结构，其文件夹结构如下：

<div class="file-tree">
  <div class="item">node_modules</div>
  <div class="item">src</div>
  <div class="children">
    <div class="item">app.controller.ts</div>
    <div class="item">app.module.ts</div>
    <div class="item">app.service.ts</div>
    <div class="item">main.ts</div>
  </div>
  <div class="item">nest-cli.json</div>
  <div class="item">package.json</div>
  <div class="item">tsconfig.json</div>
  <div class="item">eslint.config.mjs</div>
</div>

我们可以通过如下方式将其转换为 monorepo 模式结构：

```bash
$ cd my-project
$ nest generate app my-app
```

此时，`nest` 会将现有结构转换为 **monorepo 模式** 结构。这会带来几个重要变化。文件夹结构现在如下所示：

<div class="file-tree">
  <div class="item">apps</div>
  <div class="children">
    <div class="item">my-app</div>
    <div class="children">
      <div class="item">src</div>
      <div class="children">
        <div class="item">app.controller.ts</div>
        <div class="item">app.module.ts</div>
        <div class="item">app.service.ts</div>
        <div class="item">main.ts</div>
      </div>
      <div class="item">tsconfig.app.json</div>
    </div>
    <div class="item">my-project</div>
    <div class="children">
      <div class="item">src</div>
      <div class="children">
        <div class="item">app.controller.ts</div>
        <div class="item">app.module.ts</div>
        <div class="item">app.service.ts</div>
        <div class="item">main.ts</div>
      </div>
      <div class="item">tsconfig.app.json</div>
    </div>
  </div>
  <div class="item">nest-cli.json</div>
  <div class="item">package.json</div>
  <div class="item">tsconfig.json</div>
  <div class="item">eslint.config.mjs</div>
</div>

`generate app` 原型生成器（Schematics）会对代码进行重组 —— 将每个 **应用（Application）** 项目移动到 `apps` 文件夹下，并在每个项目根目录下添加项目专属的 `tsconfig.app.json` 文件。我们最初的 `my-project` 应用现在成为 monorepo 的**默认项目（default project）**，并与刚刚添加的 `my-app` 作为同级项目，共同位于 `apps` 文件夹下。关于默认项目的内容我们将在下文介绍。

> error **警告** 只有遵循标准 Nest 项目结构的项目，才能成功将标准模式结构转换为 monorepo。具体来说，在转换过程中，原型生成器尝试将 `src` 和 `test` 文件夹迁移到根目录下的 `apps` 文件夹中。如果项目未采用该结构，转换过程可能会失败或产生不可预期的结果。

#### 工作空间项目

在多包仓库结构（Monorepo）中，使用工作空间（Workspace）的概念来管理其成员实体。工作空间由多个**项目（Project）**组成。一个项目可以是：

- **应用（Application）**：一个完整的 Nest 应用（Nest application），包含用于引导应用的 `main.ts` 文件。除了编译和构建方面的考虑外，工作空间中的应用类型项目在功能上与*标准模式*结构下的应用完全一致。
- **库（Library）**：库是一种用于封装通用功能（如模块、提供者、控制器等）的方式，可以在其他项目中复用。库本身无法独立运行，也没有 `main.ts` 文件。关于库的更多内容，请参见[此章节](/cli/libraries)。

所有工作空间都有一个**默认项目（default project）**（应为应用类型项目）。该项目由顶层 `nest-cli.json` 文件中的 `"root"` 属性定义，指向默认项目的根目录（更多详情见下文 [CLI 属性](/cli/monorepo#cli-properties)）。通常情况下，这个默认项目就是你最初创建的标准模式应用，之后通过 `nest generate app` 转换为多包仓库结构。当你按照这些步骤操作时，该属性会自动填充。

默认项目会被 `nest` 命令（如 `nest build` 和 `nest start`）在未指定项目名称时使用。

例如，在上述多包仓库结构中，运行：

```bash
$ nest start
```

会启动 `my-project` 应用。若要启动 `my-app`，则需使用：

```bash
$ nest start my-app
```

#### 应用（Application）

应用类型项目，或我们通常所说的"应用"，是可以运行和部署的完整 Nest 应用。你可以通过 `nest generate app` 命令生成一个应用类型项目。

该命令会自动生成项目骨架，包括 [typescript starter](https://github.com/nestjs/typescript-starter) 中的标准 `src` 和 `test` 文件夹。与标准模式不同的是，多包仓库结构下的应用项目不会包含任何包依赖（`package.json`）或其他项目配置文件（如 `.prettierrc` 和 `eslint.config.mjs`）。相反，依赖和配置文件在整个多包仓库范围内统一管理。

不过，原型生成器（schematic）会在项目根目录下生成一个项目专用的 `tsconfig.app.json` 文件。该配置文件会自动设置合适的构建选项，包括正确设置编译输出目录。该文件会继承顶层（多包仓库）`tsconfig.json` 文件，因此你可以在全局范围内管理设置，也可以在项目级别进行覆盖。

#### 库（Library）

如前所述，库类型项目，简称"库"，是需要被组合进应用后才能运行的 Nest 组件包。你可以通过 `nest generate library` 命令生成库类型项目。至于哪些内容适合放入库中，这是一个架构设计决策。我们会在[libraries](/cli/libraries)章节中详细讨论库的相关内容。

#### CLI 属性

Nest 会将组织、构建和部署标准项目及多包仓库结构的元数据保存在 `nest-cli.json` 文件中。Nest 会在你添加项目时自动补充和更新该文件，因此你通常无需手动编辑它。不过，有些设置你可能需要手动调整，因此了解该文件的结构会很有帮助。

在按照上述步骤创建多包仓库结构后，我们的 `nest-cli.json` 文件如下所示：

```javascript
{
  "collection": "@nestjs/schematics",
  "sourceRoot": "apps/my-project/src",
  "monorepo": true,
  "root": "apps/my-project",
  "compilerOptions": {
    "webpack": true,
    "tsConfigPath": "apps/my-project/tsconfig.app.json"
  },
  "projects": {
    "my-project": {
      "type": "application",
      "root": "apps/my-project",
      "entryFile": "main",
      "sourceRoot": "apps/my-project/src",
      "compilerOptions": {
        "tsConfigPath": "apps/my-project/tsconfig.app.json"
      }
    },
    "my-app": {
      "type": "application",
      "root": "apps/my-app",
      "entryFile": "main",
      "sourceRoot": "apps/my-app/src",
      "compilerOptions": {
        "tsConfigPath": "apps/my-app/tsconfig.app.json"
      }
    }
  }
}
```

该文件分为以下几个部分：

- 全局部分，包含用于控制标准项目和多包仓库结构的顶层属性
- 顶层属性（`"projects"`），包含每个项目的元数据。该部分仅在多包仓库结构中存在。

顶层属性说明如下：

- `"collection"`：指定用于生成组件的原型生成器（Schematics）集合，通常无需更改该值
- `"sourceRoot"`：指定标准模式结构下单一项目的源代码根目录，或多包仓库结构下 _默认项目_ 的源代码根目录
- `"compilerOptions"`：一个映射对象，键为编译器选项名称，值为对应的设置；详细内容见下文
- `"generateOptions"`：一个映射对象，键为全局生成选项名称，值为对应的设置；详细内容见下文
- `"monorepo"`：（仅多包仓库结构）对于多包仓库结构，该值始终为 `true`
- `"root"`：（仅多包仓库结构）指向 _默认项目_ 的项目根目录

#### 全局编译器选项

这些属性用于指定要使用的编译器，以及影响**任何**编译步骤的各种选项，无论是通过 `nest build` 还是 `nest start`，也无论使用的是 `tsc` 还是 webpack。

| 属性名              | 属性值类型    | 说明                                                                                                                                                                                                                                                              |
| ------------------- | ------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `webpack`           | boolean       | 如果为 `true`，则使用 [webpack 编译器](https://webpack.js.org/)。如果为 `false` 或未设置，则使用 `tsc`。在多包仓库结构（monorepo）模式下，默认值为 `true`（使用 webpack），在标准模式下，默认值为 `false`（使用 `tsc`）。详情见下文。（已弃用：请改用 `builder`） |
| `tsConfigPath`      | string        | （**仅限多包仓库结构**）指向包含 `tsconfig.json` 设置的文件，当在未指定 `project` 选项的情况下调用 `nest build` 或 `nest start` 时（例如，构建或启动默认项目时）会使用该文件。                                                                                    |
| `webpackConfigPath` | string        | 指定 webpack 配置文件路径。如果未指定，Nest 会查找 `webpack.config.js` 文件。详情见下文。                                                                                                                                                                         |
| `deleteOutDir`      | boolean       | 如果为 `true`，每当编译器被调用时，会首先移除编译输出目录（在 `tsconfig.json` 中配置，默认是 `./dist`）。                                                                                                                                                         |
| `assets`            | array         | 启用后，每当编译步骤开始时会自动分发非 TypeScript 资源（在 `--watch` 增量编译模式下不会分发资源）。详情见下文。                                                                                                                                                   |
| `watchAssets`       | boolean       | 如果为 `true`，以 watch 模式运行，监视**所有**非 TypeScript 资源。（如需更细粒度的资源监控，请参见下方 [资源（Assets）](cli/monorepo#assets) 部分。）                                                                                                             |
| `manualRestart`     | boolean       | 如果为 `true`，启用 `rs` 快捷命令以手动重启服务器。默认值为 `false`。                                                                                                                                                                                             |
| `builder`           | string/object | 指定 CLI 使用哪种 `builder` 编译项目（`tsc`、`swc` 或 `webpack`）。如需自定义 builder 行为，可传递一个包含 `type`（`tsc`、`swc` 或 `webpack`）和 `options` 两个属性的对象。                                                                                       |
| `typeCheck`         | boolean       | 如果为 `true`，则为 SWC 驱动的项目启用类型检查（当 `builder` 为 `swc` 时）。默认值为 `false`。                                                                                                                                                                    |

#### 全局生成选项

这些属性用于指定 `nest generate` 命令的默认生成选项。

| 属性名 | 属性值类型        | 说明                                                                                                                                                                                                                                              |
| ------ | ----------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `spec` | boolean 或 object | 如果为 boolean，`true` 表示默认启用 spec 文件生成，`false` 表示禁用。命令行参数或项目级 `generateOptions` 设置会覆盖该值（见下文）。如果为对象，每个 key 代表一个原型生成器（schematic）名称，boolean 值决定该 schematic 是否默认生成 spec 文件。 |
| `flat` | boolean           | 如果为 true，所有生成命令将生成扁平结构。                                                                                                                                                                                                         |

以下示例使用 boolean 值指定所有项目默认禁用 spec 文件生成：

```javascript
{
  "generateOptions": {
    "spec": false
  },
  ...
}
```

以下示例使用 boolean 值指定所有项目默认生成扁平结构：

```javascript
{
  "generateOptions": {
    "flat": true
  },
  ...
}
```

在下例中，仅为 `service` 原型生成器（如 `nest generate service...`）禁用 spec 文件生成：

```javascript
{
  "generateOptions": {
    "spec": {
      "service": false
    }
  },
  ...
}
```

> warning **警告** 当 `spec` 以对象形式指定时，生成 schematic 的 key 目前不支持自动别名处理。这意味着如果只指定了 `service: false`，但通过别名 `s` 生成 service，仍会生成 spec 文件。为确保命令名和别名都按预期工作，请同时指定命令名和别名，如下所示：
>
> ```javascript
> {
>   "generateOptions": {
>     "spec": {
>       "service": false,
>       "s": false
>     }
>   },
>   ...
> }
> ```

#### 项目专属 generate 选项

除了可以设置全局 generate 选项外，你还可以为每个项目单独指定 generate 选项。项目专属 generate 选项的格式与全局 generate 选项完全一致，但直接写在每个项目下。

项目专属 generate 选项会覆盖全局 generate 选项。

```javascript
{
  "projects": {
    "cats-project": {
      "generateOptions": {
        "spec": {
          "service": false
        }
      },
      ...
    }
  },
  ...
}
```

> warning **警告** generate 选项的优先级顺序如下：命令行中通过 CLI 指定的选项优先级最高，其次是项目专属选项，最后是全局选项。也就是说，命令行选项会覆盖项目专属选项，项目专属选项会覆盖全局选项。

#### 指定编译器

之所以默认编译器会有所不同，是因为对于大型项目（例如在多包仓库结构中更常见），webpack 在构建速度和将所有项目组件打包为单一文件方面具有显著优势。如果你希望生成单独的文件，可以将 "webpack" 设置为 `false`，这样构建过程会使用 `tsc`（或 `swc`）。

#### Webpack 选项

webpack 配置文件可以包含标准的 [webpack 配置选项](https://webpack.js.org/configuration/)。例如，如果你希望 webpack 打包 `node_modules`（默认情况下会被排除），可以在 `webpack.config.js` 中添加如下内容：

```javascript
module.exports = {
  externals: [],
}
```

由于 webpack 配置文件本质上是一个 JavaScript 文件，你甚至可以导出一个函数，该函数接收默认选项并返回修改后的对象：

```javascript
module.exports = function (options) {
  return {
    ...options,
    externals: [],
  }
}
```

#### 资源文件

TypeScript 编译会自动将编译输出（`.js` 和 `.d.ts` 文件）分发到指定的输出目录。同时，也可以方便地分发非 TypeScript 文件，例如 `.graphql` 文件、图片、`.html` 文件以及其他资源文件。这样，你可以将 `nest build`（以及任何初始编译步骤）视为一个轻量级的**开发构建**步骤，在该阶段你可以编辑非 TypeScript 文件，并进行迭代编译和测试。

资源文件应位于 `src` 文件夹下，否则不会被复制。

`assets` 键的值应为一个数组，每个元素用于指定需要分发的文件。元素可以是带有 `glob` 风格文件匹配的简单字符串，例如：

```typescript
"assets": ["**/*.graphql"],
"watchAssets": true,
```

如果需要更精细的控制，元素也可以是包含以下键的对象：

- `"include"`：用于指定需要分发的资源文件的 `glob` 风格文件匹配规则
- `"exclude"`：用于指定需要从 `include` 列表中**排除**的资源文件的 `glob` 风格文件匹配规则
- `"outDir"`：字符串类型，指定资源文件分发的路径（相对于根目录）。默认为与编译输出相同的输出目录。
- `"watchAssets"`：布尔值；如果为 `true`，则以监听模式运行，监控指定的资源文件

例如：

```typescript
"assets": [
  { "include": "**/*.graphql", "exclude": "**/omitted.graphql", "watchAssets": true },
]
```

> warning **警告** 如果在顶层 `compilerOptions` 属性中设置了 `watchAssets`，会覆盖 `assets` 属性中任何 `watchAssets` 的设置。

#### 项目属性

此元素仅存在于多包仓库结构中。通常不建议修改这些属性，因为 Nest 会使用它们来定位各个项目及其在多包仓库中的配置选项。
