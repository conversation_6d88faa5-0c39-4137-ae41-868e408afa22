### 管道（Pipe）

管道是一个带有 `@Injectable()` 装饰器的类，并实现了 `PipeTransform` 接口。

<figure>
  <img class="illustrative-image" src="/assets/Pipe_1.png" />
</figure>

管道有两个典型的使用场景：

- **转换**：将输入数据转换为期望的格式（例如，将字符串转换为整数）
- **校验**：校验输入数据，如果数据有效则原样传递，否则抛出异常

在这两种情况下，管道都是对 <a href="controllers#route-parameters">控制器路由处理器</a> 正在处理的 `参数` 进行操作。Nest 会在方法调用前插入管道，管道接收即将传递给方法的参数并进行处理。任何转换或校验操作都会在此时发生，随后路由处理器会接收到（可能已被转换的）参数。

Nest 提供了多种内置管道（Pipe），可直接使用。你也可以自定义管道。本章将介绍内置管道的用法及其绑定方式，并通过几个自定义管道的例子，展示如何从零构建一个管道。

> info **提示** 管道在异常区域（exceptions zone）内运行。这意味着当管道抛出异常时，会被异常层（全局异常过滤器和当前上下文应用的 [异常过滤器](/exception-filters)）处理。由此可见，当管道抛出异常时，控制器方法不会被执行。这为在系统边界校验外部输入数据提供了最佳实践。

#### 内置管道

Nest 提供了以下内置管道：

- `ValidationPipe`
- `ParseIntPipe`
- `ParseFloatPipe`
- `ParseBoolPipe`
- `ParseArrayPipe`
- `ParseUUIDPipe`
- `ParseEnumPipe`
- `DefaultValuePipe`
- `ParseFilePipe`
- `ParseDatePipe`

这些管道均从 `@nestjs/common` 包中导出。

下面以 `ParseIntPipe` 为例，演示**转换**场景。该管道确保方法处理器参数被转换为 JavaScript 整数（如果转换失败则抛出异常）。本章后面还会展示一个简单的自定义 `ParseIntPipe` 实现。下述用法同样适用于其他内置转换管道（如 `ParseBoolPipe`、`ParseFloatPipe`、`ParseEnumPipe`、`ParseArrayPipe`、`ParseDatePipe`、`ParseUUIDPipe`，本章统称为 `Parse*` 管道）。

#### 绑定管道

要使用管道，需要将管道类的实例绑定到合适的上下文。以 `ParseIntPipe` 为例，我们希望将其绑定到某个路由处理器方法，并确保在方法调用前执行。可以通过如下方式在方法参数级别绑定管道：

```typescript
@Get(':id')
async findOne(@Param('id', ParseIntPipe) id: number) {
  return this.catsService.findOne(id);
}
```

这样可以确保以下两种情况之一成立：要么 `findOne()` 方法接收到的参数是数字（如预期），要么在调用路由处理器前抛出异常。

例如，假设路由被如下调用：

```bash
GET localhost:3000/abc
```

Nest 会抛出如下异常：

```json
{
  "statusCode": 400,
  "message": "Validation failed (numeric string is expected)",
  "error": "Bad Request"
}
```

此异常会阻止 `findOne()` 方法体的执行。

在上述例子中，我们传递的是类（`ParseIntPipe`），而不是实例，这样框架会负责实例化，并支持依赖注入。和守卫、拦截器类似，也可以直接传递实例，适用于需要自定义内置管道行为（如传递选项）的场景：

```typescript
@Get(':id')
async findOne(
  @Param('id', new ParseIntPipe({ errorHttpStatusCode: HttpStatus.NOT_ACCEPTABLE }))
  id: number,
) {
  return this.catsService.findOne(id);
}
```

其他转换管道（所有 `Parse*` 管道）的绑定方式类似。这些管道可用于校验路由参数、查询参数和请求体参数。

例如，绑定到查询参数：

```typescript
@Get()
async findOne(@Query('id', ParseIntPipe) id: number) {
  return this.catsService.findOne(id);
}
```

下面是使用 `ParseUUIDPipe` 解析字符串参数并校验其是否为 UUID 的例子：

```typescript
@@filename()
@Get(':uuid')
async findOne(@Param('uuid', new ParseUUIDPipe()) uuid: string) {
  return this.catsService.findOne(uuid);
}
@@switch
@Get(':uuid')
@Bind(Param('uuid', new ParseUUIDPipe()))
async findOne(uuid) {
  return this.catsService.findOne(uuid);
}
```

> info **提示** 使用 `ParseUUIDPipe()` 时，默认支持解析 3、4、5 版本的 UUID。如果只需特定版本，可在管道选项中指定。

上面展示了 `Parse*` 系列内置管道的绑定方式。校验管道的绑定略有不同，详见下节。

> info **提示** 更多校验管道用法详见 [校验技术](/techniques/validation)。

#### 自定义管道

如前所述，你可以自定义管道。虽然 Nest 已内置了强大的 `ParseIntPipe` 和 `验证管道（ValidationPipe）`，但我们还是从零实现一个简单版本，帮助理解自定义管道的构建方式。

我们先实现一个简单的 `验证管道（ValidationPipe）`。初始版本仅接收输入值并原样返回，相当于恒等函数。

```typescript
@@filename(validation.pipe)
import { PipeTransform, Injectable, ArgumentMetadata } from '@nestjs/common';

@Injectable()
export class ValidationPipe implements PipeTransform {
  transform(value: any, metadata: ArgumentMetadata) {
    return value;
  }
}
@@switch
import { Injectable } from '@nestjs/common';

@Injectable()
export class ValidationPipe {
  transform(value, metadata) {
    return value;
  }
}
```

> info **提示** `PipeTransform<T, R>` 是一个泛型接口，所有管道都必须实现。`T` 表示输入值类型，`R` 表示 `transform()` 方法的返回类型。

每个管道都必须实现 `transform()` 方法以满足 `PipeTransform` 接口契约。该方法有两个参数：

- `value`：当前处理的方法参数（在被路由处理器接收前）
- `metadata`：当前处理参数的元数据对象，包含以下属性：

```typescript
export interface ArgumentMetadata {
  type: 'body' | 'query' | 'param' | 'custom'
  metatype?: Type<unknown>
  data?: string
}
```

这些属性描述了当前处理的参数。

<table>
  <tr>
    <td>
      <code>type</code>
    </td>
    <td>
      指示参数类型：请求体 <code>@Body()</code>、查询参数 <code>@Query()</code>、路由参数{' '}
      <code>@Param()</code> 或自定义参数（详见 <a routerLink="/custom-decorators">自定义装饰器</a>
      ）。
    </td>
  </tr>
  <tr>
    <td>
      <code>metatype</code>
    </td>
    <td>
      参数的元类型，例如 <code>String</code>。注意：如果方法参数未声明类型，或使用原生
      JavaScript，则该值为 <code>undefined</code>。
    </td>
  </tr>
  <tr>
    <td>
      <code>data</code>
    </td>
    <td>
      传递给装饰器的字符串，例如 <code>@Body('string')</code>。如果装饰器括号为空，则为{' '}
      <code>undefined</code>。
    </td>
  </tr>
</table>

> warning **警告** TypeScript 接口在转译后会消失。因此，如果方法参数类型声明为接口而非类，`metatype` 的值会是 `Object`。

#### 基于模式的校验

让我们让校验管道更有用些。以 `CatsController` 的 `create()` 方法为例，我们通常希望在调用服务方法前，确保请求体对象有效。

```typescript
@@filename()
@Post()
async create(@Body() createCatDto: CreateCatDto) {
  this.catsService.create(createCatDto);
}
@@switch
@Post()
async create(@Body() createCatDto) {
  this.catsService.create(createCatDto);
}
```

这里的 `createCatDto` 参数类型为 `CreateCatDto`：

```typescript
@@filename(create-cat.dto)
export class CreateCatDto {
  name: string;
  age: number;
  breed: string;
}
```

我们希望确保所有对 create 方法的请求体都包含有效的对象，因此需要校验 `createCatDto` 的三个成员。虽然可以在路由处理器内部校验，但这样会破坏**单一职责原则**（SRP）。

另一种做法是创建**校验器类**并在每个方法开头调用，但这样容易遗漏。

也可以考虑用中间件实现校验，但中间件无法感知**执行上下文**（即将被调用的处理器及其参数），因此无法实现通用校验。

这正是管道的用武之地。下面我们来完善校验管道。

<app-banner-courses></app-banner-courses>

#### 对象模式校验

有多种方式可以优雅地实现对象校验，常见做法是**基于模式（schema-based）**的校验。我们以 [Zod](https://zod.dev/) 库为例，构建一个基于 Zod 的校验管道。

首先安装依赖：

```bash
$ npm install --save zod
```

下面的代码定义了一个接收 schema 的管道类，并用 `schema.parse()` 校验参数。

如前所述，**校验管道**要么返回原值，要么抛出异常。

下节将介绍如何通过 `@UsePipes()` 装饰器为控制器方法传递 schema，使校验管道可复用。

```typescript
@@filename()
import { PipeTransform, ArgumentMetadata, BadRequestException } from '@nestjs/common';
import { ZodSchema  } from 'zod';

export class ZodValidationPipe implements PipeTransform {
  constructor(private schema: ZodSchema) {}

  transform(value: unknown, metadata: ArgumentMetadata) {
    try {
      const parsedValue = this.schema.parse(value);
      return parsedValue;
    } catch (error) {
      throw new BadRequestException('Validation failed');
    }
  }
}
@@switch
import { BadRequestException } from '@nestjs/common';

export class ZodValidationPipe {
  constructor(private schema) {}

  transform(value, metadata) {
    try {
      const parsedValue = this.schema.parse(value);
      return parsedValue;
    } catch (error) {
      throw new BadRequestException('Validation failed');
    }
  }
}

```

#### 绑定校验管道

前文介绍了转换管道（如 `ParseIntPipe`）的绑定方式。

校验管道的绑定同样非常简单。

以 `ZodValidationPipe` 为例，使用步骤如下：

1. 创建 `ZodValidationPipe` 实例
2. 在管道构造函数中传入对应的 Zod schema
3. 将管道绑定到方法

Zod schema 示例：

```typescript
import { z } from 'zod'

export const createCatSchema = z
  .object({
    name: z.string(),
    age: z.number(),
    breed: z.string(),
  })
  .required()

export type CreateCatDto = z.infer<typeof createCatSchema>
```

通过 `@UsePipes()` 装饰器绑定管道：

```typescript
@@filename(cats.controller)
@Post()
@UsePipes(new ZodValidationPipe(createCatSchema))
async create(@Body() createCatDto: CreateCatDto) {
  this.catsService.create(createCatDto);
}
@@switch
@Post()
@Bind(Body())
@UsePipes(new ZodValidationPipe(createCatSchema))
async create(createCatDto) {
  this.catsService.create(createCatDto);
}
```

> info **提示** `@UsePipes()` 装饰器需从 `@nestjs/common` 包中导入。

> warning **警告** `zod` 库要求在 `tsconfig.json` 文件中启用 `strictNullChecks` 配置。

#### 类装饰器校验器（Class validator）

> warning **警告** 本节介绍的技术仅适用于 TypeScript，如果你的应用是用原生 JavaScript 编写，则无法使用。

下面我们来看一种替代的校验实现方式。

Nest 与 [class-validator](https://github.com/typestack/class-validator) 库配合良好。这个功能强大的库允许你使用装饰器（Decorator）进行校验。装饰器驱动的校验方式非常强大，尤其是结合 Nest 的**管道（Pipe）**能力时，因为我们可以访问被处理属性的 `metatype`。在开始之前，需要安装相关依赖：

```bash
$ npm i --save class-validator class-transformer
```

安装完成后，我们可以在 `CreateCatDto` 类上添加一些装饰器。这样做的一个显著优势是：`CreateCatDto` 类本身就成为了 Post 请求体对象的唯一数据源（不需要单独创建校验类）。

```typescript
@@filename(create-cat.dto)
import { IsString, IsInt } from 'class-validator';

export class CreateCatDto {
  @IsString()
  name: string;

  @IsInt()
  age: number;

  @IsString()
  breed: string;
}
```

> info **提示** 更多 class-validator 装饰器的用法请参见 [官方文档](https://github.com/typestack/class-validator#usage)。

现在我们可以实现一个基于这些注解的 `验证管道（ValidationPipe）` 类：

```typescript
@@filename(validation.pipe)
import { PipeTransform, Injectable, ArgumentMetadata, BadRequestException } from '@nestjs/common';
import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class ValidationPipe implements PipeTransform<any> {
  async transform(value: any, { metatype }: ArgumentMetadata) {
    if (!metatype || !this.toValidate(metatype)) {
      return value;
    }
    const object = plainToInstance(metatype, value);
    const errors = await validate(object);
    if (errors.length > 0) {
      throw new BadRequestException('Validation failed');
    }
    return value;
  }

  private toValidate(metatype: Function): boolean {
    const types: Function[] = [String, Boolean, Number, Array, Object];
    return !types.includes(metatype);
  }
}
```

> info **提示** 需要注意的是，你无需自己实现通用的验证管道（ValidationPipe），因为 Nest 已经内置了 `ValidationPipe`。本章示例仅为演示自定义管道的机制，Nest 内置的 `ValidationPipe` 功能更丰富，详细用法和示例请参见[这里](/techniques/validation)。

> warning **注意** 上述代码中我们使用了 [class-transformer](https://github.com/typestack/class-transformer) 库，该库与 **class-validator** 同为一位作者开发，因此两者配合非常默契。

下面我们来逐步解析这段代码。首先，注意 `transform()` 方法被标记为 `async`。Nest 支持同步和**异步**管道（Pipe），之所以这里用 `async`，是因为 class-validator 的部分校验[可能是异步的](https://github.com/typestack/class-validator#custom-validation-classes)（即返回 Promise）。

接下来，代码通过解构赋值直接获取 `ArgumentMetadata` 的 `metatype` 字段，这只是简写写法，等价于先获取完整对象再单独赋值。

然后，`toValidate()` 辅助方法用于判断当前参数类型是否为原生 JavaScript 类型（如 String、Boolean、Number、Array、Object），如果是则跳过校验，因为这些类型无法添加校验装饰器。

接着，使用 class-transformer 的 `plainToInstance()` 方法将普通 JavaScript 对象转换为带类型的对象，以便应用校验。原因在于，网络请求反序列化后的对象没有类型信息（这是底层平台如 Express 的行为），而 class-validator 需要依赖我们在 DTO 上定义的装饰器，因此必须进行类型转换。

最后，作为**验证管道（ValidationPipe）**，要么返回原值，要么抛出异常。

最后一步是绑定 `ValidationPipe`。管道可以作用于参数级、方法级、控制器级或全局级。前文 Zod 校验管道的例子展示了方法级绑定。下面的例子展示了如何将管道实例绑定到路由处理器的 `@Body()` 装饰器，从而对 post body 进行校验：

```typescript
@@filename(cats.controller)
@Post()
async create(
  @Body(new ValidationPipe()) createCatDto: CreateCatDto,
) {
  this.catsService.create(createCatDto);
}
```

参数级管道适用于只需校验某个特定参数的场景。

#### 全局作用域管道（Global scoped pipes）

由于 `ValidationPipe` 设计为通用管道，我们可以将其设置为**全局管道（global-scoped pipe）**，使其应用于整个应用的所有路由处理器。

```typescript
@@filename(main)
async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.useGlobalPipes(new ValidationPipe());
  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
```

> warning **注意** 如果是 <a href="faq/hybrid-application">混合应用（hybrid apps）</a>，`useGlobalPipes()` 方法不会为网关（gateway）和微服务（microservices）设置管道。对于"标准"微服务应用，`useGlobalPipes()` 会全局挂载管道。

全局管道会应用于整个应用的所有控制器和路由处理器。

需要注意的是，从模块外部（如上例通过 `useGlobalPipes()`）注册的全局管道，由于绑定发生在任何模块上下文之外，因此**无法进行依赖注入（Dependency Injection）**。为了解决这个问题，可以通过如下方式**直接在某个模块内设置全局管道**：

```typescript
@@filename(app.module)
import { Module } from '@nestjs/common';
import { APP_PIPE } from '@nestjs/core';

@Module({
  providers: [
    {
      provide: APP_PIPE,
      useClass: ValidationPipe,
    },
  ],
})
export class AppModule {}
```

> info **提示** 当使用此方法为管道执行依赖注入时，请注意，无论在哪个模块中使用此构造，管道实际上都是全局性的。应该在哪里执行此操作？选择定义管道的模块。此外，`useClass` 不是处理自定义提供程序注册的唯一方法。了解更多 [这里](/fundamentals/custom-providers)。

#### 内置 ValidationPipe

需要再次提醒的是，你无需自行实现通用的验证管道（ValidationPipe），因为 Nest 已经开箱即用地提供了 `ValidationPipe`。内置的 `ValidationPipe` 提供了比本章示例更多的选项，本章的实现仅用于演示自定义管道的基本机制。完整用法和丰富示例请参见[这里](/techniques/validation)。

#### 转换（Transformation）用例

自定义管道（Pipe）的用途不仅限于校验。在本章开头我们提到，管道还可以**转换**输入数据为期望的格式。这是因为 `transform` 方法的返回值会完全覆盖参数的原始值。

这种能力在什么场景下有用？有时，客户端传递的数据需要经过一定的转换（例如将字符串转换为整数），才能被路由处理器正确处理。此外，某些必需的数据字段可能会缺失，我们希望为其设置默认值。**转换管道（Transformation pipe）**可以通过在客户端请求和请求处理器之间插入处理逻辑来实现这些功能。

下面是一个简单的 `ParseIntPipe`，用于将字符串解析为整数。（如前所述，Nest 内置的 `ParseIntPipe` 功能更完善，这里仅作为自定义转换管道的简单示例。）

```typescript
@@filename(parse-int.pipe)
import { PipeTransform, Injectable, ArgumentMetadata, BadRequestException } from '@nestjs/common';

@Injectable()
export class ParseIntPipe implements PipeTransform<string, number> {
  transform(value: string, metadata: ArgumentMetadata): number {
    const val = parseInt(value, 10);
    if (isNaN(val)) {
      throw new BadRequestException('Validation failed');
    }
    return val;
  }
}
@@switch
import { Injectable, BadRequestException } from '@nestjs/common';

@Injectable()
export class ParseIntPipe {
  transform(value, metadata) {
    const val = parseInt(value, 10);
    if (isNaN(val)) {
      throw new BadRequestException('Validation failed');
    }
    return val;
  }
}
```

我们可以如下将该管道绑定到指定参数上：

```typescript
@@filename()
@Get(':id')
async findOne(@Param('id', new ParseIntPipe()) id) {
  return this.catsService.findOne(id);
}
@@switch
@Get(':id')
@Bind(Param('id', new ParseIntPipe()))
async findOne(id) {
  return this.catsService.findOne(id);
}
```

另一个常见的转换场景是：根据请求中的 id，从数据库中查找**已存在的用户**实体：

```typescript
@@filename()
@Get(':id')
findOne(@Param('id', UserByIdPipe) userEntity: UserEntity) {
  return userEntity;
}
@@switch
@Get(':id')
@Bind(Param('id', UserByIdPipe))
findOne(userEntity) {
  return userEntity;
}
```

这个管道的具体实现留给读者练习。需要注意的是，和其他转换管道一样，它接收一个输入值（如 id），返回一个输出值（如 UserEntity 对象）。这样可以将样板代码从处理器中抽离到通用管道中，使代码更加声明式并遵循 [DRY](https://en.wikipedia.org/wiki/Don%27t_repeat_yourself) 原则。

#### 提供默认值

`Parse*` 管道要求参数值必须已定义。如果收到 `null` 或 `undefined`，会抛出异常。为了让接口能够处理缺失的查询参数值，我们需要在 `Parse*` 管道处理前注入一个默认值。`DefaultValuePipe` 就是为此设计的。只需在 `@Query()` 装饰器中，将 `DefaultValuePipe` 实例放在对应的 `Parse*` 管道之前即可，如下所示：

```typescript
@@filename()
@Get()
async findAll(
  @Query('activeOnly', new DefaultValuePipe(false), ParseBoolPipe) activeOnly: boolean,
  @Query('page', new DefaultValuePipe(0), ParseIntPipe) page: number,
) {
  return this.catsService.findAll({ activeOnly, page });
}
```
