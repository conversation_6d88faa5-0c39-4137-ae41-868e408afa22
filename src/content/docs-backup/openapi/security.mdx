### 安全性（Security）

要为特定操作指定应使用哪些安全机制，可以使用 `@ApiSecurity()` 装饰器。

```typescript
@ApiSecurity('basic')
@Controller('cats')
export class CatsController {}
```

在运行应用程序之前，请记得通过 `DocumentBuilder` 向基础文档添加安全性定义：

```typescript
const options = new DocumentBuilder().addSecurity('basic', {
  type: 'http',
  scheme: 'basic',
})
```

部分常用的身份验证（Authentication）技术（如 `basic` 和 `bearer`）已内置，因此无需像上面那样手动定义安全机制。

#### 基本身份验证（Basic authentication）

要启用基本身份验证，可以使用 `@ApiBasicAuth()`。

```typescript
@ApiBasicAuth()
@Controller('cats')
export class CatsController {}
```

在运行应用程序之前，请记得通过 `DocumentBuilder` 向基础文档添加安全性定义：

```typescript
const options = new DocumentBuilder().addBasicAuth()
```

#### Bearer 身份验证（Bearer authentication）

要启用 Bearer 身份验证，可以使用 `@ApiBearerAuth()`。

```typescript
@ApiBearerAuth()
@Controller('cats')
export class CatsController {}
```

在运行应用程序之前，请记得通过 `DocumentBuilder` 向基础文档添加安全性定义：

```typescript
const options = new DocumentBuilder().addBearerAuth()
```

#### OAuth2 身份验证（OAuth2 authentication）

要启用 OAuth2，可以使用 `@ApiOAuth2()`。

```typescript
@ApiOAuth2(['pets:write'])
@Controller('cats')
export class CatsController {}
```

在运行应用程序之前，请记得通过 `DocumentBuilder` 向基础文档添加安全性定义：

```typescript
const options = new DocumentBuilder().addOAuth2()
```

#### Cookie 身份验证（Cookie authentication）

要启用 Cookie 身份验证，可以使用 `@ApiCookieAuth()`。

```typescript
@ApiCookieAuth()
@Controller('cats')
export class CatsController {}
```

在运行应用程序之前，请记得通过 `DocumentBuilder` 向基础文档添加安全性定义：

```typescript
const options = new DocumentBuilder().addCookieAuth('optional-session-id')
```
