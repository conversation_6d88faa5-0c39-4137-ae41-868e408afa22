### CLI 插件

[TypeScript](https://www.typescriptlang.org/docs/handbook/decorators.html) 的元数据反射系统存在一些限制，例如无法确定一个类包含哪些属性，或识别某个属性是可选还是必填。不过，其中部分限制可以在编译阶段通过插件解决。Nest 提供了一个插件，用于增强 TypeScript 的编译过程，从而减少模板代码的编写量。

> info **提示** 该插件为**可选使用**（opt-in）。如果你愿意，也可以手动为所有属性声明装饰器，或者只在需要的地方声明特定装饰器。

#### 概述

Swagger 插件会自动完成以下操作：

- 为所有数据传输对象属性添加 `@ApiProperty` 装饰器，除非使用了 `@ApiHideProperty`
- 根据问号（如 `name?: string` 会设置 `required: false`）自动设置 `required` 属性
- 根据类型自动设置 `type` 或 `enum` 属性（也支持数组类型）
- 根据赋予的默认值自动设置 `default` 属性
- 如果 `classValidatorShim` 设为 `true`，会根据 `class-validator` 装饰器自动添加多项校验规则
- 为每个端点自动添加响应装饰器，包含正确的状态码和 `type`（响应模型）
- 如果 `introspectComments` 设为 `true`，会根据注释为属性和端点生成描述信息
- 如果 `introspectComments` 设为 `true`，会根据注释为属性生成示例值

请注意，文件名**必须**以以下后缀之一结尾：`['.dto.ts', '.entity.ts']`（例如：`create-user.dto.ts`），插件才会分析该文件。

如果你使用了不同的后缀，可以通过指定 `dtoFileNameSuffix` 选项（见下文）来调整插件行为。

过去，如果你希望在 Swagger UI 中提供交互式体验，通常需要重复编写大量代码，以便让相关包知道你的模型/组件应如何在规范中声明。例如，你可以这样定义一个简单的 `CreateUserDto` 类：

```typescript
export class CreateUserDto {
  @ApiProperty()
  email: string

  @ApiProperty()
  password: string

  @ApiProperty({ enum: RoleEnum, default: [], isArray: true })
  roles: RoleEnum[] = []

  @ApiProperty({ required: false, default: true })
  isEnabled?: boolean = true
}
```

在中等规模项目中这或许不是大问题，但当类数量增多时，这种方式会变得冗长且难以维护。

通过[启用 Swagger 插件](/openapi/cli-plugin#using-the-cli-plugin)，上述类定义可以简化为：

```typescript
export class CreateUserDto {
  email: string
  password: string
  roles: RoleEnum[] = []
  isEnabled?: boolean = true
}
```

> info **注意** Swagger 插件会根据 TypeScript 类型和 class-validator 装饰器自动推断 `@ApiProperty()` 注解。这有助于为自动生成的 Swagger UI 文档清晰描述你的 API。不过，运行时的校验仍需依赖 class-validator 装饰器。因此，像 `IsEmail()`、`IsNumber()` 等校验器依然需要继续使用。

因此，如果你希望依赖自动注解来生成文档，同时又需要运行时校验，class-validator 装饰器依然是必需的。

> info **提示** 在 DTO 中使用 [映射类型工具](https://docs.nestjs.com/openapi/mapped-types)（如 `PartialType`）时，请从 `@nestjs/swagger` 而非 `@nestjs/mapped-types` 导入，以便插件能够正确识别并生成 schema。

该插件会基于**抽象语法树（Abstract Syntax Tree，AST）**动态添加合适的装饰器，因此你无需在代码中到处手动添加 `@ApiProperty` 装饰器。

> info **提示** 插件会自动生成所有缺失的 swagger 属性，如果需要覆盖这些属性，只需通过 `@ApiProperty()` 显式设置即可。

#### 注释自动提取

启用注释自动提取功能后，CLI 插件会根据注释为属性生成描述和示例值（example）。

例如，假设有如下 `roles` 属性：

```typescript
/**
 * 用户角色列表
 * @example ['admin']
 */
@ApiProperty({
  description: `用户角色列表`,
  example: ['admin'],
})
roles: RoleEnum[] = [];
```

此时你需要重复填写描述和示例值。而启用 `introspectComments` 后，CLI 插件可以自动提取这些注释，并为属性自动生成描述（以及示例，如果有定义的话）。这样，上述属性就可以简化为：

```typescript
/**
 * 用户角色列表
 * @example ['admin']
 */
roles: RoleEnum[] = [];
```

插件还提供了 `dtoKeyOfComment` 和 `controllerKeyOfComment` 选项，用于自定义插件如何将注释内容赋值给 `ApiProperty` 和 `ApiOperation` 装饰器。示例如下：

```typescript
export class SomeController {
  /**
   * 创建资源
   */
  @Post()
  create() {}
}
```

这等价于如下写法：

```typescript
@ApiOperation({ summary: "创建资源" })
```

> info **提示** 对于模型（model），同样的逻辑适用，但用于 `ApiProperty` 装饰器。

对于控制器（Controller），你不仅可以提供摘要（summary），还可以添加描述（备注 remarks）、标签（如 `@deprecated`）以及响应示例。例如：

```ts
/**
 * 创建新猫咪
 *
 * @remarks 此操作用于创建一只新猫咪。
 *
 * @deprecated
 * @throws {500} 发生未知错误。
 * @throws {400} 请求无效。
 */
@Post()
async create(): Promise<Cat> {}
```

#### 使用 CLI 插件

要启用该插件，请打开 `nest-cli.json`（如果你使用 [Nest 命令行工具（Nest CLI）](/cli/overview)），并添加如下 `plugins` 配置：

```javascript
{
  "collection": "@nestjs/schematics",
  "sourceRoot": "src",
  "compilerOptions": {
    "plugins": ["@nestjs/swagger"]
  }
}
```

你可以通过 `options` 属性自定义插件的行为。

```javascript
{
  "collection": "@nestjs/schematics",
  "sourceRoot": "src",
  "compilerOptions": {
    "plugins": [
      {
        "name": "@nestjs/swagger",
        "options": {
          "classValidatorShim": false,
          "introspectComments": true,
          "skipAutoHttpCode": true
        }
      }
    ]
  }
}
```

`options` 属性需符合以下接口定义：

```typescript
export interface PluginOptions {
  dtoFileNameSuffix?: string[]
  controllerFileNameSuffix?: string[]
  classValidatorShim?: boolean
  dtoKeyOfComment?: string
  controllerKeyOfComment?: string
  introspectComments?: boolean
  skipAutoHttpCode?: boolean
  esmCompatible?: boolean
}
```

<table>
  <tbody>
    <tr>
      <th>选项</th>
      <th>默认值</th>
      <th>说明</th>
    </tr>
    <tr>
      <td>
        <code>dtoFileNameSuffix</code>
      </td>
      <td>
        <code>['.dto.ts', '.entity.ts']</code>
      </td>
      <td>数据传输对象（Data Transfer Object，DTO）文件后缀</td>
    </tr>
    <tr>
      <td>
        <code>controllerFileNameSuffix</code>
      </td>
      <td>
        <code>.controller.ts</code>
      </td>
      <td>控制器（Controller）文件后缀</td>
    </tr>
    <tr>
      <td>
        <code>classValidatorShim</code>
      </td>
      <td>
        <code>true</code>
      </td>
      <td>
        如果设置为 true，模块会复用 <code>class-validator</code> 的验证装饰器（例如{' '}
        <code>@Max(10)</code> 会在 schema 定义中添加 <code>max: 10</code>）
      </td>
    </tr>
    <tr>
      <td>
        <code>dtoKeyOfComment</code>
      </td>
      <td>
        <code>'description'</code>
      </td>
      <td>
        设置 <code>ApiProperty</code> 上注释文本的属性键。
      </td>
    </tr>
    <tr>
      <td>
        <code>controllerKeyOfComment</code>
      </td>
      <td>
        <code>'summary'</code>
      </td>
      <td>
        设置 <code>ApiOperation</code> 上注释文本的属性键。
      </td>
    </tr>
    <tr>
      <td>
        <code>introspectComments</code>
      </td>
      <td>
        <code>false</code>
      </td>
      <td>如果设置为 true，插件会根据注释自动生成属性的描述和示例值</td>
    </tr>
    <tr>
      <td>
        <code>skipAutoHttpCode</code>
      </td>
      <td>
        <code>false</code>
      </td>
      <td>
        禁用在控制器中自动添加 <code>@HttpCode()</code>
      </td>
    </tr>
    <tr>
      <td>
        <code>esmCompatible</code>
      </td>
      <td>
        <code>false</code>
      </td>
      <td>
        如果设置为 true，可解决使用 ESM（<code>&#123; "type": "module" &#125;</code>
        ）时遇到的语法错误。
      </td>
    </tr>
  </tbody>
</table>

每当你更新插件选项时，请务必删除 `/dist` 文件夹并重新构建你的应用。
如果你没有使用命令行工具，而是采用自定义的 `webpack` 配置，也可以将该插件与 `ts-loader` 结合使用：

```javascript
getCustomTransformers: (program: any) => ({
  before: [require('@nestjs/swagger/plugin').before({}, program)]
}),
```

#### SWC 构建器

对于标准项目结构（非多包仓库结构 Monorepo），如需在 SWC 构建器中使用 CLI 插件，需要启用类型检查，具体操作请参考[此处](/recipes/swc#type-checking)。

```bash
$ nest start -b swc --type-check
```

如果是多包仓库结构，请按照[这里](/recipes/swc#monorepo-and-cli-plugins)的说明操作。

```bash
$ npx ts-node src/generate-metadata.ts
# 或 npx ts-node apps/{YOUR_APP}/src/generate-metadata.ts
```

此时，序列化后的元数据文件需要通过 `SwaggerModule#loadPluginMetadata` 方法加载，如下所示：

```typescript
import metadata from './metadata' // <-- 该文件由 "PluginMetadataGenerator" 自动生成

await SwaggerModule.loadPluginMetadata(metadata) // <-- 在这里加载
const document = SwaggerModule.createDocument(app, config)
```

#### 与 `ts-jest` 集成（端到端测试 e2e tests）

在运行端到端测试时，`ts-jest` 会在内存中即时编译你的源代码文件。这意味着它不会使用 Nest 命令行工具（Nest CLI）编译器，也不会应用任何插件或执行 AST 转换。

如需启用插件，请在你的 e2e 测试目录下创建如下文件：

```javascript
const transformer = require('@nestjs/swagger/plugin')

module.exports.name = 'nestjs-swagger-transformer'
// 每当你更改下方配置时，都应修改版本号，否则 jest 不会检测到变更
module.exports.version = 1

module.exports.factory = (cs) => {
  return transformer.before(
    {
      // @nestjs/swagger/plugin 选项（可为空）
    },
    cs.program // 对于旧版 Jest（<= v27）为 "cs.tsCompiler.program"
  )
}
```

完成上述操作后，在你的 `jest` 配置文件中引入 AST 转换器。默认情况下（在起始应用中），e2e 测试的配置文件位于 `test` 文件夹下，文件名为 `jest-e2e.json`。

如果你使用的是 `jest@<29`，请参考以下配置片段：

```json
{
  ... // 其他配置
  "globals": {
    "ts-jest": {
      "astTransformers": {
        "before": ["<path to the file created above>"]
      }
    }
  }
}
```

如果你使用的是 `jest@^29`，由于上述方式已被弃用，请使用如下配置片段：

```json
{
  ... // 其他配置
  "transform": {
    "^.+\\.(t|j)s$": [
      "ts-jest",
      {
        "astTransformers": {
          "before": ["<path to the file created above>"]
        }
      }
    ]
  }
}
```

#### `jest`（端到端测试）故障排查

如果 `jest` 没有正确应用你的配置更改，通常是因为 Jest 已经**缓存**了构建结果。要使新配置生效，你需要清除 Jest 的缓存目录。

要清除缓存目录，请在你的 NestJS 项目文件夹中运行以下命令：

```bash
$ npx jest --clearCache
```

如果自动清除缓存失败，你也可以通过以下命令手动删除缓存文件夹：

```bash
# 查找 jest 缓存目录（通常为 /tmp/jest_rs）
# 在 NestJS 项目根目录下运行以下命令
$ npx jest --showConfig | grep cache
# 示例输出：
#   "cache": true,
#   "cacheDirectory": "/tmp/jest_rs"

# 删除或清空 Jest 缓存目录
$ rm -rf <cacheDirectory 的值>
# 示例：
# rm -rf /tmp/jest_rs
```
