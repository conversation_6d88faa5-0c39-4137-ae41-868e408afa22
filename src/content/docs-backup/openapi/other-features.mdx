### 其他功能

本页列出了你可能会用到的其他可用功能。

#### 全局前缀

如果你希望在通过 `setGlobalPrefix()` 设置路由全局前缀后，某些路由不受该前缀影响，可以使用 `ignoreGlobalPrefix` 选项：

```typescript
const document = SwaggerModule.createDocument(app, options, {
  ignoreGlobalPrefix: true,
})
```

#### 全局参数

你可以使用 `DocumentBuilder` 为所有路由定义全局参数，示例如下：

```typescript
const config = new DocumentBuilder()
  .addGlobalParameters({
    name: 'tenantId',
    in: 'header',
  })
  // 其他配置
  .build()
```

#### 全局响应

你可以使用 `DocumentBuilder` 为所有路由定义全局响应。这对于在整个应用中为所有端点统一设置响应（如错误码 `401 Unauthorized` 或 `500 Internal Server Error`）非常有用。

```typescript
const config = new DocumentBuilder()
  .addGlobalResponse({
    status: 500,
    description: 'Internal server error',
  })
  // 其他配置
  .build()
```

#### 多规范支持

`SwaggerModule` 提供了支持多规范（Multiple specifications）的方法。换句话说，你可以在不同的端点（endpoint）上，分别展示不同的文档和不同的界面。

要支持多规范，你的应用需要采用模块化（modular approach）方式编写。`createDocument()` 方法的第三个参数 `extraOptions` 是一个对象，其中包含名为 `include` 的属性。`include` 属性的值是一个模块数组。

你可以按照如下方式配置多规范支持：

```typescript
import { NestFactory } from '@nestjs/core'
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger'
import { AppModule } from './app.module'
import { CatsModule } from './cats/cats.module'
import { DogsModule } from './dogs/dogs.module'

async function bootstrap() {
  const app = await NestFactory.create(AppModule)

  /**
   * createDocument(application, configurationOptions, extraOptions);
   *
   * createDocument 方法可以接收一个可选的第三个参数 "extraOptions"，
   * 该参数是一个包含 "include" 属性的对象，你可以在这里传入一个模块数组，
   * 用于指定要包含在该 Swagger 规范中的模块。
   * 例如：CatsModule 和 DogsModule 会分别生成两个独立的 Swagger 规范，
   * 并通过两个不同的 Swagger UI 端点进行展示。
   */

  const options = new DocumentBuilder()
    .setTitle('Cats example')
    .setDescription('The cats API description')
    .setVersion('1.0')
    .addTag('cats')
    .build()

  const catDocumentFactory = () =>
    SwaggerModule.createDocument(app, options, {
      include: [CatsModule],
    })
  SwaggerModule.setup('api/cats', app, catDocumentFactory)

  const secondOptions = new DocumentBuilder()
    .setTitle('Dogs example')
    .setDescription('The dogs API description')
    .setVersion('1.0')
    .addTag('dogs')
    .build()

  const dogDocumentFactory = () =>
    SwaggerModule.createDocument(app, secondOptions, {
      include: [DogsModule],
    })
  SwaggerModule.setup('api/dogs', app, dogDocumentFactory)

  await app.listen(process.env.PORT ?? 3000)
}
bootstrap()
```

现在你可以通过以下命令启动服务器：

```bash
$ npm run start
```

访问 `http://localhost:3000/api/cats`，即可查看 cats 的 Swagger UI：

<figure>
  <img src="/assets/swagger-cats.png" />
</figure>

同样，`http://localhost:3000/api/dogs` 会展示 dogs 的 Swagger UI：

<figure>
  <img src="/assets/swagger-dogs.png" />
</figure>

#### 在 Explorer 栏中使用下拉菜单

要在 Explorer（探索器）栏的下拉菜单中支持多个规范，你需要在 `SwaggerCustomOptions` 中设置 `explorer: true`，并配置 `swaggerOptions.urls`。

> info **提示**
> 请确保 `swaggerOptions.urls` 指向你的 Swagger 文档的 JSON 格式！如需指定 JSON 文档，请在 `SwaggerCustomOptions` 中使用 `jsonDocumentUrl`。更多设置选项请参见[这里](/openapi/introduction#setup-options)。

以下是如何在 Explorer 栏的下拉菜单中设置多个规范的示例：

```typescript
import { NestFactory } from '@nestjs/core'
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger'
import { AppModule } from './app.module'
import { CatsModule } from './cats/cats.module'
import { DogsModule } from './dogs/dogs.module'

async function bootstrap() {
  const app = await NestFactory.create(AppModule)

  // 主 API 配置项
  const options = new DocumentBuilder()
    .setTitle('Multiple Specifications Example')
    .setDescription('Description for multiple specifications')
    .setVersion('1.0')
    .build()

  // 创建主 API 文档
  const document = SwaggerModule.createDocument(app, options)

  // 配置主 API 的 Swagger UI，并支持下拉菜单
  SwaggerModule.setup('api', app, document, {
    explorer: true,
    swaggerOptions: {
      urls: [
        {
          name: '1. API',
          url: 'api/swagger.json',
        },
        {
          name: '2. Cats API',
          url: 'api/cats/swagger.json',
        },
        {
          name: '3. Dogs API',
          url: 'api/dogs/swagger.json',
        },
      ],
    },
    jsonDocumentUrl: '/api/swagger.json',
  })

  // Cats API 配置项
  const catOptions = new DocumentBuilder()
    .setTitle('Cats Example')
    .setDescription('Description for the Cats API')
    .setVersion('1.0')
    .addTag('cats')
    .build()

  // 创建 Cats API 文档
  const catDocument = SwaggerModule.createDocument(app, catOptions, {
    include: [CatsModule],
  })

  // 配置 Cats API 的 Swagger UI
  SwaggerModule.setup('api/cats', app, catDocument, {
    jsonDocumentUrl: '/api/cats/swagger.json',
  })

  // Dogs API 配置项
  const dogOptions = new DocumentBuilder()
    .setTitle('Dogs Example')
    .setDescription('Description for the Dogs API')
    .setVersion('1.0')
    .addTag('dogs')
    .build()

  // 创建 Dogs API 文档
  const dogDocument = SwaggerModule.createDocument(app, dogOptions, {
    include: [DogsModule],
  })

  // 配置 Dogs API 的 Swagger UI
  SwaggerModule.setup('api/dogs', app, dogDocument, {
    jsonDocumentUrl: '/api/dogs/swagger.json',
  })

  await app.listen(3000)
}

bootstrap()
```

在本示例中，我们设置了一个主 API，以及 Cats 和 Dogs 的独立规范，每个规范都可以通过 Explorer（探索器）栏的下拉菜单访问。
