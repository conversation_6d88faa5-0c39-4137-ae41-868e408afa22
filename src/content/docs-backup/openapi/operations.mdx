### 操作（Operations）

在 OpenAPI 中，路径（paths）指的是 API 暴露的端点（如 `/users` 或 `/reports/summary`），而操作（operations）则是用于操作这些路径的 HTTP 方法，例如 `GET`、`POST` 或 `DELETE`。

#### 标签

要将控制器关联到特定标签，可以使用 `@ApiTags(...tags)` 装饰器。

```typescript
@ApiTags('cats')
@Controller('cats')
export class CatsController {}
```

#### 请求头

如果需要定义请求中期望包含的自定义请求头，可以使用 `@ApiHeader()` 装饰器。

```typescript
@ApiHeader({
  name: 'X-MyHeader',
  description: '自定义请求头',
})
@Controller('cats')
export class CatsController {}
```

#### 响应

要自定义 HTTP 响应，可以使用 `@ApiResponse()` 装饰器。

```typescript
@Post()
@ApiResponse({ status: 201, description: '记录已成功创建。' })
@ApiResponse({ status: 403, description: '禁止访问。' })
async create(@Body() createCatDto: CreateCatDto) {
  this.catsService.create(createCatDto);
}
```

Nest 提供了一组简写的 **API 响应** 装饰器，这些装饰器继承自 `@ApiResponse` 装饰器：

- `@ApiOkResponse()`
- `@ApiCreatedResponse()`
- `@ApiAcceptedResponse()`
- `@ApiNoContentResponse()`
- `@ApiMovedPermanentlyResponse()`
- `@ApiFoundResponse()`
- `@ApiBadRequestResponse()`
- `@ApiUnauthorizedResponse()`
- `@ApiNotFoundResponse()`
- `@ApiForbiddenResponse()`
- `@ApiMethodNotAllowedResponse()`
- `@ApiNotAcceptableResponse()`
- `@ApiRequestTimeoutResponse()`
- `@ApiConflictResponse()`
- `@ApiPreconditionFailedResponse()`
- `@ApiTooManyRequestsResponse()`
- `@ApiGoneResponse()`
- `@ApiPayloadTooLargeResponse()`
- `@ApiUnsupportedMediaTypeResponse()`
- `@ApiUnprocessableEntityResponse()`
- `@ApiInternalServerErrorResponse()`
- `@ApiNotImplementedResponse()`
- `@ApiBadGatewayResponse()`
- `@ApiServiceUnavailableResponse()`
- `@ApiGatewayTimeoutResponse()`
- `@ApiDefaultResponse()`

```typescript
@Post()
@ApiCreatedResponse({ description: '记录已成功创建。' })
@ApiForbiddenResponse({ description: '禁止访问。' })
async create(@Body() createCatDto: CreateCatDto) {
  this.catsService.create(createCatDto);
}
```

如果需要为请求指定返回模型，需要先创建一个类，并为所有属性添加 `@ApiProperty()` 装饰器。

```typescript
export class Cat {
  @ApiProperty()
  id: number

  @ApiProperty()
  name: string

  @ApiProperty()
  age: number

  @ApiProperty()
  breed: string
}
```

然后，可以将 `Cat` 模型与响应装饰器的 `type` 属性结合使用。

```typescript
@ApiTags('cats')
@Controller('cats')
export class CatsController {
  @Post()
  @ApiCreatedResponse({
    description: '记录已成功创建。',
    type: Cat,
  })
  async create(@Body() createCatDto: CreateCatDto): Promise<Cat> {
    return this.catsService.create(createCatDto)
  }
}
```

让我们打开浏览器，验证生成的 `Cat` 模型：

<figure>
  <img src="/assets/swagger-response-type.png" />
</figure>

如果你不想为每个端点或控制器单独定义响应，也可以通过 `DocumentBuilder` 类为所有端点定义全局响应。这种方式适用于需要为应用中所有端点统一定义响应（例如 `401 Unauthorized` 或 `500 Internal Server Error` 等错误）的场景。

```typescript
const config = new DocumentBuilder()
  .addGlobalResponse({
    status: 500,
    description: '内部服务器错误',
  })
  // 其他配置
  .build()
```

#### 文件上传

你可以通过结合使用 `@ApiBody` 装饰器和 `@ApiConsumes()`，为特定方法启用文件上传功能。以下是一个完整示例，演示如何使用 [文件上传](/techniques/file-upload) 技巧：

```typescript
@UseInterceptors(FileInterceptor('file'))
@ApiConsumes('multipart/form-data')
@ApiBody({
  description: '猫咪列表',
  type: FileUploadDto,
})
uploadFile(@UploadedFile() file: Express.Multer.File) {}
```

其中，`FileUploadDto` 的定义如下：

```typescript
class FileUploadDto {
  @ApiProperty({ type: 'string', format: 'binary' })
  file: any
}
```

如果需要处理多个文件上传，可以这样定义 `FilesUploadDto`：

```typescript
class FilesUploadDto {
  @ApiProperty({ type: 'array', items: { type: 'string', format: 'binary' } })
  files: any[]
}
```

#### 扩展（Extensions）

要为请求添加扩展，请使用 `@ApiExtension()` 装饰器。扩展名称必须以 `x-` 为前缀。

```typescript
@ApiExtension('x-foo', { hello: 'world' })
```

#### 进阶：通用 `ApiResponse`

通过提供 [原始定义](/openapi/types-and-parameters#raw-definitions)，我们可以为 Swagger UI 定义通用（泛型）模式。假设我们有如下的数据传输对象：

```ts
export class PaginatedDto<TData> {
  @ApiProperty()
  total: number

  @ApiProperty()
  limit: number

  @ApiProperty()
  offset: number

  results: TData[]
}
```

我们没有为 `results` 属性添加装饰器，因为稍后会为其提供原始定义。接下来，我们再定义一个 DTO，例如命名为 `CatDto`，如下所示：

```ts
export class CatDto {
  @ApiProperty()
  name: string

  @ApiProperty()
  age: number

  @ApiProperty()
  breed: string
}
```

有了这些定义后，我们可以如下定义 `PaginatedDto<CatDto>` 的响应类型：

```ts
@ApiOkResponse({
  schema: {
    allOf: [
      { $ref: getSchemaPath(PaginatedDto) },
      {
        properties: {
          results: {
            type: 'array',
            items: { $ref: getSchemaPath(CatDto) },
          },
        },
      },
    ],
  },
})
async findAll(): Promise<PaginatedDto<CatDto>> {}
```

在此示例中，我们指定响应将包含 `PaginatedDto` 的所有属性，并且 `results` 属性的类型为 `Array<CatDto>`。

- `getSchemaPath()` 函数用于根据指定模型返回 OpenAPI 规范文件中的 Schema 路径。
- `allOf` 是 OAS 3（OpenAPI 规范 3）中用于处理继承等多种场景的概念。

最后，由于 `PaginatedDto` 并未被任何控制器（Controller）直接引用，`SwaggerModule` 目前还无法为其生成对应的模型定义。此时，我们需要将其作为[额外模型](/openapi/types-and-parameters#extra-models)添加。例如，可以在控制器层级使用 `@ApiExtraModels()` 装饰器，如下所示：

```ts
@Controller('cats')
@ApiExtraModels(PaginatedDto)
export class CatsController {}
```

此时如果运行 Swagger，针对该端点生成的 `swagger.json` 响应定义应如下所示：

```json
"responses": {
  "200": {
    "description": "",
    "content": {
      "application/json": {
        "schema": {
          "allOf": [
            {
              "$ref": "#/components/schemas/PaginatedDto"
            },
            {
              "properties": {
                "results": {
                  "$ref": "#/components/schemas/CatDto"
                }
              }
            }
          ]
        }
      }
    }
  }
}
```

为了便于复用，我们可以为 `PaginatedDto` 创建一个自定义装饰器，如下所示：

```ts
export const ApiPaginatedResponse = <TModel extends Type<any>>(model: TModel) => {
  return applyDecorators(
    ApiExtraModels(PaginatedDto, model),
    ApiOkResponse({
      schema: {
        allOf: [
          { $ref: getSchemaPath(PaginatedDto) },
          {
            properties: {
              results: {
                type: 'array',
                items: { $ref: getSchemaPath(model) },
              },
            },
          },
        ],
      },
    })
  )
}
```

> info **提示** `Type<any>` 接口和 `applyDecorators` 函数均从 `@nestjs/common` 包中导入。

为了确保 `SwaggerModule` 能为我们的模型生成定义，必须像前面在控制器中为 `PaginatedDto` 添加额外模型一样，将其作为额外模型添加。

有了上述内容后，我们就可以在端点上使用自定义的 `@ApiPaginatedResponse()` 装饰器：

```ts
@ApiPaginatedResponse(CatDto)
async findAll(): Promise<PaginatedDto<CatDto>> {}
```

对于客户端代码生成工具来说，这种方式在生成 `PaginatedResponse<TModel>` 时存在一定的歧义。下面是针对上述 `GET /` 端点的客户端生成示例：

```typescript
// Angular
findAll(): Observable<{ total: number, limit: number, offset: number, results: CatDto[] }>
```

可以看到，这里的**返回类型**存在歧义。为了解决这个问题，可以在 `ApiPaginatedResponse` 的 `schema` 中添加 `title` 属性：

```typescript
export const ApiPaginatedResponse = <TModel extends Type<any>>(model: TModel) => {
  return applyDecorators(
    ApiOkResponse({
      schema: {
        title: `PaginatedResponseOf${model.name}`,
        allOf: [
          // ...
        ],
      },
    })
  )
}
```

这样，客户端生成工具的结果将变为：

```ts
// Angular
findAll(): Observable<PaginatedResponseOfCatDto>
```
