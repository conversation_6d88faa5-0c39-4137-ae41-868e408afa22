### 映射类型（Mapped types）

在开发如 **CRUD**（创建/读取/更新/删除）等功能时，通常需要基于某个基础实体类型构建不同的变体。Nest 提供了多种实用函数，帮助你便捷地进行类型转换。

#### Partial

在构建输入验证类型（也称为数据传输对象）时，通常需要基于同一类型分别构建 **创建** 和 **更新** 两种变体。例如，**创建** 变体可能要求所有字段都是必填的，而 **更新** 变体则可以将所有字段设为可选。

Nest 提供了 `PartialType()` 实用函数，帮助你简化这项工作，减少模板代码。

`PartialType()` 函数会返回一个类型（类），其所有属性都变为可选。例如，假设我们有如下的 **创建** 类型：

```typescript
import { ApiProperty } from '@nestjs/swagger'

export class CreateCatDto {
  @ApiProperty()
  name: string

  @ApiProperty()
  age: number

  @ApiProperty()
  breed: string
}
```

默认情况下，这些字段都是必填的。要创建一个拥有相同字段但全部为可选的类型，可以使用 `PartialType()`，并将类引用（`CreateCatDto`）作为参数传入：

```typescript
export class UpdateCatDto extends PartialType(CreateCatDto) {}
```

> info **提示** `PartialType()` 函数需从 `@nestjs/swagger` 包中导入。

#### Pick

`PickType()` 函数可以从输入类型中挑选一组属性，构造出一个新的类型（类）。例如，假设我们有如下类型：

```typescript
import { ApiProperty } from '@nestjs/swagger'

export class CreateCatDto {
  @ApiProperty()
  name: string

  @ApiProperty()
  age: number

  @ApiProperty()
  breed: string
}
```

我们可以通过 `PickType()` 实用函数，从该类中挑选部分属性：

```typescript
export class UpdateCatAgeDto extends PickType(CreateCatDto, ['age'] as const) {}
```

> info **提示** `PickType()` 函数需从 `@nestjs/swagger` 包中导入。

#### Omit

`OmitType()` 函数会从输入类型中选取所有属性，然后移除指定的键，构造出一个新类型。例如，假设我们有如下类型：

```typescript
import { ApiProperty } from '@nestjs/swagger'

export class CreateCatDto {
  @ApiProperty()
  name: string

  @ApiProperty()
  age: number

  @ApiProperty()
  breed: string
}
```

我们可以生成一个派生类型，包含除了 `name` 以外的所有属性，如下所示。在这里，`OmitType` 的第二个参数是属性名数组。

```typescript
export class UpdateCatDto extends OmitType(CreateCatDto, ['name'] as const) {}
```

> info **提示** `OmitType()` 函数需从 `@nestjs/swagger` 包中导入。

#### Intersection

`IntersectionType()` 函数可以将两个类型合并为一个新类型（类）。例如，假设我们有如下两个类型：

```typescript
import { ApiProperty } from '@nestjs/swagger'

export class CreateCatDto {
  @ApiProperty()
  name: string

  @ApiProperty()
  breed: string
}

export class AdditionalCatInfo {
  @ApiProperty()
  color: string
}
```

我们可以生成一个新类型，包含两个类型中的所有属性。

```typescript
export class UpdateCatDto extends IntersectionType(CreateCatDto, AdditionalCatInfo) {}
```

> info **提示** `IntersectionType()` 函数需从 `@nestjs/swagger` 包中导入。

#### 组合（Composition）

类型映射实用函数是可以组合使用的。例如，下面的写法会生成一个类型（类），它拥有 `CreateCatDto` 类型中除了 `name` 以外的所有属性，并且这些属性都是可选的：

```typescript
export class UpdateCatDto extends PartialType(OmitType(CreateCatDto, ['name'] as const)) {}
```
