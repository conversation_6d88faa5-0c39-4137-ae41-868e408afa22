### 类型与参数

`SwaggerModule` 会在路由处理器（Route Handler）中查找所有的 `@Body()`、`@Query()` 和 `@Param()` 装饰器（Decorator），以生成 API 文档（API Documentation）。同时，它还会利用反射机制（Reflection）自动创建相应的模型定义。请参考以下代码示例：

```typescript
@Post()
async create(@Body() createCatDto: CreateCatDto) {
  this.catsService.create(createCatDto);
}
```

> info **提示** 如果需要显式设置请求体（Request Body）定义，可以使用 `@ApiBody()` 装饰器（从 `@nestjs/swagger` 包中导入）。

基于 `CreateCatDto`，Swagger UI 会生成如下模型定义：

<figure>
  <img src="/assets/swagger-dto.png" />
</figure>

如上图所示，虽然类中声明了多个属性，但定义却是空的。为了让 `SwaggerModule` 能够识别并展示类的属性，我们需要为每个属性添加 `@ApiProperty()` 装饰器，或者使用 CLI 插件（详见 **插件** 部分），该插件会自动完成此操作：

```typescript
import { ApiProperty } from '@nestjs/swagger'

export class CreateCatDto {
  @ApiProperty()
  name: string

  @ApiProperty()
  age: number

  @ApiProperty()
  breed: string
}
```

> info **提示** 如果不想手动为每个属性添加注解，可以考虑使用 Swagger 插件（详见 [插件](/openapi/cli-plugin) 部分），该插件会自动为你处理。

接下来，我们可以在浏览器中查看生成的 `CreateCatDto` 模型：

<figure>
  <img src="/assets/swagger-dto2.png" />
</figure>

此外，`@ApiProperty()` 装饰器允许你设置多种 [Schema Object](https://swagger.io/specification/#schemaObject) 属性，例如：

```typescript
@ApiProperty({
  description: '猫的年龄',
  minimum: 1,
  default: 1,
})
age: number;
```

> info **提示** 如果你想让属性变为可选项，可以直接使用 `@ApiPropertyOptional()` 装饰器，无需显式书写 `{{"@ApiProperty({ required: false })"}}`。

如果需要显式指定属性的类型，可以使用 `type` 键：

```typescript
@ApiProperty({
  type: Number,
})
age: number;
```

#### 数组

当属性为数组时，我们必须手动指定数组的类型，如下所示：

```typescript
@ApiProperty({ type: [String] })
names: string[];
```

> info **提示** 建议使用 Swagger 插件（详见[插件](/openapi/cli-plugin)章节），该插件会自动检测数组类型。

你可以像上面这样，将类型作为数组的第一个元素传递，或者设置 `isArray` 属性为 `true`。

<app-banner-enterprise></app-banner-enterprise>

#### 循环依赖

当类之间存在循环依赖时，可以使用惰性函数（lazy function）为 `SwaggerModule` 提供类型信息：

```typescript
@ApiProperty({ type: () => Node })
node: Node;
```

> info **提示** 建议使用 Swagger 插件（详见[插件](/openapi/cli-plugin)章节），该插件会自动检测循环依赖。

#### 泛型与接口

由于 TypeScript 不会存储关于泛型（Generics）或接口（Interfaces）的元数据（Metadata），当你在数据传输对象中使用它们时，`SwaggerModule` 可能无法在运行时正确生成模型定义。例如，下面的代码无法被 Swagger 模块正确识别：

```typescript
createBulk(@Body() usersDto: CreateUserDto[])
```

为了解决这个限制，你可以显式地设置类型：

```typescript
@ApiBody({ type: [CreateUserDto] })
createBulk(@Body() usersDto: CreateUserDto[])
```

#### 枚举（Enum）

要让 `SwaggerModule` 识别一个枚举（enum），我们必须在 `@ApiProperty` 装饰器中手动设置 `enum` 属性，并传入一个值数组。

```typescript
@ApiProperty({ enum: ['Admin', 'Moderator', 'User']})
role: UserRole;
```

或者，也可以像下面这样定义一个实际的 TypeScript 枚举：

```typescript
export enum UserRole {
  Admin = 'Admin',
  Moderator = 'Moderator',
  User = 'User',
}
```

然后你可以将该枚举直接与 `@Query()` 参数装饰器结合 `@ApiQuery()` 装饰器一起使用。

```typescript
@ApiQuery({ name: 'role', enum: UserRole })
async filterByRole(@Query('role') role: UserRole = UserRole.User) {}
```

<figure>
  <img src="/assets/enum_query.gif" />
</figure>

当 `isArray` 设置为 **true** 时，枚举可以作为**多选**项：

<figure>
  <img src="/assets/enum_query_array.gif" />
</figure>

#### 枚举 schema

默认情况下，`enum` 属性会在 `parameter` 上添加一个原始的 [枚举（Enum）](https://swagger.io/docs/specification/data-models/enums/) 定义。

```yaml
- breed:
    type: 'string'
    enum:
      - Persian
      - Tabby
      - Siamese
```

上述规范在大多数场景下都能正常工作。然而，如果你使用某些工具将该规范作为**输入**，并生成**客户端代码（client-side code）**，你可能会遇到生成的代码中出现重复 `枚举（Enum）` 的问题。请参考以下代码片段：

```typescript
// 生成的客户端代码
export class CatDetail {
  breed: CatDetailEnum
}

export class CatInformation {
  breed: CatInformationEnum
}

export enum CatDetailEnum {
  Persian = 'Persian',
  Tabby = 'Tabby',
  Siamese = 'Siamese',
}

export enum CatInformationEnum {
  Persian = 'Persian',
  Tabby = 'Tabby',
  Siamese = 'Siamese',
}
```

> info **提示** 上述代码片段是通过名为 [NSwag](https://github.com/RicoSuter/NSwag) 的工具生成的。

你可以看到，现在有两个完全相同的 `枚举`。
为了解决这个问题，你可以在装饰器中为 `enum` 属性同时传递一个 `enumName`。

```typescript
export class CatDetail {
  @ApiProperty({ enum: CatBreed, enumName: 'CatBreed' })
  breed: CatBreed
}
```

`enumName` 属性使 `@nestjs/swagger` 能够将 `CatBreed` 转换为独立的 `schema`，从而让 `CatBreed` 枚举（Enum）实现可复用。生成的规范如下所示：

```yaml
CatDetail:
  type: 'object'
  properties:
    ...
    - breed:
        schema:
          $ref: '#/components/schemas/CatBreed'
CatBreed:
  type: string
  enum:
    - Persian
    - Tabby
    - Siamese
```

> info **提示** 任何带有 `enum` 属性的**装饰器**，同样支持 `enumName` 属性。

#### 属性值示例

你可以通过 `example` 键为属性设置单个示例，示例如下：

```typescript
@ApiProperty({
  example: 'persian',
})
breed: string;
```

如果你希望提供多个示例，可以使用 `examples` 键，并传入如下结构的对象：

```typescript
@ApiProperty({
  examples: {
    Persian: { value: 'persian' },
    Tabby: { value: 'tabby' },
    Siamese: { value: 'siamese' },
    'Scottish Fold': { value: 'scottish_fold' },
  },
})
breed: string;
```

#### 原始定义

在某些情况下，例如深度嵌套的数组或矩阵，你可能需要手动定义类型：

```typescript
@ApiProperty({
  type: 'array',
  items: {
    type: 'array',
    items: {
      type: 'number',
    },
  },
})
coords: number[][];
```

你也可以像下面这样指定原始对象（raw object）模式：

```typescript
@ApiProperty({
  type: 'object',
  properties: {
    name: {
      type: 'string',
      example: 'Error'
    },
    status: {
      type: 'number',
      example: 400
    }
  },
  required: ['name', 'status']
})
rawDefinition: Record<string, any>;
```

如果你需要在控制器类中手动定义输入/输出内容，可以使用 `schema` 属性：

```typescript
@ApiBody({
  schema: {
    type: 'array',
    items: {
      type: 'array',
      items: {
        type: 'number',
      },
    },
  },
})
async create(@Body() coords: number[][]) {}
```

#### 额外模型

如果你需要定义一些不会被控制器直接引用、但希望被 Swagger 模块扫描的额外模型，可以使用 `@ApiExtraModels()` 装饰器：

```typescript
@ApiExtraModels(ExtraModel)
export class CreateCatDto {}
```

> info **提示** 对于同一个模型类，你只需要使用一次 `@ApiExtraModels()`。

另外，你也可以在调用 `SwaggerModule#createDocument()` 方法时，通过传递包含 `extraModels` 属性的选项对象来实现：

```typescript
const documentFactory = () =>
  SwaggerModule.createDocument(app, options, {
    extraModels: [ExtraModel],
  })
```

如果你需要获取模型的引用（`$ref`），可以使用 `getSchemaPath(ExtraModel)` 函数：

```typescript
'application/vnd.api+json': {
   schema: { $ref: getSchemaPath(ExtraModel) },
},
```

#### oneOf、anyOf、allOf

要组合多个模式（schema），可以使用 `oneOf`、`anyOf` 或 `allOf` 关键字（[详细说明](https://swagger.io/docs/specification/data-models/oneof-anyof-allof-not/)）。

```typescript
@ApiProperty({
  oneOf: [
    { $ref: getSchemaPath(Cat) },
    { $ref: getSchemaPath(Dog) },
  ],
})
pet: Cat | Dog;
```

如果你想定义一个多态数组（即数组成员可以属于多个模式），应当手动使用原始定义（见上文）来指定类型。

```typescript
type Pet = Cat | Dog;

@ApiProperty({
  type: 'array',
  items: {
    oneOf: [
      { $ref: getSchemaPath(Cat) },
      { $ref: getSchemaPath(Dog) },
    ],
  },
})
pets: Pet[];
```

> info **提示** `getSchemaPath()` 函数需从 `@nestjs/swagger` 导入。

`Cat` 和 `Dog` 都必须通过 `@ApiExtraModels()` 装饰器（应用于类级别）声明为额外模型。

#### 模式名称与描述

你可能已经注意到，生成的模式名称基于原始模型类的名称（例如，`CreateCatDto` 模型会生成 `CreateCatDto` 模式）。如果你希望更改模式名称，可以使用 `@ApiSchema()` 装饰器。

示例：

```typescript
@ApiSchema({ name: 'CreateCatRequest' })
class CreateCatDto {}
```

上述模型会被转换为 `CreateCatRequest` 模式。

默认情况下，生成的模式不会包含描述。你可以通过 `description` 属性添加描述信息：

```typescript
@ApiSchema({ description: 'CreateCatDto 模式的描述' })
class CreateCatDto {}
```

这样，描述信息就会被包含在模式中，效果如下：

```yaml
schemas:
  CreateCatDto:
    type: object
    description: CreateCatDto 模式的描述
```
