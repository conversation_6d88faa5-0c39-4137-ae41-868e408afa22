### 健康检查（Healthcheck / Terminus）

Terminus 集成为你提供了 **就绪性/存活性（readiness/liveness）** 健康检查。在复杂的后端架构中，健康检查至关重要。简而言之，Web 开发中的健康检查通常指的是一个特殊的地址，例如：`https://my-website.com/health/readiness`。
你的基础设施中的某个服务或组件（例如 [Kubernetes](https://kubernetes.io/)）会持续检查这个地址。根据对该地址发起 `GET` 请求后返回的 HTTP 状态码，服务会在收到“不健康”响应时采取相应措施。
由于“健康”或“不健康”的定义会根据你提供的服务类型而有所不同，**Terminus** 集成为你提供了一组 **健康指示器（Health Indicator）**。

举个例子，如果你的 Web 服务器使用 MongoDB 存储数据，那么 MongoDB 是否仍在运行就是非常重要的信息。
在这种情况下，你可以使用 `MongooseHealthIndicator`。如果配置正确（后文会详细介绍），你的健康检查地址会根据 MongoDB 是否运行返回健康或不健康的 HTTP 状态码。

#### 快速开始

要开始使用 `@nestjs/terminus`，我们需要安装所需的依赖包。

```bash
$ npm install --save @nestjs/terminus
```

#### 配置健康检查

健康检查是由多个 **健康指示器** 组成的汇总。健康指示器会检查某个服务是否处于健康或不健康状态。如果所有分配的健康指示器都正常运行，则健康检查为通过。由于许多应用都需要类似的健康指示器，[`@nestjs/terminus`](https://github.com/nestjs/terminus) 提供了一组预定义的指示器，例如：

- `HttpHealthIndicator`
- `TypeOrmHealthIndicator`
- `MongooseHealthIndicator`
- `SequelizeHealthIndicator`
- `MikroOrmHealthIndicator`
- `PrismaHealthIndicator`
- `MicroserviceHealthIndicator`
- `GRPCHealthIndicator`
- `MemoryHealthIndicator`
- `DiskHealthIndicator`

接下来，让我们创建 `HealthModule`，并在其 imports 数组中引入 `TerminusModule`。

> info **提示** 你可以通过 [Nest 命令行工具（Nest CLI）](cli/overview) 快速创建模块，只需执行 `$ nest g module health` 命令。

```typescript
@@filename(health.module)
import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';

@Module({
  imports: [TerminusModule]
})
export class HealthModule {}
```

我们的健康检查可以通过 [控制器（Controller）](/controllers) 来执行，你可以使用 [Nest 命令行工具（Nest CLI）](cli/overview) 轻松创建控制器。

```bash
$ nest g controller health
```

> info **提示** 强烈建议在你的应用中启用关闭钩子（Shutdown Hook）。Terminus 集成在启用后会利用此生命周期事件（Lifecycle Event）。你可以在[这里](fundamentals/lifecycle-events#application-shutdown)阅读更多关于关闭钩子的内容。

#### HTTP 健康检查

当我们已经安装了 `@nestjs/terminus`，导入了 `TerminusModule` 并创建了新的控制器后，就可以开始编写健康检查了。

`HTTPHealthIndicator` 依赖 `@nestjs/axios` 包，因此请确保已安装：

```bash
$ npm i --save @nestjs/axios axios
```

现在我们可以设置 `HealthController`：

```typescript
@@filename(health.controller)
import { Controller, Get } from '@nestjs/common';
import { HealthCheckService, HttpHealthIndicator, HealthCheck } from '@nestjs/terminus';

@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private http: HttpHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.http.pingCheck('nestjs-docs', 'https://docs.nestjs.com'),
    ]);
  }
}
@@switch
import { Controller, Dependencies, Get } from '@nestjs/common';
import { HealthCheckService, HttpHealthIndicator, HealthCheck } from '@nestjs/terminus';

@Controller('health')
@Dependencies(HealthCheckService, HttpHealthIndicator)
export class HealthController {
  constructor(
    private health,
    private http,
  ) { }

  @Get()
  @HealthCheck()
  healthCheck() {
    return this.health.check([
      () => this.http.pingCheck('nestjs-docs', 'https://docs.nestjs.com'),
    ])
  }
}
```

```typescript
@@filename(health.module)
import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { HttpModule } from '@nestjs/axios';
import { HealthController } from './health.controller';

@Module({
  imports: [TerminusModule, HttpModule],
  controllers: [HealthController],
})
export class HealthModule {}
@@switch
import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { HttpModule } from '@nestjs/axios';
import { HealthController } from './health.controller';

@Module({
  imports: [TerminusModule, HttpModule],
  controllers: [HealthController],
})
export class HealthModule {}
```

现在，健康检查会向 `https://docs.nestjs.com` 地址发送一次 _GET_ 请求。如果该地址返回健康响应，则我们在 `http://localhost:3000/health` 路由下会收到如下对象，并返回 200 状态码：

```json
{
  "status": "ok",
  "info": {
    "nestjs-docs": {
      "status": "up"
    }
  },
  "error": {},
  "details": {
    "nestjs-docs": {
      "status": "up"
    }
  }
}
```

该响应对象的接口定义可通过 `@nestjs/terminus` 包中的 `HealthCheckResult` 接口获取。

|           |                                                                                                                                |                                      |
| --------- | ------------------------------------------------------------------------------------------------------------------------------ | ------------------------------------ |
| `status`  | 如果有任何健康指示器失败，则状态为 `'error'`。如果 NestJS 应用正在关闭但仍接受 HTTP 请求，则健康检查状态为 `'shutting_down'`。 | `'error' \| 'ok' \| 'shutting_down'` |
| `info`    | 包含所有健康（'up'）健康指示器信息的对象。                                                                                     | `object`                             |
| `error`   | 包含所有不健康（'down'）健康指示器信息的对象。                                                                                 | `object`                             |
| `details` | 包含所有健康指示器详细信息的对象。                                                                                             | `object`                             |

##### 检查特定 HTTP 响应码

在某些情况下，你可能希望检查特定条件并验证响应。例如，假设 `https://my-external-service.com` 返回响应码 `204`。通过 `HttpHealthIndicator.responseCheck`，你可以专门检查该响应码，并将其他所有响应码视为不健康。

如果返回的不是 `204` 响应码，则下方示例会判定为不健康。第三个参数需要你提供一个函数（同步或异步），用于判断响应是否健康（`true` 表示健康，`false` 表示不健康）。

```typescript
@@filename(health.controller)
// 在 `HealthController` 类中

@Get()
@HealthCheck()
check() {
  return this.health.check([
    () =>
      this.http.responseCheck(
        'my-external-service',
        'https://my-external-service.com',
        (res) => res.status === 204,
      ),
  ]);
}
```

#### TypeOrm 健康检查指示器

Terminus 提供了为健康检查添加数据库检查的能力。要开始使用该健康检查指示器，请先阅读[数据库章节](/techniques/sql)，并确保你的应用中数据库连接已成功建立。

> info **提示** 其实在底层，`TypeOrmHealthIndicator` 只是简单地执行了一条 `SELECT 1` SQL 命令，这通常用于验证数据库是否仍然存活。如果你使用的是 Oracle 数据库，则会执行 `SELECT 1 FROM DUAL`。

```typescript
@@filename(health.controller)
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.db.pingCheck('database'),
    ]);
  }
}
@@switch
@Controller('health')
@Dependencies(HealthCheckService, TypeOrmHealthIndicator)
export class HealthController {
  constructor(
    private health,
    private db,
  ) { }

  @Get()
  @HealthCheck()
  healthCheck() {
    return this.health.check([
      () => this.db.pingCheck('database'),
    ])
  }
}
```

如果数据库可用，当你通过 `GET` 请求访问 `http://localhost:3000/health` 时，将会看到如下 JSON 结果：

```json
{
  "status": "ok",
  "info": {
    "database": {
      "status": "up"
    }
  },
  "error": {},
  "details": {
    "database": {
      "status": "up"
    }
  }
}
```

如果你的应用使用了[多数据库](techniques/database#multiple-databases)，你需要将每个连接都注入到 `HealthController` 中。然后，只需将连接引用传递给 `TypeOrmHealthIndicator` 即可。

```typescript
@@filename(health.controller)
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
    @InjectConnection('albumsConnection')
    private albumsConnection: Connection,
    @InjectConnection()
    private defaultConnection: Connection,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.db.pingCheck('albums-database', { connection: this.albumsConnection }),
      () => this.db.pingCheck('database', { connection: this.defaultConnection }),
    ]);
  }
}
```

#### 磁盘健康指示器

使用 `DiskHealthIndicator`，我们可以检查磁盘的存储使用情况。首先，请确保在你的 `HealthController`（健康检查控制器）中注入 `DiskHealthIndicator`。下面的示例会检查路径 `/`（在 Windows 系统中可以使用 `C:\`）的存储使用情况。如果已用存储空间超过总空间的 50%，则会返回不健康的健康检查结果。

```typescript
@@filename(health.controller)
@Controller('health')
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly disk: DiskHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.disk.checkStorage('storage', { path: '/', thresholdPercent: 0.5 }),
    ]);
  }
}
@@switch
@Controller('health')
@Dependencies(HealthCheckService, DiskHealthIndicator)
export class HealthController {
  constructor(health, disk) {}

  @Get()
  @HealthCheck()
  healthCheck() {
    return this.health.check([
      () => this.disk.checkStorage('storage', { path: '/', thresholdPercent: 0.5 }),
    ])
  }
}
```

通过 `DiskHealthIndicator.checkStorage` 方法，你还可以检查是否超过了固定的存储空间阈值。下面的示例会检查路径 `/my-app/`，如果其占用空间超过 250 GB，则健康检查会被判定为不健康。

```typescript
@@filename(health.controller)
// 在 `HealthController` 类中

@Get()
@HealthCheck()
check() {
  return this.health.check([
    () => this.disk.checkStorage('storage', {  path: '/', threshold: 250 * 1024 * 1024 * 1024, })
  ]);
}
```

#### 内存健康指示器

为了确保你的进程不会超过指定的内存限制，可以使用 `MemoryHealthIndicator`。下面的示例演示了如何检查进程的堆内存（heap）。

> info **提示** 堆（Heap）是用于存放动态分配内存（即通过 malloc 分配的内存）的内存区域。分配在堆上的内存会一直保留，直到发生以下情况之一：
>
> - 该内存被 _free_ 释放
> - 程序终止

```typescript
@@filename(health.controller)
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private memory: MemoryHealthIndicator,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
    ]);
  }
}
@@switch
@Controller('health')
@Dependencies(HealthCheckService, MemoryHealthIndicator)
export class HealthController {
  constructor(health, memory) {}

  @Get()
  @HealthCheck()
  healthCheck() {
    return this.health.check([
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
    ])
  }
}
```

你还可以通过 `MemoryHealthIndicator.checkRSS` 方法检查进程的 RSS（Resident Set Size，常驻集大小）。下面的示例会在进程分配的内存超过 150 MB 时，返回不健康的响应码。

> info **提示** RSS 用于表示分配给该进程并实际驻留在 RAM 中的内存总量。
> 它不包括已被交换出去的内存，但包括共享库中实际驻留在内存中的页面，也包括所有的栈和堆内存。

```typescript
@@filename(health.controller)
// 在 `HealthController` 类中

@Get()
@HealthCheck()
check() {
  return this.health.check([
    () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024),
  ]);
}
```

#### 自定义健康指示器

在某些情况下，`@nestjs/terminus` 提供的预定义健康指示器并不能覆盖你所有的健康检查需求。这时，你可以根据实际需要设置自定义健康指示器。

我们先来创建一个服务，用于实现自定义指示器。为了帮助理解指示器的结构，这里以 `DogHealthIndicator` 为例。该服务会在所有 `Dog` 对象的 type 属性为 `'goodboy'` 时，返回状态 `'up'`。如果条件不满足，则抛出错误。

```typescript
@@filename(dog.health)
import { Injectable } from '@nestjs/common';
import { HealthIndicatorService } from '@nestjs/terminus';

export interface Dog {
  name: string;
  type: string;
}

@Injectable()
export class DogHealthIndicator {
  constructor(
    private readonly healthIndicatorService: HealthIndicatorService
  ) {}

  private dogs: Dog[] = [
    { name: 'Fido', type: 'goodboy' },
    { name: 'Rex', type: 'badboy' },
  ];

  async isHealthy(key: string){
    const indicator = this.healthIndicatorService.check(key);
    const badboys = this.dogs.filter(dog => dog.type === 'badboy');
    const isHealthy = badboys.length === 0;

    if (!isHealthy) {
      return indicator.down({ badboys: badboys.length });
    }

    return indicator.up();
  }
}
@@switch
import { Injectable } from '@nestjs/common';
import { HealthIndicatorService } from '@nestjs/terminus';

@Injectable()
@Dependencies(HealthIndicatorService)
export class DogHealthIndicator {
  constructor(healthIndicatorService) {
    this.healthIndicatorService = healthIndicatorService;
  }

  private dogs = [
    { name: 'Fido', type: 'goodboy' },
    { name: 'Rex', type: 'badboy' },
  ];

  async isHealthy(key){
    const indicator = this.healthIndicatorService.check(key);
    const badboys = this.dogs.filter(dog => dog.type === 'badboy');
    const isHealthy = badboys.length === 0;

    if (!isHealthy) {
      return indicator.down({ badboys: badboys.length });
    }

    return indicator.up();
  }
}
```

接下来，需要将健康指示器注册为提供者。

```typescript
@@filename(health.module)
import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { DogHealthIndicator } from './dog.health';

@Module({
  controllers: [HealthController],
  imports: [TerminusModule],
  providers: [DogHealthIndicator]
})
export class HealthModule { }
```

> info **提示** 在实际项目中，`DogHealthIndicator` 应当在单独的模块（如 `DogModule`）中提供，然后由 `HealthModule` 导入。

最后一步是在健康检查接口中，调用我们自定义的健康指示器。回到 `HealthController`，在 `check` 方法中加入该指示器。

```typescript
@@filename(health.controller)
import { HealthCheckService, HealthCheck } from '@nestjs/terminus';
import { Injectable, Dependencies, Get } from '@nestjs/common';
import { DogHealthIndicator } from './dog.health';

@Injectable()
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private dogHealthIndicator: DogHealthIndicator
  ) {}

  @Get()
  @HealthCheck()
  healthCheck() {
    return this.health.check([
      () => this.dogHealthIndicator.isHealthy('dog'),
    ])
  }
}
@@switch
import { HealthCheckService, HealthCheck } from '@nestjs/terminus';
import { Injectable, Get } from '@nestjs/common';
import { DogHealthIndicator } from './dog.health';

@Injectable()
@Dependencies(HealthCheckService, DogHealthIndicator)
export class HealthController {
  constructor(
    health,
    dogHealthIndicator
  ) {
    this.health = health;
    this.dogHealthIndicator = dogHealthIndicator;
  }

  @Get()
  @HealthCheck()
  healthCheck() {
    return this.health.check([
      () => this.dogHealthIndicator.isHealthy('dog'),
    ])
  }
}
```

#### 日志记录

Terminus 只会记录错误信息，例如当健康检查失败时。通过 `TerminusModule.forRoot()` 方法，你可以更灵活地控制错误日志的记录方式，甚至可以完全接管日志记录。

本节将带你了解如何创建自定义日志记录器 `TerminusLogger`。该日志记录器继承自内置日志记录器（Logger），因此你可以按需选择性地重写日志记录器的部分功能。

> info **提示** 如果你想了解更多关于 NestJS 自定义日志记录器的信息，[请阅读此处](/techniques/logger#injecting-a-custom-logger)。

```typescript
@@filename(terminus-logger.service)
import { Injectable, Scope, ConsoleLogger } from '@nestjs/common';

@Injectable({ scope: Scope.TRANSIENT })
export class TerminusLogger extends ConsoleLogger {
  error(message: any, stack?: string, context?: string): void;
  error(message: any, ...optionalParams: any[]): void;
  error(
    message: unknown,
    stack?: unknown,
    context?: unknown,
    ...rest: unknown[]
  ): void {
    // 在这里重写错误信息的日志记录方式
  }
}
```

创建好自定义日志记录器后，只需将其传递给 `TerminusModule.forRoot()` 即可，如下所示：

```typescript
@@filename(health.module)
@Module({
imports: [
  TerminusModule.forRoot({
    logger: TerminusLogger,
  }),
],
})
export class HealthModule {}
```

如果你希望完全屏蔽 Terminus 输出的所有日志（包括错误日志），可以按如下方式配置 Terminus：

```typescript
@@filename(health.module)
@Module({
imports: [
  TerminusModule.forRoot({
    logger: false,
  }),
],
})
export class HealthModule {}
```

Terminus 允许你配置健康检查错误在日志中的展示方式。

| 错误日志样式   | 描述                                                                    | 示例图                                                               |
| :------------- | :---------------------------------------------------------------------- | :------------------------------------------------------------------- |
| `json`（默认） | 当发生错误时，以 JSON 对象的形式输出健康检查结果摘要                    | <figure><img src="/assets/Terminus_Error_Log_Json.png" /></figure>   |
| `pretty`       | 当发生错误时，以格式化框体输出健康检查结果摘要，并高亮显示成功/失败结果 | <figure><img src="/assets/Terminus_Error_Log_Pretty.png" /></figure> |

你可以通过如下配置项 `errorLogStyle` 来切换日志样式：

```typescript
@@filename(health.module)
@Module({
  imports: [
    TerminusModule.forRoot({
      errorLogStyle: 'pretty',
    }),
  ]
})
export class HealthModule {}
```

#### 优雅关闭超时时间

如果你的应用需要延迟其关闭（shutdown）过程，Terminus 可以为你处理。
这个设置在使用如 Kubernetes 这样的编排器（orchestrator）时尤其有用。
通过将延迟时间设置得比就绪检查（readiness check）间隔稍长，你可以在关闭容器时实现零停机时间（zero downtime）。

```typescript
@@filename(health.module)
@Module({
  imports: [
    TerminusModule.forRoot({
      gracefulShutdownTimeoutMs: 1000,
    }),
  ]
})
export class HealthModule {}
```

#### 更多示例

更多可用的示例请参见 [这里](https://github.com/nestjs/terminus/tree/master/sample)。
