### Prisma

[Prisma](https://www.prisma.io) 是一个 [开源](https://github.com/prisma/prisma) 的 ORM（Object-Relational Mapping，对象关系映射）工具，适用于 Node.js 和 TypeScript。它作为编写原生 SQL 或使用其他数据库访问工具（如 SQL 查询构建器 [knex.js](https://knexjs.org/) 或其他 ORM，如 [TypeORM](https://typeorm.io/) 和 [Sequelize](https://sequelize.org/)）的**替代方案**。Prisma 目前支持 PostgreSQL、MySQL、SQL Server、SQLite、MongoDB 以及 CockroachDB（[预览版](https://www.prisma.io/docs/reference/database-reference/supported-databases)）。

虽然 Prisma 可以与原生 JavaScript 一起使用，但它充分拥抱 TypeScript，并在类型安全（type-safety）方面提供了超越 TypeScript 生态中其他 ORM 的保障。你可以在[这里](https://www.prisma.io/docs/concepts/more/comparisons/prisma-and-typeorm#type-safety)找到 Prisma 与 TypeORM 在类型安全方面的详细对比。

> info **注意** 如果你想快速了解 Prisma 的工作原理，可以参考 [快速上手](https://www.prisma.io/docs/getting-started/quickstart) 或阅读 [文档中的介绍](https://www.prisma.io/docs/understand-prisma/introduction)。在 [`prisma-examples`](https://github.com/prisma/prisma-examples/) 仓库中也有可直接运行的 [REST](https://github.com/prisma/prisma-examples/tree/b53fad046a6d55f0090ddce9fd17ec3f9b95cab3/orm/nest) 和 [GraphQL](https://github.com/prisma/prisma-examples/tree/b53fad046a6d55f0090ddce9fd17ec3f9b95cab3/orm/nest-graphql) 示例。

#### 快速开始

本教程将带你从零开始，学习如何在 NestJS 中集成 Prisma。你将构建一个示例 NestJS 应用，并实现一个可以读写数据库数据的 RESTful API。

为了简化操作，本指南将使用 [SQLite](https://sqlite.org/) 数据库，无需额外搭建数据库服务器。当然，即使你使用的是 PostgreSQL 或 MySQL，也可以按照本指南操作 —— 在相关步骤会有针对这些数据库的额外说明。

> info **注意** 如果你已经有现有项目，想要迁移到 Prisma，可以参考[为现有项目添加 Prisma 的指南](https://www.prisma.io/docs/getting-started/setup-prisma/add-to-existing-project-typescript-postgres)。如果你是从 TypeORM 迁移，可以阅读[从 TypeORM 迁移到 Prisma 的指南](https://www.prisma.io/docs/guides/migrate-to-prisma/migrate-from-typeorm)。

#### 创建你的 NestJS 项目

首先，安装 Nest 命令行工具（Nest CLI），并通过以下命令创建项目骨架：

```bash
$ npm install -g @nestjs/cli
$ nest new hello-prisma
```

更多关于该命令创建的项目文件说明，请参见 [First steps](https://docs.nestjs.com/first-steps) 页面。你现在可以运行 `npm start` 启动应用。此时，运行在 `http://localhost:3000/` 的 RESTful 接口只包含一个在 `src/app.controller.ts` 中实现的路由。接下来你将逐步实现更多路由，用于存储和获取 _用户_ 和 _帖子_ 的数据。

#### 配置 Prisma

首先，在你的项目中将 Prisma CLI 作为开发依赖进行安装：

```bash
$ cd hello-prisma
$ npm install prisma --save-dev
```

在接下来的步骤中，我们将使用 [Prisma CLI](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-cli)。作为最佳实践，推荐通过在命令前加上 `npx` 前缀来本地调用 CLI：

```bash
$ npx prisma
```

<details>
<summary>如果你使用 Yarn，请展开</summary>

如果你使用 Yarn，可以通过以下方式安装 Prisma CLI：

```bash
$ yarn add prisma --dev
```

安装完成后，可以通过在命令前加上 `yarn` 前缀来调用 CLI：

```bash
$ yarn prisma
```

</details>

现在，使用 Prisma CLI 的 `init` 命令创建你的初始 Prisma 配置：

```bash
$ npx prisma init
```

该命令会创建一个新的 `prisma` 目录，包含以下内容：

- `schema.prisma`：用于指定数据库连接信息并包含数据库模式定义
- `.env`：一个 [dotenv](https://github.com/motdotla/dotenv) 文件，通常用于通过一组环境变量存储你的数据库凭据

#### 配置数据库连接

你的数据库连接配置在 `schema.prisma` 文件中的 `datasource` 块。默认情况下，`provider` 字段设置为 `postgresql`，但本指南使用的是 SQLite 数据库，因此你需要将 `datasource` 块的 `provider` 字段修改为 `sqlite`：

```groovy
datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}
```

接下来，打开 `.env` 文件，将 `DATABASE_URL` 环境变量修改如下：

```bash
DATABASE_URL="file:./dev.db"
```

请确保你已经配置了 [ConfigModule](https://docs.nestjs.com/techniques/configuration)（配置模块 ConfigModule），否则 `DATABASE_URL` 变量将无法从 `.env` 文件中读取。

SQLite 数据库本质上是一个简单的文件，无需服务器即可使用。因此，你只需将连接 URL 指向本地文件（本例中为 `dev.db`），而无需像其他数据库那样配置 _host_ 和 _port_。该文件会在下一步中自动创建。

<details>
<summary>如果你使用的是 PostgreSQL、MySQL、MsSQL 或 Azure SQL，请展开查看</summary>

对于 PostgreSQL 和 MySQL，你需要将连接 URL 配置为指向 _数据库服务器_。你可以在[这里](https://www.prisma.io/docs/reference/database-reference/connection-urls)了解所需的连接 URL 格式。

**PostgreSQL**

如果你使用的是 PostgreSQL，需要按如下方式调整 `schema.prisma` 和 `.env` 文件：

**`schema.prisma`**

```groovy
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}
```

**`.env`**

```bash
DATABASE_URL="postgresql://USER:PASSWORD@HOST:PORT/DATABASE?schema=SCHEMA"
```

将所有大写字母的占位符替换为你的数据库凭据。如果你不确定 `SCHEMA` 占位符的值，通常可以使用默认值 `public`：

```bash
DATABASE_URL="postgresql://USER:PASSWORD@HOST:PORT/DATABASE?schema=public"
```

如果你想了解如何搭建 PostgreSQL 数据库，可以参考这篇关于[在 Heroku 上搭建免费 PostgreSQL 数据库](https://dev.to/prisma/how-to-setup-a-free-postgresql-database-on-heroku-1dc1)的指南。

**MySQL**

如果你使用的是 MySQL，需要按如下方式调整 `schema.prisma` 和 `.env` 文件：

**`schema.prisma`**

```groovy
datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}
```

**`.env`**

```bash
DATABASE_URL="mysql://USER:PASSWORD@HOST:PORT/DATABASE"
```

将所有大写字母的占位符替换为你的数据库凭据。

**Microsoft SQL Server / Azure SQL Server**

如果你使用的是 Microsoft SQL Server 或 Azure SQL Server，需要按如下方式调整 `schema.prisma` 和 `.env` 文件：

**`schema.prisma`**

```groovy
datasource db {
  provider = "sqlserver"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}
```

**`.env`**

将所有大写字母的占位符替换为你的数据库凭据。如果你不确定 `encrypt` 占位符的值，通常可以使用默认值 `true`：

```bash
DATABASE_URL="sqlserver://HOST:PORT;database=DATABASE;user=USER;password=PASSWORD;encrypt=true"
```

</details>

#### 使用 Prisma Migrate 创建两张数据库表

本节将指导你如何使用 [Prisma Migrate](https://www.prisma.io/docs/concepts/components/prisma-migrate) 在数据库中创建两张新表。Prisma Migrate 会根据你在 Prisma schema 中声明式的数据模型定义生成 SQL 迁移文件。这些迁移文件可以完全自定义，你可以根据需要配置底层数据库的其他特性，或添加额外命令（例如用于数据填充 seeding）。

请在你的 `schema.prisma` 文件中添加以下两个模型：

```groovy
model User {
  id    Int     @default(autoincrement()) @id
  email String  @unique
  name  String?
  posts Post[]
}

model Post {
  id        Int      @default(autoincrement()) @id
  title     String
  content   String?
  published Boolean? @default(false)
  author    User?    @relation(fields: [authorId], references: [id])
  authorId  Int?
}
```

完成 Prisma 模型的定义后，你可以生成 SQL 迁移文件并将其应用到数据库。请在终端中运行以下命令：

```bash
$ npx prisma migrate dev --name init
```

`prisma migrate dev` 命令会生成 SQL 文件，并直接在数据库上执行这些文件。在本例中，以下迁移文件被创建在现有的 `prisma` 目录下：

```bash
$ tree prisma
prisma
├── dev.db
├── migrations
│   └── 20201207100915_init
│       └── migration.sql
└── schema.prisma
```

<details>
<summary>展开以查看生成的 SQL 语句</summary>

以下表结构已在你的 SQLite 数据库中创建：

```sql
-- CreateTable
CREATE TABLE "User" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "email" TEXT NOT NULL,
    "name" TEXT
);

-- CreateTable
CREATE TABLE "Post" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "title" TEXT NOT NULL,
    "content" TEXT,
    "published" BOOLEAN DEFAULT false,
    "authorId" INTEGER,

    FOREIGN KEY ("authorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "User.email_unique" ON "User"("email");
```

</details>

#### 安装并生成 Prisma 客户端（Prisma Client）

Prisma 客户端（Prisma Client）是一款类型安全的数据库客户端，它会根据你的 Prisma 模型定义自动 _生成_。得益于这种方式，Prisma 客户端能够为你的模型 _量身定制_ [CRUD](https://www.prisma.io/docs/concepts/components/prisma-client/crud) 操作。

要在你的项目中安装 Prisma 客户端，请在终端中运行以下命令：

```bash
$ npm install @prisma/client
```

请注意，在安装过程中，Prisma 会自动为你执行 `prisma generate` 命令。今后，每当你修改 Prisma 模型时，都需要运行该命令，以便更新生成的 Prisma 客户端。

> info **注意** `prisma generate` 命令会读取你的 Prisma schema，并在 `node_modules/@prisma/client` 目录下更新生成的 Prisma 客户端库。

#### 在 NestJS 服务中使用 Prisma Client

现在，你已经可以通过 Prisma Client 发送数据库查询。如果你想进一步了解如何使用 Prisma Client 构建查询，可以参考 [API 文档](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/crud)。

在搭建 NestJS 应用时，建议将 Prisma Client 的数据库查询 API 封装在服务（Service）中。首先，你可以创建一个新的 `PrismaService`，用于实例化 `PrismaClient` 并连接数据库。

在 `src` 目录下，新建一个名为 `prisma.service.ts` 的文件，并添加如下代码：

```typescript
import { Injectable, OnModuleInit } from '@nestjs/common'
import { PrismaClient } from '@prisma/client'

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit {
  async onModuleInit() {
    await this.$connect()
  }
}
```

> info **注意** `onModuleInit` 是可选的——如果省略该方法，Prisma 会在首次访问数据库时自动连接。

接下来，你可以编写服务，用于通过 Prisma schema 中的 `User` 和 `Post` 模型进行数据库操作。

同样在 `src` 目录下，新建 `user.service.ts` 文件，并添加如下代码：

```typescript
import { Injectable } from '@nestjs/common'
import { PrismaService } from './prisma.service'
import { User, Prisma } from '@prisma/client'

@Injectable()
export class UsersService {
  constructor(private prisma: PrismaService) {}

  async user(userWhereUniqueInput: Prisma.UserWhereUniqueInput): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: userWhereUniqueInput,
    })
  }

  async users(params: {
    skip?: number
    take?: number
    cursor?: Prisma.UserWhereUniqueInput
    where?: Prisma.UserWhereInput
    orderBy?: Prisma.UserOrderByWithRelationInput
  }): Promise<User[]> {
    const { skip, take, cursor, where, orderBy } = params
    return this.prisma.user.findMany({
      skip,
      take,
      cursor,
      where,
      orderBy,
    })
  }

  async createUser(data: Prisma.UserCreateInput): Promise<User> {
    return this.prisma.user.create({
      data,
    })
  }

  async updateUser(params: {
    where: Prisma.UserWhereUniqueInput
    data: Prisma.UserUpdateInput
  }): Promise<User> {
    const { where, data } = params
    return this.prisma.user.update({
      data,
      where,
    })
  }

  async deleteUser(where: Prisma.UserWhereUniqueInput): Promise<User> {
    return this.prisma.user.delete({
      where,
    })
  }
}
```

注意，这里你使用了 Prisma Client 自动生成的类型，确保服务中暴露的方法类型安全，无需为模型单独编写类型或数据传输对象（DTO）文件，减少了模板代码（Boilerplate）。

接下来，为 `Post` 模型实现同样的服务。

继续在 `src` 目录下，新建 `post.service.ts` 文件，并添加如下代码：

```typescript
import { Injectable } from '@nestjs/common'
import { PrismaService } from './prisma.service'
import { Post, Prisma } from '@prisma/client'

@Injectable()
export class PostsService {
  constructor(private prisma: PrismaService) {}

  async post(postWhereUniqueInput: Prisma.PostWhereUniqueInput): Promise<Post | null> {
    return this.prisma.post.findUnique({
      where: postWhereUniqueInput,
    })
  }

  async posts(params: {
    skip?: number
    take?: number
    cursor?: Prisma.PostWhereUniqueInput
    where?: Prisma.PostWhereInput
    orderBy?: Prisma.PostOrderByWithRelationInput
  }): Promise<Post[]> {
    const { skip, take, cursor, where, orderBy } = params
    return this.prisma.post.findMany({
      skip,
      take,
      cursor,
      where,
      orderBy,
    })
  }

  async createPost(data: Prisma.PostCreateInput): Promise<Post> {
    return this.prisma.post.create({
      data,
    })
  }

  async updatePost(params: {
    where: Prisma.PostWhereUniqueInput
    data: Prisma.PostUpdateInput
  }): Promise<Post> {
    const { data, where } = params
    return this.prisma.post.update({
      data,
      where,
    })
  }

  async deletePost(where: Prisma.PostWhereUniqueInput): Promise<Post> {
    return this.prisma.post.delete({
      where,
    })
  }
}
```

你的 `UsersService` 和 `PostsService` 目前封装了 Prisma Client 提供的 CRUD 查询。在实际应用中，服务还可以承载业务逻辑。例如，你可以在 `UsersService` 中添加 `updatePassword` 方法，用于更新用户密码。

记得在 `AppModule` 中注册这些新服务。

##### 在主控制器中实现 REST API 路由

最后，你将使用前面创建的服务，在应用的主控制器（AppController）中实现各类路由。为简化演示，本指南将所有路由都放在已存在的 `AppController` 类中。

将 `app.controller.ts` 文件内容替换为如下代码：

```typescript
import { Controller, Get, Param, Post, Body, Put, Delete } from '@nestjs/common'
import { UsersService } from './user.service'
import { PostsService } from './post.service'
import { User as UserModel, Post as PostModel } from '@prisma/client'

@Controller()
export class AppController {
  constructor(
    private readonly userService: UsersService,
    private readonly postService: PostsService
  ) {}

  @Get('post/:id')
  async getPostById(@Param('id') id: string): Promise<PostModel> {
    return this.postService.post({ id: Number(id) })
  }

  @Get('feed')
  async getPublishedPosts(): Promise<PostModel[]> {
    return this.postService.posts({
      where: { published: true },
    })
  }

  @Get('filtered-posts/:searchString')
  async getFilteredPosts(@Param('searchString') searchString: string): Promise<PostModel[]> {
    return this.postService.posts({
      where: {
        OR: [
          {
            title: { contains: searchString },
          },
          {
            content: { contains: searchString },
          },
        ],
      },
    })
  }

  @Post('post')
  async createDraft(
    @Body() postData: { title: string; content?: string; authorEmail: string }
  ): Promise<PostModel> {
    const { title, content, authorEmail } = postData
    return this.postService.createPost({
      title,
      content,
      author: {
        connect: { email: authorEmail },
      },
    })
  }

  @Post('user')
  async signupUser(@Body() userData: { name?: string; email: string }): Promise<UserModel> {
    return this.userService.createUser(userData)
  }

  @Put('publish/:id')
  async publishPost(@Param('id') id: string): Promise<PostModel> {
    return this.postService.updatePost({
      where: { id: Number(id) },
      data: { published: true },
    })
  }

  @Delete('post/:id')
  async deletePost(@Param('id') id: string): Promise<PostModel> {
    return this.postService.deletePost({ id: Number(id) })
  }
}
```

该控制器实现了如下路由：

###### `GET`

- `/post/:id`：根据 `id` 获取单个帖子
- `/feed`：获取所有已发布的帖子
- `/filter-posts/:searchString`：根据 `title` 或 `content` 过滤帖子

###### `POST`

- `/post`：创建新帖子
  - 请求体：
    - `title: String`（必填）：帖子的标题
    - `content: String`（可选）：帖子的内容
    - `authorEmail: String`（必填）：发帖用户的邮箱
- `/user`：创建新用户
  - 请求体：
    - `email: String`（必填）：用户邮箱
    - `name: String`（可选）：用户名

###### `PUT`

- `/publish/:id`：根据 `id` 发布帖子

###### `DELETE`

- `/post/:id`：根据 `id` 删除帖子

#### 总结

在本教程中，你学习了如何结合使用 Prisma 和 NestJS 来实现一个 RESTful 接口。实现接口路由的控制器会调用 `PrismaService`，而该服务则通过 Prisma Client 向数据库发送查询，以满足传入请求的数据需求。

如果你想进一步了解如何在 NestJS 中使用 Prisma，欢迎参考以下资源：

- [NestJS & Prisma](https://www.prisma.io/nestjs)
- [REST 与 GraphQL 的开箱即用示例项目](https://github.com/prisma/prisma-examples/)
- [适用于生产环境的入门模板](https://github.com/notiz-dev/nestjs-prisma-starter#instructions)
- [视频：使用 NestJS 与 Prisma 访问数据库（5 分钟）](https://www.youtube.com/watch?v=UlVJ340UEuk&ab_channel=Prisma)（作者：[Marc Stammerjohann](https://github.com/marcjulian)）
