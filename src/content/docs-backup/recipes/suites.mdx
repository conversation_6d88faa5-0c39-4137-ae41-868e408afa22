### Suites（原 Automock）

Suites 是一个主张明确且灵活的测试元框架，旨在提升后端系统的软件测试体验。通过将多种测试工具整合到统一框架中，Suites 简化了可靠测试的创建流程，帮助开发者确保高质量软件的开发。

> info **提示** `Suites` 是第三方包，并非由 NestJS 核心团队维护。如遇到该库相关问题，请前往[对应仓库](https://github.com/suites-dev/suites)反馈。

#### 简介

控制反转（Inversion of Control，IoC）是 NestJS 框架中的一项基础原则，使其具备模块化和可测试的架构。虽然 NestJS 提供了内置工具用于创建测试模块（Testing Module），但 Suites 提供了一种替代方案，强调对单元或小型单元组的隔离测试。Suites 使用依赖的虚拟容器（virtual container），自动生成模拟对象（Mock），无需在 IoC（或依赖注入，Dependency Injection）容器中手动替换每个提供者（Provider）。这种方式既可以替代，也可以与 NestJS 的 `Test.createTestingModule` 方法配合使用，根据实际需求为单元测试带来更高的灵活性。

#### 安装

要在 NestJS 中使用 Suites，请安装以下必要依赖：

```bash
$ npm i -D @suites/unit @suites/di.nestjs @suites/doubles.jest
```

> info **提示** `Suites` 同样支持 Vitest 和 Sinon 作为测试替身（test double），可分别使用 `@suites/doubles.vitest` 和 `@suites/doubles.sinon`。

#### 示例与模块设置

假设我们有一个 `CatsService` 的模块设置，其中包含了 `CatsApiService`、`CatsDAL`、`HttpClient` 和 `Logger`。这将作为本示例的基础：

```typescript
@@filename(cats.module)
import { HttpModule } from '@nestjs/axios';
import { PrismaModule } from '../prisma.module';

@Module({
  imports: [HttpModule.register({ baseUrl: 'https://api.cats.com/' }), PrismaModule],
  providers: [CatsService, CatsApiService, CatsDAL, Logger],
  exports: [CatsService],
})
export class CatsModule {}
```

`HttpModule` 和 `PrismaModule` 都向宿主模块导出了提供者。

接下来我们将以隔离的方式测试 `CatsHttpService`。该服务负责从 API 获取猫的数据，并记录日志。

```typescript
@@filename(cats-http.service)
@Injectable()
export class CatsHttpService {
  constructor(private httpClient: HttpClient, private logger: Logger) {}

  async fetchCats(): Promise<Cat[]> {
    this.logger.log('Fetching cats from the API');
    const response = await this.httpClient.get('/cats');
    return response.data;
  }
}
```

我们希望对 `CatsHttpService` 进行隔离测试，并对其依赖项 `HttpClient` 和 `Logger` 进行模拟（Mock）。Suites 可以通过 `TestBed` 的 `.solitary()` 方法轻松实现这一点。

```typescript
@@filename(cats-http.service.spec)
import { TestBed, Mocked } from '@suites/unit';

describe('Cats Http Service 单元测试', () => {
  let catsHttpService: CatsHttpService;
  let httpClient: Mocked<HttpClient>;
  let logger: Mocked<Logger>;

  beforeAll(async () => {
    // 隔离 CatsHttpService，并模拟 HttpClient 和 Logger
    const { unit, unitRef } = await TestBed.solitary(CatsHttpService).compile();

    catsHttpService = unit;
    httpClient = unitRef.get(HttpClient);
    logger = unitRef.get(Logger);
  });

  it('应从 API 获取猫的数据并记录操作日志', async () => {
    const catsFixtures: Cat[] = [{ id: 1, name: 'Catty' }, { id: 2, name: 'Mitzy' }];
    httpClient.get.mockResolvedValue({ data: catsFixtures });

    const cats = await catsHttpService.fetchCats();

    expect(logger.log).toHaveBeenCalledWith('Fetching cats from the API');
    expect(httpClient.get).toHaveBeenCalledWith('/cats');
    expect(cats).toEqual<Cat[]>(catsFixtures);
  });
});
```

在上面的示例中，Suites 会自动为 `CatsHttpService` 的所有依赖项生成模拟对象，通过 `TestBed.solitary()` 方法让测试设置变得更加简单，无需手动为每个依赖项编写模拟。

- 依赖项自动模拟：Suites 会为被测试单元的所有依赖项自动生成模拟对象。
- 模拟对象初始行为为空：这些模拟对象默认没有预定义行为，需要在测试中根据需要指定。
- `unit` 和 `unitRef` 属性：
  - `unit` 指向被测试类的实际实例，并自动注入了模拟依赖项。
  - `unitRef` 允许你访问这些被模拟的依赖项。

#### 使用 `TestingModule` 测试 `CatsApiService`

对于 `CatsApiService`，我们需要确保在宿主模块 `CatsModule` 中正确导入并配置了 `HttpModule`。这包括验证 `Axios` 的基础 URL（以及其他配置项）是否设置正确。

在本例中，我们不使用 Suites，而是使用 Nest 的 `TestingModule` 来测试 `HttpModule` 的实际配置。我们将利用 `nock` 来模拟 HTTP 请求，而不是在本场景下模拟 `HttpClient`。

```typescript
@@filename(cats-api.service)
import { HttpClient } from '@nestjs/axios';

@Injectable()
export class CatsApiService {
  constructor(private httpClient: HttpClient) {}

  async getCatById(id: number): Promise<Cat> {
    const response = await this.httpClient.get(`/cats/${id}`);
    return response.data;
  }
}
```

我们需要使用真实（未被模拟）的 `HttpClient` 来测试 `CatsApiService`，以确保依赖注入和 `Axios`（http）的配置是正确的。这涉及导入 `CatsModule` 并使用 `nock` 进行 HTTP 请求模拟。

```typescript
@@filename(cats-api.service.integration.test)
import { Test } from '@nestjs/testing';
import * as nock from 'nock';

describe('Cats Api Service 集成测试', () => {
  let catsApiService: CatsApiService;

  beforeAll(async () => {
    const moduleRef = await Test.createTestingModule({
      imports: [CatsModule],
    }).compile();

    catsApiService = moduleRef.get(CatsApiService);
  });

  afterEach(() => {
    nock.cleanAll();
  });

  it('应当使用真实 HttpClient 根据 id 获取猫信息', async () => {
    const catFixture: Cat = { id: 1, name: 'Catty' };

    nock('https://api.cats.com') // 该 URL 应与 HttpModule 注册时配置的保持一致
      .get('/cats/1')
      .reply(200, catFixture);

    const cat = await catsApiService.getCatById(1);
    expect(cat).toEqual<Cat>(catFixture);
  });
});
```

#### Sociable Testing 示例

接下来，我们来测试 `CatsService`，它依赖于 `CatsApiService` 和 `CatsDAL`。我们将对 `CatsApiService` 进行模拟（mock），并暴露 `CatsDAL`。

```typescript
@@filename(cats.dal)
import { PrismaClient } from '@prisma/client';

@Injectable()
export class CatsDAL {
  constructor(private prisma: PrismaClient) {}

  async saveCat(cat: Cat): Promise<Cat> {
    return this.prisma.cat.create({data: cat});
  }
}
```

接下来是 `CatsService`，它依赖于 `CatsApiService` 和 `CatsDAL`：

```typescript
@@filename(cats.service)
@Injectable()
export class CatsService {
  constructor(
    private catsApiService: CatsApiService,
    private catsDAL: CatsDAL
  ) {}

  async getAndSaveCat(id: number): Promise<Cat> {
    const cat = await this.catsApiService.getCatById(id);
    return this.catsDAL.saveCat(cat);
  }
}
```

现在，我们使用 Suites 进行友好测试，来测试 `CatsService`：

```typescript
@@filename(cats.service.spec)
import { TestBed, Mocked } from '@suites/unit';
import { PrismaClient } from '@prisma/client';

describe('Cats Service Sociable Unit Test', () => {
  let catsService: CatsService;
  let prisma: Mocked<PrismaClient>;
  let catsApiService: Mocked<CatsApiService>;

  beforeAll(async () => {
    // 友好测试环境搭建，暴露 CatsDAL 并模拟 CatsApiService
    const { unit, unitRef } = await TestBed.sociable(CatsService)
      .expose(CatsDAL)
      .mock(CatsApiService)
      .final({ getCatById: async () => ({ id: 1, name: 'Catty' })})
      .compile();

    catsService = unit;
    prisma = unitRef.get(PrismaClient);
  });

  it('应根据 id 获取猫并保存', async () => {
    const catFixture: Cat = { id: 1, name: 'Catty' };
    prisma.cat.create.mockResolvedValue(catFixture);

    const savedCat = await catsService.getAndSaveCat(1);

    expect(prisma.cat.create).toHaveBeenCalledWith({ data: catFixture });
    expect(savedCat).toEqual(catFixture);
  });
});
```

在本示例中，我们使用 `.sociable()` 方法来搭建测试环境。通过 `.expose()` 方法允许与 `CatsDAL` 进行真实交互，同时使用 `.mock()` 方法对 `CatsApiService` 进行模拟。`.final()` 方法为 `CatsApiService` 设定固定行为，确保测试结果的一致性。

这种方式强调对 `CatsService` 进行测试时，能够与 `CatsDAL` 真实交互（涉及对 Prisma 的操作）。Suites 会直接使用 `CatsDAL`，而仅对其依赖（如 Prisma）进行模拟。

需要注意的是，这种方式**仅用于验证行为**，与加载完整测试模块不同。友好测试适用于在隔离直接依赖的情况下，验证单元的行为，尤其适合关注单元行为和交互的场景。

#### 集成测试与数据库

对于 `CatsDAL`，可以选择使用真实数据库（如 SQLite 或 PostgreSQL，例如通过 Docker Compose）进行测试。但在本例中，我们将对 `Prisma` 进行模拟（Mock），重点关注“社交型测试”（Sociable Testing）。之所以模拟 `Prisma`，是为了避免 I/O 操作，将注意力集中在 `CatsService` 的行为本身。当然，你也可以选择进行包含真实 I/O 操作和实际数据库的测试。

#### 社交型单元测试、集成测试与模拟（Mock）

- 社交型单元测试：关注单元之间的交互和行为，同时对其更深层的依赖进行模拟。在本例中，我们模拟了 `Prisma` 并暴露了 `CatsDAL`。

- 集成测试：涉及真实的 I/O 操作和完整配置的依赖注入环境。测试 `CatsApiService` 结合 `HttpModule` 和 `nock` 就属于集成测试，因为它验证了 `HttpClient` 的真实配置和交互。在这种场景下，我们会使用 Nest 的 `测试模块（TestingModule）` 加载实际的模块配置。

**使用模拟（Mock）时需谨慎。** 一定要对 I/O 操作和依赖注入配置进行测试（尤其是涉及 HTTP 或数据库交互时）。在通过集成测试验证这些组件后，可以放心地在社交型单元测试中对它们进行模拟，从而专注于行为和交互。社交型测试主要用于验证单元在脱离直接依赖的情况下的行为，而集成测试则确保整体系统配置和 I/O 操作的正确性。

#### 测试 IoC 容器注册情况

验证依赖注入容器配置是否正确非常重要，这可以防止运行时出现错误。包括确保所有提供者、服务和模块都已正确注册和注入。测试依赖注入容器的配置有助于及早发现配置错误，避免仅在运行时才暴露问题。

为了确认 IoC 容器配置无误，我们可以编写一个集成测试，加载实际的模块配置，并验证所有提供者都已正确注册和注入。

```typescript
import { Test, TestingModule } from '@nestjs/testing'
import { CatsModule } from './cats.module'
import { CatsService } from './cats.service'

describe('Cats Module Integration Test', () => {
  let moduleRef: TestingModule

  beforeAll(async () => {
    moduleRef = await Test.createTestingModule({
      imports: [CatsModule],
    }).compile()
  })

  it('should resolve exported providers from the ioc container', () => {
    const catsService = moduleRef.get(CatsService)
    expect(catsService).toBeDefined()
  })
})
```

#### 孤立型、社交型、集成与端到端（E2E）测试的对比

#### 独立单元测试

- **关注点**：在完全隔离的环境下测试单个单元（类）。
- **适用场景**：测试 `CatsHttpService`。
- **使用工具**：测试套件的 `TestBed.solitary()` 方法。
- **示例**：通过模拟（Mock）`HttpClient`，测试 `CatsHttpService`。

#### 交互单元测试

- **关注点**：在模拟更深层依赖的同时，验证各单元之间的交互。
- **适用场景**：测试 `CatsService`，并模拟 `CatsApiService`，暴露 `CatsDAL`。
- **使用工具**：测试套件的 `TestBed.sociable()` 方法。
- **示例**：通过模拟 `Prisma`，测试 `CatsService`。

#### 集成测试

- **关注点**：涉及真实的 I/O 操作和完整配置的模块（依赖注入容器）。
- **适用场景**：结合 `HttpModule` 和 `nock` 测试 `CatsApiService`。
- **使用工具**：Nest 的 `TestingModule`。
- **示例**：测试 `HttpClient` 的真实配置与交互。

#### 端到端测试（E2E 测试）

- **关注点**：在更高聚合层级下，覆盖类和模块之间的交互。
- **适用场景**：从最终用户视角测试系统的完整行为。
- **使用工具**：Nest 的 `TestingModule`、`supertest`。
- **示例**：使用 `supertest` 模拟 HTTP 请求，测试 `CatsModule`。

如需了解端到端测试的详细设置与运行方法，请参阅 [NestJS 官方测试指南](https://docs.nestjs.com/fundamentals/testing#end-to-end-testing)。
