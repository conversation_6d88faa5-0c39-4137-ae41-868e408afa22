### Mongoose（MongoDB）

> **警告** 本文将介绍如何基于 **Mongoose** 包，从零开始自定义组件创建一个 `DatabaseModule`。因此，这种方案会带来较多的样板代码（overhead），而这些其实可以通过官方现成的 `@nestjs/mongoose` 专用包轻松实现。建议优先使用该包，详情请参见[这里](/techniques/mongodb)。

[Mongoose](https://mongoosejs.com) 是目前最流行的 [MongoDB](https://www.mongodb.org/) 对象建模工具。

#### 快速开始

要开始使用这个库，首先需要安装所有必需的依赖：

```typescript
$ npm install --save mongoose
```

第一步，我们需要通过 `connect()` 函数与数据库建立连接。`connect()` 函数会返回一个 `Promise`，因此我们需要创建一个[异步提供者](/fundamentals/async-components)。

```typescript
@@filename(database.providers)
import * as mongoose from 'mongoose';

export const databaseProviders = [
  {
    provide: 'DATABASE_CONNECTION',
    useFactory: (): Promise<typeof mongoose> =>
      mongoose.connect('mongodb://localhost/nest'),
  },
];
@@switch
import * as mongoose from 'mongoose';

export const databaseProviders = [
  {
    provide: 'DATABASE_CONNECTION',
    useFactory: () => mongoose.connect('mongodb://localhost/nest'),
  },
];
```

> info **提示** 按照最佳实践，我们将自定义提供者声明在单独的文件中，文件名以 `*.providers.ts` 结尾。

接下来，需要导出这些提供者，使其**可以被应用的其他部分访问**。

```typescript
@@filename(database.module)
import { Module } from '@nestjs/common';
import { databaseProviders } from './database.providers';

@Module({
  providers: [...databaseProviders],
  exports: [...databaseProviders],
})
export class DatabaseModule {}
```

现在，我们可以通过 `@Inject()` 装饰器注入 `Connection` 对象。任何依赖于 `Connection` 异步提供者的类都会在 `Promise` 被解析后再进行实例化。

#### 模型注入（Model injection）

在 Mongoose 中，一切都源自于 [Schema](https://mongoosejs.com/docs/guide.html)。我们先来定义一个 `CatSchema`：

```typescript
@@filename(schemas/cat.schema)
import * as mongoose from 'mongoose';

export const CatSchema = new mongoose.Schema({
  name: String,
  age: Number,
  breed: String,
});
```

`CatsSchema` 属于 `cats` 目录。该目录代表了 `CatsModule`。

现在我们来创建一个 **模型** 提供者：

```typescript
@@filename(cats.providers)
import { Connection } from 'mongoose';
import { CatSchema } from './schemas/cat.schema';

export const catsProviders = [
  {
    provide: 'CAT_MODEL',
    useFactory: (connection: Connection) => connection.model('Cat', CatSchema),
    inject: ['DATABASE_CONNECTION'],
  },
];
@@switch
import { CatSchema } from './schemas/cat.schema';

export const catsProviders = [
  {
    provide: 'CAT_MODEL',
    useFactory: (connection) => connection.model('Cat', CatSchema),
    inject: ['DATABASE_CONNECTION'],
  },
];
```

> warning **警告** 在实际项目中应避免使用 **魔法字符串**。`CAT_MODEL` 和 `DATABASE_CONNECTION` 都应该放在单独的 `constants.ts` 文件中。

现在我们可以通过 `@Inject()` 装饰器将 `CAT_MODEL` 注入到 `CatsService` 中：

```typescript
@@filename(cats.service)
import { Model } from 'mongoose';
import { Injectable, Inject } from '@nestjs/common';
import { Cat } from './interfaces/cat.interface';
import { CreateCatDto } from './dto/create-cat.dto';

@Injectable()
export class CatsService {
  constructor(
    @Inject('CAT_MODEL')
    private catModel: Model<Cat>,
  ) {}

  async create(createCatDto: CreateCatDto): Promise<Cat> {
    const createdCat = new this.catModel(createCatDto);
    return createdCat.save();
  }

  async findAll(): Promise<Cat[]> {
    return this.catModel.find().exec();
  }
}
@@switch
import { Injectable, Dependencies } from '@nestjs/common';

@Injectable()
@Dependencies('CAT_MODEL')
export class CatsService {
  constructor(catModel) {
    this.catModel = catModel;
  }

  async create(createCatDto) {
    const createdCat = new this.catModel(createCatDto);
    return createdCat.save();
  }

  async findAll() {
    return this.catModel.find().exec();
  }
}
```

在上面的示例中，我们使用了 `Cat` 接口。该接口继承自 mongoose 包中的 `Document`：

```typescript
import { Document } from 'mongoose'

export interface Cat extends Document {
  readonly name: string
  readonly age: number
  readonly breed: string
}
```

数据库连接是**异步**的，但 Nest 会让这个过程对最终用户完全透明。`CatModel` 类会等待数据库连接完成，而 `CatsService` 也会延迟，直到模型准备好可用。整个应用会在每个类都实例化后再启动。

下面是最终的 `CatsModule`：

```typescript
@@filename(cats.module)
import { Module } from '@nestjs/common';
import { CatsController } from './cats.controller';
import { CatsService } from './cats.service';
import { catsProviders } from './cats.providers';
import { DatabaseModule } from '../database/database.module';

@Module({
  imports: [DatabaseModule],
  controllers: [CatsController],
  providers: [
    CatsService,
    ...catsProviders,
  ],
})
export class CatsModule {}
```

> info **提示** 不要忘记将 `CatsModule` 导入到根模块 `AppModule` 中。

#### 示例

可用的完整示例请参考 [这里](https://github.com/nestjs/nest/tree/master/sample/14-mongoose-base)。
