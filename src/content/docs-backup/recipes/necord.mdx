### Necord

Necord 是一个强大的模块，可以简化 [Discord](https://discord.com) 机器人开发，并且能够无缝集成到你的 NestJS 应用中。

> info **注意** Necord 是第三方包，并非由 NestJS 核心团队官方维护。如果你遇到任何问题，请前往 [官方仓库](https://github.com/necordjs/necord) 反馈。

#### 安装

首先，你需要安装 Necord 及其依赖 [`Discord.js`](https://discord.js.org)。

```bash
$ npm install necord discord.js
```

#### 用法

要在项目中使用 Necord，需要导入 `NecordModule` 并进行必要的配置。

```typescript
@@filename(app.module)
import { Module } from '@nestjs/common';
import { NecordModule } from 'necord';
import { IntentsBitField } from 'discord.js';
import { AppService } from './app.service';

@Module({
  imports: [
    NecordModule.forRoot({
      token: process.env.DISCORD_TOKEN,
      intents: [IntentsBitField.Flags.Guilds],
      development: [process.env.DISCORD_DEVELOPMENT_GUILD_ID],
    }),
  ],
  providers: [AppService],
})
export class AppModule {}
```

> info **提示** 你可以在 [这里](https://discord.com/developers/docs/topics/gateway#gateway-intents) 查看所有可用的 intents 列表。

通过上述配置，你可以在 providers 中注入 `AppService`，从而轻松注册命令、事件等功能。

```typescript
@@filename(app.service)
import { Injectable, Logger } from '@nestjs/common';
import { Context, On, Once, ContextOf } from 'necord';
import { Client } from 'discord.js';

@Injectable()
export class AppService {
  private readonly logger = new Logger(AppService.name);

  @Once('ready')
  public onReady(@Context() [client]: ContextOf<'ready'>) {
    this.logger.log(`Bot logged in as ${client.user.username}`);
  }

  @On('warn')
  public onWarn(@Context() [message]: ContextOf<'warn'>) {
    this.logger.warn(message);
  }
}
```

##### 理解 context

你可能已经注意到上面示例中的 `@Context` 装饰器。这个装饰器会将事件上下文（context）注入到你的方法中，让你能够访问各种事件相关的数据。由于事件类型有多种，context 的类型通过 `ContextOf<type: string>` 泛型自动推断。你只需在参数上使用 `@Context()` 装饰器，就可以获取到与事件相关的参数数组。

#### 文本指令

> warning **注意** 文本指令依赖于消息内容（message content），而该功能即将对经过验证的机器人以及在超过 100 个服务器上的应用弃用。如果你的机器人无法访问消息内容，文本指令将无法正常工作。详细信息请参阅[这里](https://support-dev.discord.com/hc/en-us/articles/4404772028055-Message-Content-Access-Deprecation-for-Verified-Bots)。

以下展示了如何使用 `@TextCommand` 装饰器为消息创建一个简单的指令处理器：

```typescript
@@filename(app.commands)
import { Injectable } from '@nestjs/common';
import { Context, TextCommand, TextCommandContext, Arguments } from 'necord';

@Injectable()
export class AppCommands {
  @TextCommand({
    name: 'ping',
    description: 'Responds with pong!',
  })
  public onPing(
    @Context() [message]: TextCommandContext,
    @Arguments() args: string[],
  ) {
    return message.reply('pong!');
  }
}
```

#### 应用指令

应用指令（Application commands）为用户在 Discord 客户端内与应用交互提供了原生方式。应用指令分为三种类型，可通过不同的界面访问：聊天输入（chat input）、消息上下文菜单（通过右键点击消息访问）以及用户上下文菜单（通过右键点击用户访问）。

<figure>
  <img class="illustrative-image" src="https://i.imgur.com/4EmG8G8.png" />
</figure>

#### 斜杠命令（Slash commands）

斜杠命令是一种极佳的方式，可以以结构化的形式与用户进行互动。通过斜杠命令，你可以创建带有精确参数和选项的命令，从而显著提升用户体验。

要在 Necord 中定义斜杠命令，可以使用 `SlashCommand` 装饰器。

```typescript
@@filename(app.commands)
import { Injectable } from '@nestjs/common';
import { Context, SlashCommand, SlashCommandContext } from 'necord';

@Injectable()
export class AppCommands {
  @SlashCommand({
    name: 'ping',
    description: 'Responds with pong!',
  })
  public async onPing(@Context() [interaction]: SlashCommandContext) {
    return interaction.reply({ content: 'Pong!' });
  }
}
```

> info **提示** 当你的机器人客户端登录时，会自动注册所有已定义的命令。请注意，全局命令最多会被缓存 1 小时。为避免全局缓存带来的问题，建议在 Necord 模块中使用 `development` 参数，将命令仅限于单个 guild（服务器）可见。

##### 选项（Options）

你可以通过选项装饰器（Option Decorator）为斜杠命令定义参数。下面我们创建一个 `TextDto` 类来实现：

```typescript
@@filename(text.dto)
import { StringOption } from 'necord';

export class TextDto {
  @StringOption({
    name: 'text',
    description: '在此输入你的文本',
    required: true,
  })
  text: string;
}
```

然后你可以在 `AppCommands` 类中使用这个 DTO：

```typescript
@@filename(app.commands)
import { Injectable } from '@nestjs/common';
import { Context, SlashCommand, Options, SlashCommandContext } from 'necord';
import { TextDto } from './length.dto';

@Injectable()
export class AppCommands {
  @SlashCommand({
    name: 'length',
    description: '计算你的文本长度',
  })
  public async onLength(
    @Context() [interaction]: SlashCommandContext,
    @Options() { text }: TextDto,
  ) {
    return interaction.reply({
      content: `你的文本长度为：${text.length}`,
    });
  }
}
```

如需查看所有内置选项装饰器，请参考 [官方文档](https://necord.org/interactions/slash-commands#options)。

##### 自动补全（Autocomplete）

要为斜杠命令实现自动补全功能，需要创建一个拦截器，用于在用户输入自动补全字段时处理请求。

```typescript
@@filename(cats-autocomplete.interceptor)
import { Injectable } from '@nestjs/common';
import { AutocompleteInteraction } from 'discord.js';
import { AutocompleteInterceptor } from 'necord';

@Injectable()
class CatsAutocompleteInterceptor extends AutocompleteInterceptor {
  public transformOptions(interaction: AutocompleteInteraction) {
    const focused = interaction.options.getFocused(true);
    let choices: string[];

    if (focused.name === 'cat') {
      choices = ['Siamese', 'Persian', 'Maine Coon'];
    }

    return interaction.respond(
      choices
        .filter((choice) => choice.startsWith(focused.value.toString()))
        .map((choice) => ({ name: choice, value: choice })),
    );
  }
}
```

你还需要在选项类上标记 `autocomplete: true`：

```typescript
@@filename(cat.dto)
import { StringOption } from 'necord';

export class CatDto {
  @StringOption({
    name: 'cat',
    description: '选择一个猫的品种',
    autocomplete: true,
    required: true,
  })
  cat: string;
}
```

最后，将拦截器应用到你的斜杠命令上：

```typescript
@@filename(cats.commands)
import { Injectable, UseInterceptors } from '@nestjs/common';
import { Context, SlashCommand, Options, SlashCommandContext } from 'necord';
import { CatDto } from '/cat.dto';
import { CatsAutocompleteInterceptor } from './cats-autocomplete.interceptor';

@Injectable()
export class CatsCommands {
  @UseInterceptors(CatsAutocompleteInterceptor)
  @SlashCommand({
    name: 'cat',
    description: '获取指定猫品种的信息',
  })
  public async onSearch(
    @Context() [interaction]: SlashCommandContext,
    @Options() { cat }: CatDto,
  ) {
    return interaction.reply({
      content: `我找到了 ${cat} 猫品种的信息！`,
    });
  }
}
```

#### 用户上下文菜单

用户命令会出现在右键点击（或轻触）用户时弹出的上下文菜单中。这些命令为用户提供了针对特定用户的快捷操作。

```typescript
@@filename(app.commands)
import { Injectable } from '@nestjs/common';
import { Context, UserCommand, UserCommandContext, TargetUser } from 'necord';
import { User } from 'discord.js';

@Injectable()
export class AppCommands {
  @UserCommand({ name: 'Get avatar' })
  public async getUserAvatar(
    @Context() [interaction]: UserCommandContext,
    @TargetUser() user: User,
  ) {
    return interaction.reply({
      embeds: [
        new MessageEmbed()
          .setTitle(`Avatar of ${user.username}`)
          .setImage(user.displayAvatarURL({ size: 4096, dynamic: true })),
      ],
    });
  }
}
```

#### 消息上下文菜单

消息命令会在右键点击消息时显示在上下文菜单中，方便你对该消息进行快捷操作。

```typescript
@@filename(app.commands)
import { Injectable } from '@nestjs/common';
import { Context, MessageCommand, MessageCommandContext, TargetMessage } from 'necord';
import { Message } from 'discord.js';

@Injectable()
export class AppCommands {
  @MessageCommand({ name: 'Copy Message' })
  public async copyMessage(
    @Context() [interaction]: MessageCommandContext,
    @TargetMessage() message: Message,
  ) {
    return interaction.reply({ content: message.content });
  }
}
```

#### 按钮（Buttons）

[按钮](https://discord.com/developers/docs/interactions/message-components#buttons) 是可以嵌入消息中的交互式元素。用户点击按钮后，会向你的应用发送一个 [交互对象（interaction）](https://discord.com/developers/docs/interactions/receiving-and-responding#interaction-object)。

```typescript
@@filename(app.components)
import { Injectable } from '@nestjs/common';
import { Context, Button, ButtonContext } from 'necord';

@Injectable()
export class AppComponents {
  @Button('BUTTON')
  public onButtonClick(@Context() [interaction]: ButtonContext) {
    return interaction.reply({ content: 'Button clicked!' });
  }
}
```

#### 选择菜单（Select menus）

[选择菜单](https://discord.com/developers/docs/interactions/message-components#select-menus) 是另一种消息中的交互组件，用户可以通过下拉列表选择一个或多个选项。

```typescript
@@filename(app.components)
import { Injectable } from '@nestjs/common';
import { Context, StringSelect, StringSelectContext, SelectedStrings } from 'necord';

@Injectable()
export class AppComponents {
  @StringSelect('SELECT_MENU')
  public onSelectMenu(
    @Context() [interaction]: StringSelectContext,
    @SelectedStrings() values: string[],
  ) {
    return interaction.reply({ content: `你选择了：${values.join(', ')}` });
  }
}
```

如需查看所有内置选择菜单组件，请访问 [此链接](https://necord.org/interactions/message-components#select-menu)。

#### 模态框（Modals）

模态框是一种弹出式表单，允许用户提交格式化输入。以下是如何使用 Necord 创建和处理模态框的示例：

```typescript
@@filename(app.modals)
import { Injectable } from '@nestjs/common';
import { Context, Modal, ModalContext } from 'necord';

@Injectable()
export class AppModals {
  @Modal('pizza')
  public onModal(@Context() [interaction]: ModalContext) {
    return interaction.reply({
      content: `你最喜欢的披萨：${interaction.fields.getTextInputValue('pizza')}`
    });
  }
}
```

#### 更多信息

如需了解更多内容，请访问 [Necord 官方网站](https://necord.org)。
