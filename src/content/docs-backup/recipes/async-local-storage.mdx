### 异步本地存储（Async Local Storage）

`AsyncLocalStorage` 是一个 [Node.js API](https://nodejs.org/api/async_context.html#async_context_class_asynclocalstorage)（基于 `async_hooks` API），它提供了一种在应用程序中传播本地状态的替代方式，无需显式地将其作为函数参数传递。它类似于其他语言中的线程本地存储。

Async Local Storage（异步本地存储）的核心思想是，我们可以用 `AsyncLocalStorage#run` 方法 _包裹_ 某个函数调用。所有在该包裹调用内被执行的代码，都能访问到同一个 `store`，而这个 `store` 对每个调用链来说都是唯一的。

在 NestJS 的上下文中，这意味着如果我们能在请求的生命周期中找到一个合适的位置，用来包裹后续的请求处理代码，就可以访问和修改仅对该请求可见的状态。这为请求作用域提供者及其部分局限性提供了一种替代方案。

另外，我们也可以仅在系统的某一部分（例如 _事务_ 对象）中使用 ALS 传播上下文，无需在服务之间显式传递，从而提升隔离性和封装性。

#### 自定义实现

NestJS 本身没有为 `AsyncLocalStorage` 提供内置抽象，因此我们以最简单的 HTTP 场景为例，演示如何自行实现，以便更好地理解这一概念：

> info **提示** 如需现成的 [专用包](recipes/async-local-storage#nestjs-cls)，请继续阅读下文。

1. 首先，在某个共享源文件中创建一个新的 `AsyncLocalStorage` 实例。由于我们使用的是 NestJS，建议将其封装为一个模块，并通过自定义提供者（Custom Provider）注册。

```ts
@@filename(als.module)
@Module({
  providers: [
    {
      provide: AsyncLocalStorage,
      useValue: new AsyncLocalStorage(),
    },
  ],
  exports: [AsyncLocalStorage],
})
export class AlsModule {}
```

> info **提示** `AsyncLocalStorage` 需从 `async_hooks` 导入。

2. 由于我们只关注 HTTP 场景，可以通过中间件将 `next` 函数用 `AsyncLocalStorage#run` 包裹。中间件是请求进入的第一站，这样 `store` 就能在所有增强器和后续系统中被访问。

```ts
@@filename(app.module)
@Module({
  imports: [AlsModule],
  providers: [CatsService],
  controllers: [CatsController],
})
export class AppModule implements NestModule {
  constructor(
    // 在模块构造函数中注入 AsyncLocalStorage 实例
    private readonly als: AsyncLocalStorage
  ) {}

  configure(consumer: MiddlewareConsumer) {
    // 绑定中间件
    consumer
      .apply((req, res, next) => {
        // 根据请求初始化 store 的默认值
        const store = {
          userId: req.headers['x-user-id'],
        };
        // 用 als.run 方法和 store 包裹 "next" 回调
        this.als.run(store, () => next());
      })
      .forRoutes('*path');
  }
}
@@switch
@Module({
  imports: [AlsModule],
  providers: [CatsService],
  controllers: [CatsController],
})
@Dependencies(AsyncLocalStorage)
export class AppModule {
  constructor(als) {
    // 在模块构造函数中注入 AsyncLocalStorage 实例
    this.als = als
  }

  configure(consumer) {
    // 绑定中间件
    consumer
      .apply((req, res, next) => {
        // 根据请求初始化 store 的默认值
        const store = {
          userId: req.headers['x-user-id'],
        };
        // 用 als.run 方法和 store 包裹 "next" 回调
        this.als.run(store, () => next());
      })
      .forRoutes('*path');
  }
}
```

3. 现在，在请求的生命周期内的任意位置，我们都可以访问本地 store 实例。

```ts
@@filename(cats.service)
@Injectable()
export class CatsService {
  constructor(
    // 可以注入 ALS 实例
    private readonly als: AsyncLocalStorage,
    private readonly catsRepository: CatsRepository,
  ) {}

  getCatForUser() {
    // getStore 方法始终返回与当前请求关联的 store 实例
    const userId = this.als.getStore()["userId"] as number;
    return this.catsRepository.getForUser(userId);
  }
}
@@switch
@Injectable()
@Dependencies(AsyncLocalStorage, CatsRepository)
export class CatsService {
  constructor(als, catsRepository) {
    // 可以注入 ALS 实例
    this.als = als
    this.catsRepository = catsRepository
  }

  getCatForUser() {
    // getStore 方法始终返回与当前请求关联的 store 实例
    const userId = this.als.getStore()["userId"] as number;
    return this.catsRepository.getForUser(userId);
  }
}
```

4. 就这样，我们无需注入整个 `REQUEST` 对象，也能在请求间共享相关状态。

> warning **警告** 请注意，虽然该技术适用于许多场景，但它本质上会让代码流程变得隐式（即创建了隐式上下文），因此请谨慎使用，尤其要避免创建上下文“[上帝对象](https://en.wikipedia.org/wiki/God_object)”。

### NestJS CLS

[nestjs-cls](https://github.com/Papooch/nestjs-cls) 包为直接使用 `AsyncLocalStorage`（CLS 是 continuation-local storage 的缩写）带来了多项开发体验（DX）提升。它将实现细节封装为 `ClsModule`，为不同传输层（不仅限于 HTTP）提供多种初始化 `store` 的方式，并且支持强类型。

随后，可以通过可注入的 `ClsService` 访问 store，或者通过 [代理提供者（Proxy Providers）](https://www.npmjs.com/package/nestjs-cls#proxy-providers) 完全将其从业务逻辑中抽象出来。

> info **信息** `nestjs-cls` 是第三方包，并非由 NestJS 核心团队维护。如遇到问题，请在[对应仓库](https://github.com/Papooch/nestjs-cls/issues)提交 issue。

#### 安装

除了依赖 `@nestjs` 相关库外，它仅使用 Node.js 内置 API。像安装其他包一样安装即可。

```bash
npm i nestjs-cls
```

#### 使用方法

可以用 `nestjs-cls` 实现与[上文](recipes/async-local-storage#custom-implementation)类似的功能，示例如下：

1. 在根模块中导入 `ClsModule`。

```ts
@@filename(app.module)
@Module({
  imports: [
    // 注册 ClsModule
    ClsModule.forRoot({
      middleware: {
        // 自动为所有路由挂载
        // ClsMiddleware
        mount: true,
        // 并通过 setup 方法
        // 提供默认 store 值
        setup: (cls, req) => {
          cls.set('userId', req.headers['x-user-id']);
        },
      },
    }),
  ],
  providers: [CatsService],
  controllers: [CatsController],
})
export class AppModule {}
```

2. 然后可以通过 `ClsService` 访问 store 中的值。

```ts
@@filename(cats.service)
@Injectable()
export class CatsService {
  constructor(
    // 注入 ClsService 实例
    private readonly cls: ClsService,
    private readonly catsRepository: CatsRepository,
  ) {}

  getCatForUser() {
    // 使用 "get" 方法获取存储的值
    const userId = this.cls.get('userId');
    return this.catsRepository.getForUser(userId);
  }
}
@@switch
@Injectable()
@Dependencies(AsyncLocalStorage, CatsRepository)
export class CatsService {
  constructor(cls, catsRepository) {
    // 注入 ClsService 实例
    this.cls = cls
    this.catsRepository = catsRepository
  }

  getCatForUser() {
    // 使用 "get" 方法获取存储的值
    const userId = this.cls.get('userId');
    return this.catsRepository.getForUser(userId);
  }
}
```

3. 若希望对 `ClsService` 管理的 store 值进行强类型约束（并获得字符串键的自动补全），可以在注入时为 `ClsService<MyClsStore>` 提供可选类型参数。

```ts
export interface MyClsStore extends ClsStore {
  userId: number
}
```

> info **提示** 该包还可以自动生成请求 ID，并通过 `cls.getId()` 获取，或通过 `cls.get(CLS_REQ)` 获取完整的请求对象。

#### 测试

由于 `ClsService` 只是另一个可注入的提供者（Provider），因此在单元测试中可以完全 mock 掉。

但在某些集成测试中，可能仍希望使用真实的 `ClsService` 实现。这种情况下，需要用 `ClsService#run` 或 `ClsService#runWith` 包裹上下文相关的代码。

```ts
describe('CatsService', () => {
  let service: CatsService
  let cls: ClsService
  const mockCatsRepository = createMock<CatsRepository>()

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      // 按常规方式设置测试模块
      providers: [
        CatsService,
        {
          provide: CatsRepository
          useValue: mockCatsRepository
        }
      ],
      imports: [
        // 导入 ClsModule 的静态版本，仅提供
        // ClsService，不会设置 store
        ClsModule
      ],
    }).compile()

    service = module.get(CatsService)

    // 获取 ClsService 以便后续使用
    cls = module.get(ClsService)
  })

  describe('getCatForUser', () => {
    it('根据用户 id 获取猫', async () => {
      const expectedUserId = 42
      mocksCatsRepository.getForUser.mockImplementationOnce(
        (id) => ({ userId: id })
      )

      // 用 `runWith` 方法包裹测试调用
      // 并传入自定义 store 值
      const cat = await cls.runWith(
        { userId: expectedUserId },
        () => service.getCatForUser()
      )

      expect(cat.userId).toEqual(expectedUserId)
    })
  })
})
```

#### 更多信息

访问 [NestJS CLS GitHub 页面](https://github.com/Papooch/nestjs-cls) 获取完整 API 文档和更多代码示例。
