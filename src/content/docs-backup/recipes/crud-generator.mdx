### CRUD 生成器（仅限 TypeScript）

在项目的整个生命周期中，每当我们构建新功能时，通常需要为应用程序添加新的资源。这些资源往往需要我们每次定义新资源时都要重复执行多项操作。

#### 简介

让我们设想一个真实场景：我们需要为两个实体（如 **User** 和 **Product** 实体）暴露 CRUD（增删改查）接口。
按照最佳实践，对于每个实体，我们都需要执行以下几步操作：

- 生成一个模块（`nest g mo`），用于组织代码并建立清晰的边界（将相关组件分组）
- 生成一个控制器（`nest g co`），用于定义 CRUD 路由（对于 GraphQL 应用则是查询/变更）
- 生成一个服务（`nest g s`），用于实现和隔离业务逻辑
- 生成一个实体类/接口，用于描述资源数据结构
- 生成数据传输对象（或 GraphQL 应用的输入类型），用于定义数据在网络中的传输方式

这确实是很多步骤！

为了加快这一重复流程，[Nest 命令行工具（Nest CLI）](/cli/overview) 提供了一个生成器（原型生成器，schematic），可以自动生成所有模板代码，帮助我们避免手动操作，大大简化开发体验。

> info **注意** 该原型生成器支持生成 **HTTP** 控制器、**微服务** 控制器、**GraphQL** 解析器（支持 code first 和 schema first 两种模式）以及 **WebSocket** 网关。

#### 生成新资源

要创建一个新资源，只需在项目根目录下运行以下命令：

```shell
$ nest g resource
```

`nest g resource` 命令不仅会生成所有 NestJS 构建模块（模块、服务、控制器类），还会生成实体类、DTO 类以及测试（`.spec`）文件。

下面是生成的控制器文件示例（针对 RESTful 接口）：

```typescript
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto)
  }

  @Get()
  findAll() {
    return this.usersService.findAll()
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.usersService.findOne(+id)
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.usersService.update(+id, updateUserDto)
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.usersService.remove(+id)
  }
}
```

此外，生成器会自动为所有 CRUD 接口（RESTful API 的路由、GraphQL 的查询和变更、微服务和 WebSocket 网关的消息订阅）创建占位符，无需手动编写。

> warning **注意** 生成的服务类默认**不依赖于任何特定 ORM（或数据源）**，这使得生成器足够通用，能够满足各种项目需求。所有方法默认仅包含占位实现，便于你根据项目实际数据源进行填充。

同样，如果你希望为 GraphQL 应用生成解析器，只需选择 `GraphQL（code first）`（或 `GraphQL（schema first）`）作为传输层（Transport Layer）。

此时，NestJS 会为你生成解析器类，而不是 RESTful API 控制器：

```shell
$ nest g resource users

> ? 你使用哪种传输层？GraphQL（code first）
> ? 是否需要生成 CRUD 入口？是
> CREATE src/users/users.module.ts (224 bytes)
> CREATE src/users/users.resolver.spec.ts (525 bytes)
> CREATE src/users/users.resolver.ts (1109 bytes)
> CREATE src/users/users.service.spec.ts (453 bytes)
> CREATE src/users/users.service.ts (625 bytes)
> CREATE src/users/dto/create-user.input.ts (195 bytes)
> CREATE src/users/dto/update-user.input.ts (281 bytes)
> CREATE src/users/entities/user.entity.ts (187 bytes)
> UPDATE src/app.module.ts (312 bytes)
```

> info **提示** 如果不想生成测试文件，可以添加 `--no-spec` 参数，例如：`nest g resource users --no-spec`

可以看到，不仅所有模板代码（包括变更和查询）都已生成，而且各部分已自动关联。我们在使用 `UsersService`、`User` 实体和 DTO。

```typescript
import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql'
import { UsersService } from './users.service'
import { User } from './entities/user.entity'
import { CreateUserInput } from './dto/create-user.input'
import { UpdateUserInput } from './dto/update-user.input'

@Resolver(() => User)
export class UsersResolver {
  constructor(private readonly usersService: UsersService) {}

  @Mutation(() => User)
  createUser(@Args('createUserInput') createUserInput: CreateUserInput) {
    return this.usersService.create(createUserInput)
  }

  @Query(() => [User], { name: 'users' })
  findAll() {
    return this.usersService.findAll()
  }

  @Query(() => User, { name: 'user' })
  findOne(@Args('id', { type: () => Int }) id: number) {
    return this.usersService.findOne(id)
  }

  @Mutation(() => User)
  updateUser(@Args('updateUserInput') updateUserInput: UpdateUserInput) {
    return this.usersService.update(updateUserInput.id, updateUserInput)
  }

  @Mutation(() => User)
  removeUser(@Args('id', { type: () => Int }) id: number) {
    return this.usersService.remove(id)
  }
}
```
