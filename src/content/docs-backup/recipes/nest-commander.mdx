### Nest 命令行工具

在 [独立应用](/standalone-applications) 文档的基础上，`nest-commander` 包为你提供了一种类似于常规 Nest 应用结构的命令行应用开发方式。详细介绍可参考 [nest-commander 官方文档](https://jmcdo29.github.io/nest-commander)。

> info **信息** `nest-commander` 是第三方包，并非由 NestJS 核心团队全权维护。如遇到相关问题，请前往 [对应仓库](https://github.com/jmcdo29/nest-commander/issues/new/choose) 反馈。

#### 安装

和其他包一样，使用前需要先安装。

```bash
$ npm i nest-commander
```

#### 命令文件

`nest-commander` 让你可以通过 [装饰器（Decorator）](https://www.typescriptlang.org/docs/handbook/decorators.html) 轻松编写命令行应用。你可以为类添加 `@Command()` 装饰器，为类的方法添加 `@Option()` 装饰器。每个命令文件都应实现 `CommandRunner` 抽象类，并使用 `@Command()` 装饰器进行修饰。

每个命令都会被 Nest 视为 `@Injectable()`，因此你可以像平常一样使用依赖注入。需要注意的是，每个命令都应实现 `CommandRunner` 抽象类。该抽象类确保所有命令都拥有一个 `run` 方法，该方法返回 `Promise<void>`，并接收 `string[], Record<string, any>` 两个参数。`run` 方法是你编写主要逻辑的地方，未被选项标志匹配的参数会以数组形式传入，方便你处理多个参数。至于选项参数 `Record<string, any>`，其属性名与 `@Option()` 装饰器中定义的 `name` 属性一致，属性值则为选项处理器的返回值。如果你希望有更好的类型安全性，也可以为选项自定义接口。

#### 运行命令

类似于在 NestJS 应用中使用 `NestFactory` 创建服务器并通过 `listen` 启动，`nest-commander` 包也提供了简单易用的 API 来运行你的命令行应用。只需导入 `CommandFactory`，并使用其静态方法 `run`，传入应用的根模块即可。示例如下：

```ts
import { CommandFactory } from 'nest-commander'
import { AppModule } from './app.module'

async function bootstrap() {
  await CommandFactory.run(AppModule)
}

bootstrap()
```

默认情况下，使用 `CommandFactory` 时 Nest 的日志记录器是禁用的。不过你可以通过 `run` 方法的第二个参数传入自定义的 NestJS 日志记录器，或者传入你希望保留的日志级别数组。例如，如果你只想输出错误日志，可以传入 `['error']`。

```ts
import { CommandFactory } from 'nest-commander'
import { AppModule } from './app.module'
import { LogService } from './log.service'

async function bootstrap() {
  await CommandFactory.run(AppModule, new LogService())

  // 或者，只输出警告和错误日志
  await CommandFactory.run(AppModule, ['warn', 'error'])
}

bootstrap()
```

就是这样。底层实现中，`CommandFactory` 会自动帮你调用 `NestFactory` 并在需要时调用 `app.close()`，因此你无需担心内存泄漏问题。如果你需要添加错误处理，可以用 `try/catch` 包裹 `run` 方法，或者在 `bootstrap()` 调用后链式调用 `.catch()`。

#### 测试

写出强大的命令行脚本后，当然也要方便地进行测试。幸运的是，`nest-commander` 提供了与 NestJS 生态完美契合的测试工具，任何 NestJS 用户都会觉得很熟悉。在测试模式下，不需要用 `CommandFactory` 构建命令，而是可以使用 `CommandTestFactory` 并传入元数据，这与 `@nestjs/testing` 包中的 `Test.createTestingModule` 用法非常类似。实际上，底层就是用的这个包。你同样可以在调用 `compile()` 前链式调用 `overrideProvider` 方法，从而在测试中替换依赖注入（DI）组件。

#### 综合示例

下面的类相当于实现了一个 CLI 命令，该命令可以接收 `basic` 作为子命令，或者直接调用，并且支持 `-n`、`-s` 和 `-b`（以及它们的长参数名），每个选项都可以自定义解析器。同时也支持 `--help` 参数，这也是 commander 的常规用法。

```ts
import { Command, CommandRunner, Option } from 'nest-commander'
import { LogService } from './log.service'

interface BasicCommandOptions {
  string?: string
  boolean?: boolean
  number?: number
}

@Command({ name: 'basic', description: '参数解析示例' })
export class BasicCommand extends CommandRunner {
  constructor(private readonly logService: LogService) {
    super()
  }

  async run(passedParam: string[], options?: BasicCommandOptions): Promise<void> {
    if (options?.boolean !== undefined && options?.boolean !== null) {
      this.runWithBoolean(passedParam, options.boolean)
    } else if (options?.number) {
      this.runWithNumber(passedParam, options.number)
    } else if (options?.string) {
      this.runWithString(passedParam, options.string)
    } else {
      this.runWithNone(passedParam)
    }
  }

  @Option({
    flags: '-n, --number [number]',
    description: '基础数字解析器',
  })
  parseNumber(val: string): number {
    return Number(val)
  }

  @Option({
    flags: '-s, --string [string]',
    description: '返回字符串',
  })
  parseString(val: string): string {
    return val
  }

  @Option({
    flags: '-b, --boolean [boolean]',
    description: '布尔值解析器',
  })
  parseBoolean(val: string): boolean {
    return JSON.parse(val)
  }

  runWithString(param: string[], option: string): void {
    this.logService.log({ param, string: option })
  }

  runWithNumber(param: string[], option: number): void {
    this.logService.log({ param, number: option })
  }

  runWithBoolean(param: string[], option: boolean): void {
    this.logService.log({ param, boolean: option })
  }

  runWithNone(param: string[]): void {
    this.logService.log({ param })
  }
}
```

确保将命令类添加到模块中：

```ts
@Module({
  providers: [LogService, BasicCommand],
})
export class AppModule {}
```

现在，你可以在 main.ts 中这样运行 CLI：

```ts
async function bootstrap() {
  await CommandFactory.run(AppModule)
}

bootstrap()
```

如此一来，你就拥有了一个命令行应用程序。

#### 更多信息

如需获取更多信息、示例和 API 文档，请访问 [nest-commander 官方文档站点](https://jmcdo29.github.io/nest-commander)。
