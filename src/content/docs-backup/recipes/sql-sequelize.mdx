### SQL（Sequelize）

##### 本章节仅适用于 TypeScript

> **警告** 本文将介绍如何基于 **Sequelize** 包，从零开始使用自定义组件创建一个 `DatabaseModule`。需要注意的是，这种做法会带来较多的额外工作量。你可以通过使用专门的、开箱即用的 `@nestjs/sequelize` 包来避免这些繁琐步骤。想了解更多信息，请参见[这里](/techniques/database#sequelize-integration)。

[Sequelize](https://github.com/sequelize/sequelize) 是一个流行的对象关系映射器（Object Relational Mapper，ORM），采用原生 JavaScript 编写。不过，社区还提供了一个 [sequelize-typescript](https://github.com/RobinBuschmann/sequelize-typescript) 的 TypeScript 封装，它为基础的 Sequelize 提供了一系列装饰器（Decorator）和其他扩展功能。

#### 快速开始

要开始使用这个库，我们需要先安装以下依赖：

```bash
$ npm install --save sequelize sequelize-typescript mysql2
$ npm install --save-dev @types/sequelize
```

第一步，我们需要创建一个 **Sequelize** 实例，并在构造函数中传入配置对象。同时，需要将所有模型添加到实例中（也可以使用 `modelPaths` 属性），并通过 `sync()` 方法同步数据库表。

```typescript
@@filename(database.providers)
import { Sequelize } from 'sequelize-typescript';
import { Cat } from '../cats/cat.entity';

export const databaseProviders = [
  {
    provide: 'SEQUELIZE',
    useFactory: async () => {
      const sequelize = new Sequelize({
        dialect: 'mysql',
        host: 'localhost',
        port: 3306,
        username: 'root',
        password: 'password',
        database: 'nest',
      });
      sequelize.addModels([Cat]);
      await sequelize.sync();
      return sequelize;
    },
  },
];
```

> info **提示** 按照最佳实践，我们将自定义提供者声明在单独的文件中，文件名以 `*.providers.ts` 结尾。

接下来，我们需要导出这些提供者，以便应用程序的其他部分可以访问。

```typescript
import { Module } from '@nestjs/common'
import { databaseProviders } from './database.providers'

@Module({
  providers: [...databaseProviders],
  exports: [...databaseProviders],
})
export class DatabaseModule {}
```

现在，我们可以通过 `@Inject()` 装饰器注入 `Sequelize` 对象。所有依赖于 `Sequelize` 异步提供者（Provider）的类都会在 `Promise` 被解析后再进行实例化。

#### 模型注入（Model injection）

在 [Sequelize](https://github.com/sequelize/sequelize) 中，**模型** 定义了数据库中的一张表。该类的实例代表数据库中的一行。首先，我们需要至少定义一个实体：

```typescript
@@filename(cat.entity)
import { Table, Column, Model } from 'sequelize-typescript';

@Table
export class Cat extends Model {
  @Column
  name: string;

  @Column
  age: number;

  @Column
  breed: string;
}
```

`Cat` 实体位于 `cats` 目录下。该目录代表 `CatsModule`。接下来，我们需要创建一个**仓库**提供者：

```typescript
@@filename(cats.providers)
import { Cat } from './cat.entity';

export const catsProviders = [
  {
    provide: 'CATS_REPOSITORY',
    useValue: Cat,
  },
];
```

> warning **警告** 在实际应用中应避免使用魔法字符串。`CATS_REPOSITORY` 和 `SEQUELIZE` 都应当放在单独的 `constants.ts` 文件中进行管理。

在 Sequelize 中，我们使用静态方法来操作数据，因此这里创建了一个**别名（alias）**。

现在，我们可以通过 `@Inject()` 装饰器将 `CATS_REPOSITORY` 注入到 `CatsService`中：

```typescript
@@filename(cats.service)
import { Injectable, Inject } from '@nestjs/common';
import { CreateCatDto } from './dto/create-cat.dto';
import { Cat } from './cat.entity';

@Injectable()
export class CatsService {
  constructor(
    @Inject('CATS_REPOSITORY')
    private catsRepository: typeof Cat
  ) {}

  async findAll(): Promise<Cat[]> {
    return this.catsRepository.findAll<Cat>();
  }
}
```

数据库连接是**异步**的，但 Nest 会让这一过程对最终用户完全透明。`CATS_REPOSITORY` 提供者会等待数据库连接完成，而 `CatsService` 会延迟到仓库准备好后再进行实例化。整个应用会在所有类都实例化完成后启动。

下面是最终的 `CatsModule`：

```typescript
@@filename(cats.module)
import { Module } from '@nestjs/common';
import { CatsController } from './cats.controller';
import { CatsService } from './cats.service';
import { catsProviders } from './cats.providers';
import { DatabaseModule } from '../database/database.module';

@Module({
  imports: [DatabaseModule],
  controllers: [CatsController],
  providers: [
    CatsService,
    ...catsProviders,
  ],
})
export class CatsModule {}
```

> info **提示** 不要忘记在根模块 `AppModule` 中导入 `CatsModule`。
