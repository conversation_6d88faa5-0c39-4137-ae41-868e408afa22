### CQRS（命令查询职责分离）

简单 [CRUD](https://en.wikipedia.org/wiki/Create,_read,_update_and_delete)（创建、读取、更新和删除）应用的流程通常如下：

1. 控制器（Controller）层处理 HTTP 请求，并将任务委托给服务（Service）层。
2. 服务层承载大部分业务逻辑。
3. 服务通过仓储（Repository）/ 数据访问对象（DAO）来变更或持久化实体（Entity）。
4. 实体作为值的容器，提供 setter 和 getter 方法。

虽然这种模式对于中小型应用通常已经足够，但对于更大、更复杂的应用来说，可能并不是最佳选择。在这类场景下，**CQRS（命令查询职责分离，Command and Query Responsibility Segregation）** 模型可能更合适且更具可扩展性（具体取决于应用需求）。该模型的优势包括：

- **关注点分离**。该模型将读操作和写操作分离为不同的模型。
- **可扩展性**。读写操作可以独立扩展。
- **灵活性**。该模型允许为读写操作分别使用不同的数据存储。
- **性能优化**。可以针对读写操作分别采用最优的数据存储方案。

为支持该模型，Nest 提供了一个轻量级的 [CQRS 模块](https://github.com/nestjs/cqrs)。本章节将介绍如何使用该模块。

#### 安装

首先安装所需的依赖包：

```bash
$ npm install --save @nestjs/cqrs
```

安装完成后，进入应用的根模块（通常为 `AppModule`），并导入 `CqrsModule.forRoot()`：

```typescript
import { Module } from '@nestjs/common'
import { CqrsModule } from '@nestjs/cqrs'

@Module({
  imports: [CqrsModule.forRoot()],
})
export class AppModule {}
```

该模块支持可选的配置对象。可用的配置项如下表所示：

| 属性                          | 说明                                                                      | 默认值                            |
| ----------------------------- | ------------------------------------------------------------------------- | --------------------------------- |
| `commandPublisher`            | 负责将命令（Command）分发到系统的发布器。                                 | `DefaultCommandPubSub`            |
| `eventPublisher`              | 用于发布事件（Event）的发布器，支持事件广播或处理。                       | `DefaultPubSub`                   |
| `queryPublisher`              | 用于发布查询（Query）的发布器，可触发数据检索操作。                       | `DefaultQueryPubSub`              |
| `unhandledExceptionPublisher` | 负责处理未捕获异常（Unhandled Exception）的发布器，确保异常被追踪和上报。 | `DefaultUnhandledExceptionPubSub` |
| `eventIdProvider`             | 提供唯一事件 ID 的服务，通过生成或从事件实例中获取。                      | `DefaultEventIdProvider`          |
| `rethrowUnhandled`            | 是否在处理未捕获异常后重新抛出异常，便于调试和错误管理。                  | `false`                           |

#### 命令（Commands）

命令用于更改应用程序状态。命令应以任务为导向，而非以数据为中心。当命令被分发时，会由对应的 **命令处理器（Command Handler）** 进行处理。处理器负责更新应用程序状态。

```typescript
@@filename(heroes-game.service)
@Injectable()
export class HeroesGameService {
  constructor(private commandBus: CommandBus) {}

  async killDragon(heroId: string, killDragonDto: KillDragonDto) {
    return this.commandBus.execute(
      new KillDragonCommand(heroId, killDragonDto.dragonId)
    );
  }
}
@@switch
@Injectable()
@Dependencies(CommandBus)
export class HeroesGameService {
  constructor(commandBus) {
    this.commandBus = commandBus;
  }

  async killDragon(heroId, killDragonDto) {
    return this.commandBus.execute(
      new KillDragonCommand(heroId, killDragonDto.dragonId)
    );
  }
}
```

在上面的代码片段中，我们实例化了 `KillDragonCommand` 类，并将其实例传递给 `CommandBus` 的 `execute()` 方法。下面是演示用的命令类：

```typescript
@@filename(kill-dragon.command)
export class KillDragonCommand extends Command<{
  actionId: string // 此类型表示命令执行的结果
}> {
  constructor(
    public readonly heroId: string,
    public readonly dragonId: string,
  ) {}
}
@@switch
export class KillDragonCommand extends Command {
  constructor(heroId, dragonId) {
    this.heroId = heroId;
    this.dragonId = dragonId;
  }
}
```

如你所见，`KillDragonCommand` 类继承自 `Command` 类。`Command` 是从 `@nestjs/cqrs` 包中导出的一个简单工具类，用于定义命令的返回类型。在本例中，返回类型是一个包含 `actionId` 属性的对象。现在，每当分发 `KillDragonCommand` 命令时，`CommandBus#execute()` 方法的返回类型会被推断为 `Promise<{ actionId: string }>`。当你希望从命令处理器返回一些数据时，这种方式非常有用。

> info **提示** 继承 `Command` 类是可选的。只有在你需要定义命令的返回类型时才有必要。

`CommandBus` 代表命令的**流**。它负责将命令分发给合适的处理器。`execute()` 方法返回一个 Promise，该 Promise 会解析为处理器返回的值。

接下来，我们为 `KillDragonCommand` 命令创建一个处理器。

```typescript
@@filename(kill-dragon.handler)
@CommandHandler(KillDragonCommand)
export class KillDragonHandler implements ICommandHandler<KillDragonCommand> {
  constructor(private repository: HeroesRepository) {}

  async execute(command: KillDragonCommand) {
    const { heroId, dragonId } = command;
    const hero = this.repository.findOneById(+heroId);

    hero.killEnemy(dragonId);
    await this.repository.persist(hero);

    // "ICommandHandler<KillDragonCommand>" 会强制你返回与命令返回类型一致的值
    return {
      actionId: crypto.randomUUID(), // 该值会返回给调用方
    }
  }
}
@@switch
@CommandHandler(KillDragonCommand)
@Dependencies(HeroesRepository)
export class KillDragonHandler {
  constructor(repository) {
    this.repository = repository;
  }

  async execute(command) {
    const { heroId, dragonId } = command;
    const hero = this.repository.findOneById(+heroId);

    hero.killEnemy(dragonId);
    await this.repository.persist(hero);

    // "ICommandHandler<KillDragonCommand>" 会强制你返回与命令返回类型一致的值
    return {
      actionId: crypto.randomUUID(), // 该值会返回给调用方
    }
  }
}
```

该处理器会从仓库中获取 `Hero` 实体，调用其 `killEnemy()` 方法，并持久化更改。`KillDragonHandler` 类实现了 `ICommandHandler` 接口，该接口要求实现 `execute()` 方法。`execute()` 方法会接收命令对象作为参数。

请注意，`ICommandHandler<KillDragonCommand>` 会强制你返回与命令返回类型一致的值。在本例中，返回类型是一个包含 `actionId` 属性的对象。只有当命令继承自 `Command` 类时才会有此要求，否则你可以返回任意内容。

最后，确保在模块中将 `KillDragonHandler` 注册为提供者：

```typescript
providers: [KillDragonHandler]
```

#### 查询（Query）

查询用于从应用程序状态中检索数据。查询应以数据为中心，而非以任务为导向。当分发（dispatch）一个查询时，会由对应的 **查询处理器（Query Handler）** 进行处理。处理器负责检索数据。

`QueryBus`（查询总线）遵循与 `CommandBus`（命令总线）相同的模式。查询处理器应实现 `IQueryHandler`（查询处理器接口）接口，并使用 `@QueryHandler()` 装饰器进行注解。请参考以下示例：

```typescript
export class GetHeroQuery extends Query<Hero> {
  constructor(public readonly heroId: string) {}
}
```

与 `Command` 类类似，`Query` 类是从 `@nestjs/cqrs` 包中导出的一个简单工具类，用于定义查询的返回类型。在本例中，返回类型为 `Hero` 对象。现在，每当分发 `GetHeroQuery` 查询时，`QueryBus#execute()` 方法的返回类型会被自动推断为 `Promise<Hero>`。

要检索 hero 数据，我们需要创建一个查询处理器：

```typescript
@@filename(get-hero.handler)
@QueryHandler(GetHeroQuery)
export class GetHeroHandler implements IQueryHandler<GetHeroQuery> {
  constructor(private repository: HeroesRepository) {}

  async execute(query: GetHeroQuery) {
    return this.repository.findOneById(query.hero);
  }
}
@@switch
@QueryHandler(GetHeroQuery)
@Dependencies(HeroesRepository)
export class GetHeroHandler {
  constructor(repository) {
    this.repository = repository;
  }

  async execute(query) {
    return this.repository.findOneById(query.hero);
  }
}
```

`GetHeroHandler` 类实现了 `IQueryHandler` 接口，该接口要求实现 `execute()` 方法。`execute()` 方法接收查询对象作为参数，并且必须返回与查询返回类型匹配的数据（在本例中为 `Hero` 对象）。

最后，请确保在模块中将 `GetHeroHandler` 注册为提供者：

```typescript
providers: [GetHeroHandler]
```

现在，可以通过 `QueryBus` 分发查询：

```typescript
const hero = await this.queryBus.execute(new GetHeroQuery(heroId)) // "hero" 的类型会被自动推断为 "Hero"
```

#### 事件（Events）

事件用于在应用程序状态发生变化时，通知应用程序的其他部分。事件通常由**模型**或直接通过 `EventBus`（事件总线）分发。当事件被分发时，会由相应的**事件处理器（Event Handler）**进行处理。事件处理器可以用于更新读取模型等操作。

下面我们以一个示例来创建一个事件类：

```typescript
@@filename(hero-killed-dragon.event)
export class HeroKilledDragonEvent {
  constructor(
    public readonly heroId: string,
    public readonly dragonId: string,
  ) {}
}
@@switch
export class HeroKilledDragonEvent {
  constructor(heroId, dragonId) {
    this.heroId = heroId;
    this.dragonId = dragonId;
  }
}
```

虽然我们可以直接通过 `EventBus.publish()` 方法分发事件，但同样也可以在模型中分发事件。下面我们将更新 `Hero` 模型，在调用 `killEnemy()` 方法时分发 `HeroKilledDragonEvent` 事件。

```typescript
@@filename(hero.model)
export class Hero extends AggregateRoot {
  constructor(private id: string) {
    super();
  }

  killEnemy(enemyId: string) {
    // 业务逻辑
    this.apply(new HeroKilledDragonEvent(this.id, enemyId));
  }
}
@@switch
export class Hero extends AggregateRoot {
  constructor(id) {
    super();
    this.id = id;
  }

  killEnemy(enemyId) {
    // 业务逻辑
    this.apply(new HeroKilledDragonEvent(this.id, enemyId));
  }
}
```

`apply()` 方法用于分发事件。它接收一个事件对象作为参数。然而，由于我们的模型并不了解 `EventBus`，因此需要将事件发布器与模型关联起来。我们可以通过 `EventPublisher` 类来实现这一点。

```typescript
@@filename(kill-dragon.handler)
@CommandHandler(KillDragonCommand)
export class KillDragonHandler implements ICommandHandler<KillDragonCommand> {
  constructor(
    private repository: HeroesRepository,
    private publisher: EventPublisher,
  ) {}

  async execute(command: KillDragonCommand) {
    const { heroId, dragonId } = command;
    const hero = this.publisher.mergeObjectContext(
      await this.repository.findOneById(+heroId),
    );
    hero.killEnemy(dragonId);
    hero.commit();
  }
}
@@switch
@CommandHandler(KillDragonCommand)
@Dependencies(HeroesRepository, EventPublisher)
export class KillDragonHandler {
  constructor(repository, publisher) {
    this.repository = repository;
    this.publisher = publisher;
  }

  async execute(command) {
    const { heroId, dragonId } = command;
    const hero = this.publisher.mergeObjectContext(
      await this.repository.findOneById(+heroId),
    );
    hero.killEnemy(dragonId);
    hero.commit();
  }
}
```

`EventPublisher#mergeObjectContext` 方法会将事件发布器合并到指定对象中，这意味着该对象现在可以将事件发布到事件流中。

请注意，在本例中我们还调用了模型的 `commit()` 方法。该方法用于分发所有待处理的事件。若希望自动分发事件，可以将 `autoCommit` 属性设置为 `true`：

```typescript
export class Hero extends AggregateRoot {
  constructor(private id: string) {
    super()
    this.autoCommit = true
  }
}
```

如果我们希望将事件发布器合并到一个尚未实例化的对象（而是一个类）中，可以使用 `EventPublisher#mergeClassContext` 方法：

```typescript
const HeroModel = this.publisher.mergeClassContext(Hero)
const hero = new HeroModel('id') // <-- HeroModel 是一个类
```

现在，`HeroModel` 类的每个实例都可以发布事件，而无需再使用 `mergeObjectContext()` 方法。

此外，我们还可以通过 `EventBus` 手动发布事件：

```typescript
this.eventBus.publish(new HeroKilledDragonEvent())
```

> info **提示** `EventBus` 是一个可注入的类。

每个事件都可以拥有多个**事件处理器**。

```typescript
@@filename(hero-killed-dragon.handler)
@EventsHandler(HeroKilledDragonEvent)
export class HeroKilledDragonHandler implements IEventHandler<HeroKilledDragonEvent> {
  constructor(private repository: HeroesRepository) {}

  handle(event: HeroKilledDragonEvent) {
    // 业务逻辑
  }
}
```

> info **提示** 当你开始使用事件处理器时，需要注意你已经脱离了传统的 HTTP Web 上下文。
>
> - `CommandHandler`（命令处理器）中的错误仍然可以被内置的 [异常过滤器](/exception-filters) 捕获。
> - `EventHandler`（事件处理器）中的错误无法被异常过滤器捕获：你需要手动处理这些错误。可以通过简单的 `try/catch`，或者使用 [Sagas](/recipes/cqrs#sagas) 触发补偿事件，或采用其他解决方案。
> - `CommandHandler` 中仍然可以向客户端发送 HTTP 响应。
> - `EventHandler` 中则无法发送 HTTP 响应。如果你需要向客户端推送信息，可以使用 [WebSocket](/websockets/gateways)、[SSE](/techniques/server-sent-events) 或其他方案。

与命令和查询一样，记得在模块中将 `HeroKilledDragonHandler` 注册为提供者：

```typescript
providers: [HeroKilledDragonHandler]
```

#### Saga（编排器）

Saga 是一种长时间运行的流程，用于监听事件并可能触发新的命令。它通常用于管理应用中的复杂工作流。例如，当用户注册时，Saga 可以监听 `UserRegisteredEvent`，并向用户发送欢迎邮件。

Saga 是非常强大的功能。单个 Saga 可以监听 1..\* 个事件。借助 [RxJS](https://github.com/ReactiveX/rxjs) 库，我们可以对事件流进行过滤、映射、分支和合并，从而创建复杂的工作流。每个 Saga 都会返回一个 Observable，该对象会生成命令实例。随后，这个命令会被 `CommandBus` **异步**分发。

下面我们来创建一个 Saga，用于监听 `HeroKilledDragonEvent`，并分发 `DropAncientItemCommand`（掉落远古物品命令）。

```typescript
@@filename(heroes-game.saga)
@Injectable()
export class HeroesGameSagas {
  @Saga()
  dragonKilled = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType(HeroKilledDragonEvent),
      map((event) => new DropAncientItemCommand(event.heroId, fakeItemID)),
    );
  }
}
@@switch
@Injectable()
export class HeroesGameSagas {
  @Saga()
  dragonKilled = (events$) => {
    return events$.pipe(
      ofType(HeroKilledDragonEvent),
      map((event) => new DropAncientItemCommand(event.heroId, fakeItemID)),
    );
  }
}
```

> info **提示** `ofType` 操作符和 `@Saga()` 装饰器均由 `@nestjs/cqrs` 包导出。

`@Saga()` 装饰器用于将方法标记为 Saga。`events$` 参数是包含所有事件的 Observable 流。`ofType` 操作符会根据指定的事件类型过滤事件流。`map` 操作符则将事件映射为新的命令实例。

在本例中，我们将 `HeroKilledDragonEvent` 映射为 `DropAncientItemCommand`。随后，`DropAncientItemCommand` 会被 `CommandBus` 自动分发。

与查询、命令和事件处理器类似，请确保在模块中将 `HeroesGameSagas` 注册为提供者：

```typescript
providers: [HeroesGameSagas]
```

#### 未处理异常

事件处理器是异步执行的，因此必须始终妥善处理异常，以防止应用进入不一致的状态。如果异常未被处理，`EventBus` 会创建一个 `UnhandledExceptionInfo` 对象，并将其推送到 `UnhandledExceptionBus` 流中。该流是一个 `Observable`，可用于处理未捕获的异常。

```typescript
private destroy$ = new Subject<void>();

constructor(private unhandledExceptionsBus: UnhandledExceptionBus) {
  this.unhandledExceptionsBus
    .pipe(takeUntil(this.destroy$))
    .subscribe((exceptionInfo) => {
      // 在此处处理异常
      // 例如：发送到外部服务、终止进程或发布新事件
    });
}

onModuleDestroy() {
  this.destroy$.next();
  this.destroy$.complete();
}
```

如果需要筛选特定异常，可以使用 `ofType` 操作符，示例如下：

```typescript
this.unhandledExceptionsBus
  .pipe(takeUntil(this.destroy$), UnhandledExceptionBus.ofType(TransactionNotAllowedException))
  .subscribe((exceptionInfo) => {
    // 在此处处理异常
  })
```

其中，`TransactionNotAllowedException` 是我们希望筛选的异常类型。

`UnhandledExceptionInfo` 对象包含以下属性：

```typescript
export interface UnhandledExceptionInfo<Cause = IEvent | ICommand, Exception = any> {
  /**
   * 抛出的异常。
   */
  exception: Exception
  /**
   * 异常的原因（事件或命令的引用）。
   */
  cause: Cause
}
```

#### 订阅所有事件

`CommandBus`、`QueryBus` 和 `EventBus` 都是 **可观察对象**。这意味着我们可以订阅整个事件流，例如处理所有事件。比如，我们可以将所有事件记录到控制台，或者保存到事件存储中。

```typescript
private destroy$ = new Subject<void>();

constructor(private eventBus: EventBus) {
  this.eventBus
    .pipe(takeUntil(this.destroy$))
    .subscribe((event) => {
      // 将事件保存到数据库
    });
}

onModuleDestroy() {
  this.destroy$.next();
  this.destroy$.complete();
}
```

#### 请求作用域（Request-scoping）

对于来自其他编程语言背景的开发者来说，可能会惊讶地发现，在 Nest 中，大多数内容都是在所有传入请求之间共享的。这包括数据库的连接池、具有全局状态的单例服务等。需要注意的是，Node.js 并不采用请求/响应的多线程无状态模型（即每个请求由独立线程处理）。因此，在我们的应用中使用单例实例是**安全**的。

不过，在某些特殊场景下，可能希望处理器具有基于请求的生命周期。例如在 GraphQL 应用中进行每个请求的缓存、请求追踪或多租户（multi-tenancy）等场景。你可以在[这里](/fundamentals/injection-scopes)了解如何控制作用域。

将请求作用域（Request-scoped）提供者与 CQRS 一起使用时会有一定复杂性，因为 `CommandBus`、`QueryBus` 和 `EventBus` 都是单例（singleton）。幸运的是，`@nestjs/cqrs` 包会自动为每个被处理的命令、查询或事件创建新的请求作用域处理器实例，从而简化了这一过程。

要让处理器成为请求作用域，你可以：

1. 依赖于请求作用域的提供者。
2. 或者，显式地在 `@CommandHandler`、`@QueryHandler` 或 `@EventsHandler` 装饰器中将其作用域设置为 `REQUEST`，如下所示：

```typescript
@CommandHandler(KillDragonCommand, {
  scope: Scope.REQUEST,
})
export class KillDragonHandler {
  // 具体实现
}
```

如果你想在任何请求作用域的提供者中注入请求载荷，可以使用 `@Inject(REQUEST)` 装饰器。不过，在 CQRS 场景下，请求载荷的具体内容取决于上下文 —— 它可能是 HTTP 请求、定时任务，或是任何触发命令的操作。

请求载荷必须是继承自 `AsyncContext`（由 `@nestjs/cqrs` 提供）的类实例。`AsyncContext` 作为请求上下文，能够在整个请求生命周期中持有可访问的数据。

```typescript
import { AsyncContext } from '@nestjs/cqrs'

export class MyRequest extends AsyncContext {
  constructor(public readonly user: User) {
    super()
  }
}
```

在执行命令时，将自定义的请求上下文作为第二个参数传递给 `CommandBus#execute` 方法：

```typescript
const myRequest = new MyRequest(user)
await this.commandBus.execute(new KillDragonCommand(heroId, killDragonDto.dragonId), myRequest)
```

这样，`MyRequest` 实例就会作为 `REQUEST` 提供者，注入到对应的处理器中：

```typescript
@CommandHandler(KillDragonCommand, {
  scope: Scope.REQUEST,
})
export class KillDragonHandler {
  constructor(
    @Inject(REQUEST) private request: MyRequest // 注入请求上下文
  ) {}

  // 处理器具体实现
}
```

对于查询（Query）同样适用：

```typescript
const myRequest = new MyRequest(user)
const hero = await this.queryBus.execute(new GetHeroQuery(heroId), myRequest)
```

在查询处理器中：

```typescript
@QueryHandler(GetHeroQuery, {
  scope: Scope.REQUEST,
})
export class GetHeroHandler {
  constructor(
    @Inject(REQUEST) private request: MyRequest // 注入请求上下文
  ) {}

  // 处理器具体实现
}
```

对于事件（Event），虽然你可以将请求提供者传递给 `EventBus#publish`，但这种做法较少见。更常见的方式是使用 `EventPublisher`，将请求提供者合并到模型对象中：

```typescript
const hero = this.publisher.mergeObjectContext(
  await this.repository.findOneById(+heroId),
  this.request // 在此注入请求上下文
)
```

订阅这些事件的请求作用域事件处理器将能够访问到请求提供者。

Saga 始终是单例实例，因为它们负责管理长生命周期的流程。不过，你可以从事件对象中获取请求提供者：

```typescript
@Saga()
dragonKilled = (events$: Observable<any>): Observable<ICommand> => {
  return events$.pipe(
    ofType(HeroKilledDragonEvent),
    map((event) => {
      const request = AsyncContext.of(event); // 获取请求上下文
      const command = new DropAncientItemCommand(event.heroId, fakeItemID);

      AsyncContext.merge(request, command); // 将请求上下文合并到命令中
      return command;
    }),
  );
}
```

或者，也可以使用 `request.attachTo(command)` 方法，将请求上下文绑定到命令对象上。

#### 示例

可用的完整示例请参考 [这里](https://github.com/kamilmysliwiec/nest-cqrs-example)。
