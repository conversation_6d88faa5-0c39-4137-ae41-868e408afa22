### Passport（认证）

[Passport](https://github.com/jaredhanson) 是目前社区中最流行的 Node.js 身份验证库，已经在众多生产环境应用中被广泛使用。通过 `@nestjs/passport` 模块，可以非常方便地将该库集成到 **Nest** 应用中。从整体上看，Passport 会执行以下几个主要步骤：

- 通过验证用户的"凭证"（如用户名/密码、JSON Web Token、或来自身份提供方的身份令牌）来进行身份验证
- 管理已认证状态（例如签发可携带的令牌，如 JWT，或创建 [Express 会话（Session）](https://github.com/expressjs/session)）
- 将已认证用户的信息附加到 `请求对象（Request Object）` 上，便于后续在路由处理器中使用

Passport 拥有丰富的 [策略（Strategy）](http://www.passportjs.org/) 生态，支持多种不同的身份验证机制。虽然 Passport 的核心思想很简单，但可选的策略种类繁多，灵活性极高。Passport 将这些多样化的步骤抽象为统一的标准模式，而 `@nestjs/passport` 模块则进一步将其封装为 Nest 框架中常见的结构。

在本章节中，我们将基于这些强大且灵活的模块，为 RESTful 接口（RESTful API）服务器实现一套完整的端到端身份验证解决方案。你可以根据本章介绍的思路，选择任意 Passport 策略，定制属于自己的身份验证方案。只需按照本章的步骤，即可构建出完整的示例。

#### 身份验证需求

让我们先明确下需求。在本例中，客户端会先通过用户名和密码进行身份验证。认证通过后，服务器会签发一个 JWT（JSON Web Token），后续请求可将该 JWT 作为 [授权头（Authorization Header）中的 Bearer Token](https://tools.ietf.org/html/rfc6750) 发送，用于证明身份。我们还会创建一个受保护的路由，仅允许携带有效 JWT 的请求访问。

我们将从第一个需求出发：用户身份验证。接着扩展为签发 JWT，最后实现一个校验 JWT 的受保护路由。

首先需要安装相关依赖包。Passport 提供了一个名为 [passport-local](https://github.com/jaredhanson/passport-local) 的策略（Strategy），实现了基于用户名和密码的身份验证机制，非常适合本例的需求。

```bash
$ npm install --save @nestjs/passport passport passport-local
$ npm install --save-dev @types/passport-local
```

> warning **注意** 无论你选择哪种 Passport 策略，都必须安装 `@nestjs/passport` 和 `passport` 这两个包。然后，根据具体的身份验证策略（如 `passport-jwt` 或 `passport-local`），再安装对应的策略包。此外，建议像上面这样安装策略的类型定义包（如 `@types/passport-local`），这样在编写 TypeScript 代码时会有更好的类型提示和开发体验。

#### 实现 Passport 策略

现在我们已经准备好实现身份验证功能了。我们先来概述一下实现**任意** Passport 策略的通用流程。可以将 Passport 理解为一个小型框架。它的优雅之处在于将身份验证流程抽象为几个基础步骤，开发者只需根据所选策略进行自定义。之所以称其为框架，是因为你可以通过传递自定义参数（通常为普通 JSON 对象）和自定义回调函数来配置它，Passport 会在合适的时机调用这些回调函数。`@nestjs/passport` 模块将这个框架以 Nest 风格的包进行封装，使其能够轻松集成到 Nest 应用中。下面我们会用到 `@nestjs/passport`，但首先让我们了解下**原生 Passport** 的工作方式。

在原生 Passport 中，配置一个策略需要提供两样东西：

1. 一组特定于该策略的选项。例如，在 JWT 策略中，你可能需要提供用于签名令牌的密钥。
2. 一个"验证回调"（verify callback），用于告诉 Passport 如何与用户存储（即你管理用户账户的地方）交互。在这里，你需要验证用户是否存在（或在需要时创建新用户），以及其凭证是否有效。Passport 期望该回调在验证成功时返回完整的用户对象，验证失败时返回 null（失败的定义包括用户未找到，或在 passport-local 策略下密码不匹配）。

使用 `@nestjs/passport` 时，你可以通过继承 `PassportStrategy` 类来配置 Passport 策略。你可以在子类的 `super()` 方法中传递策略选项（即上面第 1 项），可选地传入一个选项对象。你还需要通过实现子类的 `validate()` 方法来提供验证回调（即上面第 2 项）。

我们先生成一个 `AuthModule`，并在其中创建一个 `AuthService`：

```bash
$ nest g module auth
$ nest g service auth
```

在实现 `AuthService` 的过程中，我们会发现将用户相关操作封装到 `UsersService` 中会更方便，因此现在一并生成该模块和服务：

```bash
$ nest g module users
$ nest g service users
```

将这些自动生成文件的默认内容替换为如下所示。在我们的示例应用中，`UsersService` 仅维护一个硬编码的内存用户列表，并提供一个根据用户名查找用户的方法。在实际应用中，这里通常会构建你的用户模型和持久化层，可以选择你喜欢的库（如 TypeORM、Sequelize、Mongoose 等）。

```typescript
@@filename(users/users.service)
import { Injectable } from '@nestjs/common';

// 这里应该是一个真正代表用户实体的类或接口
export type User = any;

@Injectable()
export class UsersService {
  private readonly users = [
    {
      userId: 1,
      username: 'john',
      password: 'changeme',
    },
    {
      userId: 2,
      username: 'maria',
      password: 'guess',
    },
  ];

  async findOne(username: string): Promise<User | undefined> {
    return this.users.find(user => user.username === username);
  }
}
@@switch
import { Injectable } from '@nestjs/common';

@Injectable()
export class UsersService {
  constructor() {
    this.users = [
      {
        userId: 1,
        username: 'john',
        password: 'changeme',
      },
      {
        userId: 2,
        username: 'maria',
        password: 'guess',
      },
    ];
  }

  async findOne(username) {
    return this.users.find(user => user.username === username);
  }
}
```

在 `UsersModule` 中，唯一需要做的更改就是在 `@Module` 装饰器的 exports 数组中添加 `UsersService`，这样它才能在该模块之外被访问到（我们很快会在 `AuthService` 中用到它）。

```typescript
@@filename(users/users.module)
import { Module } from '@nestjs/common';
import { UsersService } from './users.service';

@Module({
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
@@switch
import { Module } from '@nestjs/common';
import { UsersService } from './users.service';

@Module({
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
```

我们的 `AuthService` 负责检索用户并验证密码。为此，我们创建了一个 `validateUser()` 方法。在下面的代码中，我们使用了便捷的 ES6 展开运算符，在返回用户对象前去除了 password 属性。稍后我们会在 Passport 的本地策略（local strategy）中调用 `validateUser()` 方法。

```typescript
@@filename(auth/auth.service)
import { Injectable } from '@nestjs/common';
import { UsersService } from '../users/users.service';

@Injectable()
export class AuthService {
  constructor(private usersService: UsersService) {}

  async validateUser(username: string, pass: string): Promise<any> {
    const user = await this.usersService.findOne(username);
    if (user && user.password === pass) {
      const { password, ...result } = user;
      return result;
    }
    return null;
  }
}
@@switch
import { Injectable, Dependencies } from '@nestjs/common';
import { UsersService } from '../users/users.service';

@Injectable()
@Dependencies(UsersService)
export class AuthService {
  constructor(usersService) {
    this.usersService = usersService;
  }

  async validateUser(username, pass) {
    const user = await this.usersService.findOne(username);
    if (user && user.password === pass) {
      const { password, ...result } = user;
      return result;
    }
    return null;
  }
}
```

> Warning **警告** 当然，在真实应用中，你绝不能以明文形式存储密码。你应该使用像 [bcrypt](https://github.com/kelektiv/node.bcrypt.js#readme) 这样的库，采用加盐的单向哈希算法。这样，你只会存储经过哈希处理的密码，并将存储的密码与**传入**密码的哈希值进行比较，从而永远不会以明文形式存储或暴露用户密码。为了让我们的示例应用更简单，这里违反了这一绝对原则，直接使用明文密码。**请不要在你的真实应用中这样做！**

现在，我们需要更新 `AuthModule`，导入 `UsersModule`。

```typescript
@@filename(auth/auth.module)
import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [UsersModule],
  providers: [AuthService],
})
export class AuthModule {}
@@switch
import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [UsersModule],
  providers: [AuthService],
})
export class AuthModule {}
```

#### 实现 Passport 本地策略

现在我们可以实现 Passport 的**本地身份验证策略**了。在 `auth` 文件夹下创建一个名为 `local.strategy.ts` 的文件，并添加如下代码：

```typescript
@@filename(auth/local.strategy)
import { Strategy } from 'passport-local';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { AuthService } from './auth.service';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private authService: AuthService) {
    super();
  }

  async validate(username: string, password: string): Promise<any> {
    const user = await this.authService.validateUser(username, password);
    if (!user) {
      throw new UnauthorizedException();
    }
    return user;
  }
}
@@switch
import { Strategy } from 'passport-local';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException, Dependencies } from '@nestjs/common';
import { AuthService } from './auth.service';

@Injectable()
@Dependencies(AuthService)
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(authService) {
    super();
    this.authService = authService;
  }

  async validate(username, password) {
    const user = await this.authService.validateUser(username, password);
    if (!user) {
      throw new UnauthorizedException();
    }
    return user;
  }
}
```

我们遵循了前文介绍的 Passport 策略实现方式。在本例中，passport-local 策略没有额外的配置选项，因此构造函数中只需调用 `super()`，无需传递配置对象。

> info **提示** 你可以在调用 `super()` 时传递一个配置对象，以自定义 passport 策略的行为。在本例中，passport-local 策略默认期望请求体中包含 `username` 和 `password` 字段。你可以通过传递配置对象来指定不同的字段名，例如：`super({{ '{' }} usernameField: 'email' {{ '}' }})`。更多信息请参考 [Passport 官方文档](http://www.passportjs.org/docs/configure/)。

我们还实现了 `validate()` 方法。对于每种策略，Passport 都会调用验证函数（在 `@nestjs/passport` 中通过 `validate()` 方法实现），并传递相应的参数。对于本地策略，Passport 期望 `validate()` 方法的签名为：`validate(username: string, password: string): any`。

大部分验证逻辑都在我们的 `AuthService`（借助 `UsersService`）中完成，因此该方法实现较为简洁。**任何** Passport 策略的 `validate()` 方法实现模式都类似，仅在凭证的具体表现形式上有所不同。如果用户存在且凭证有效，则返回该用户，Passport 会继续后续流程（如在 `请求对象（Request Object）` 上创建 `user` 属性），请求处理流程也将继续。如果未找到用户，则抛出异常，由我们的 <a href="exception-filters">异常过滤器（exceptions layer）</a> 处理。

通常，不同策略的 `validate()` 方法主要区别在于**如何**判断用户是否存在且有效。例如，在 JWT 策略中，可能需要根据解码后的 token 中的 `userId` 是否存在于用户数据库，或是否在已吊销 token 列表中进行判断。因此，这种通过继承并实现特定策略验证逻辑的模式，既统一又易于扩展。

接下来需要配置我们的 `AuthModule`，以使用刚刚定义的 Passport 相关功能。将 `auth.module.ts` 更新为如下内容：

```typescript
@@filename(auth/auth.module)
import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { UsersModule } from '../users/users.module';
import { PassportModule } from '@nestjs/passport';
import { LocalStrategy } from './local.strategy';

@Module({
  imports: [UsersModule, PassportModule],
  providers: [AuthService, LocalStrategy],
})
export class AuthModule {}
@@switch
import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { UsersModule } from '../users/users.module';
import { PassportModule } from '@nestjs/passport';
import { LocalStrategy } from './local.strategy';

@Module({
  imports: [UsersModule, PassportModule],
  providers: [AuthService, LocalStrategy],
})
export class AuthModule {}
```

#### 内置 Passport 守卫（Guard）

<a href="guards">守卫</a> 章节介绍了守卫的主要功能：决定某个请求是否会被路由处理器（Route
Handler）处理。这个结论依然成立，我们很快会用到这种标准能力。不过，在使用 `@nestjs/passport`
模块时，还会引入一个新的小变化，初学者可能会感到困惑，所以我们现在先来讨论一下。可以这样理解，从身份验证（Authentication）的角度看，你的应用可以有两种状态：

1. 用户/客户端**未**登录（未认证）
2. 用户/客户端**已**登录（已认证）

对于第一种情况（用户未登录），我们需要完成两个不同的功能：

- 限制未认证用户可访问的路由（即，拒绝访问受限路由）。我们会用守卫来完成这个功能，把守卫加在受保护的路由上。你可能已经猜到，我们会在这个守卫中检查 JWT 是否有效，所以等我们能成功签发 JWT 后再来实现这个守卫。

- 当未认证用户尝试登录时，发起**认证步骤**。也就是说，我们要为合法用户**签发** JWT。想一想，我们需要通过 `POST` 提交用户名和密码来发起认证，所以会设置一个 `POST /auth/login` 路由来处理这个过程。那么问题来了：我们该如何在这个路由中调用 passport-local 策略呢？

答案很简单：使用另一种稍有不同的守卫。`@nestjs/passport` 模块为我们提供了一个内置守卫，可以帮我们完成这件事。这个守卫会调用 Passport 策略，并启动上述流程（获取凭证、执行验证函数、创建 `user` 属性等）。

对于第二种情况（用户已登录），只需依赖我们前面讨论过的标准守卫类型，让已登录用户可以访问受保护的路由。

<app-banner-courses-auth></app-banner-courses-auth>

#### 登录路由

有了策略之后，我们现在可以实现一个最基础的 `/auth/login` 路由，并应用内置的守卫来启动 passport-local 流程。

打开 `app.controller.ts` 文件，并将其内容替换为如下代码：

```typescript
@@filename(app.controller)
import { Controller, Request, Post, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Controller()
export class AppController {
  @UseGuards(AuthGuard('local'))
  @Post('auth/login')
  async login(@Request() req) {
    return req.user;
  }
}
@@switch
import { Controller, Bind, Request, Post, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Controller()
export class AppController {
  @UseGuards(AuthGuard('local'))
  @Post('auth/login')
  @Bind(Request())
  async login(req) {
    return req.user;
  }
}
```

通过 `@UseGuards(AuthGuard('local'))`，我们使用了一个 `AuthGuard`，当我们扩展 passport-local 策略时，`@nestjs/passport` 会**自动为我们提供**这个守卫。让我们来详细解释一下。我们的 Passport 本地策略默认名称为 `'local'`。我们在 `@UseGuards()` 装饰器中引用该名称，将其与 `passport-local` 包提供的代码关联起来。当应用中存在多个 Passport 策略时（每个策略都可能提供一个特定的 `AuthGuard`），这个名称用于区分要调用哪个策略。虽然目前我们只有一个策略，但很快会添加第二个，因此需要通过名称来区分。

为了测试我们的路由，当前让 `/auth/login` 路由直接返回用户对象。这也让我们可以演示 Passport 的另一个特性：Passport 会自动创建一个 `user` 对象，该对象基于我们在 `validate()` 方法中返回的值，并将其赋值到 `Request` 上的 `req.user` 属性。稍后我们会将其替换为生成并返回 JWT 的代码。

由于这些是 API 路由，我们将使用常用的 [cURL](https://curl.haxx.se/) 工具进行测试。你可以使用 `UsersService` 中硬编码的任意 `user` 对象进行测试。

```bash
$ # 向 /auth/login 发送 POST 请求
$ curl -X POST http://localhost:3000/auth/login -d '{"username": "john", "password": "changeme"}' -H "Content-Type: application/json"
$ # 结果 -> {"userId":1,"username":"john"}
```

虽然这样可以正常工作，但直接将策略名称传递给 `AuthGuard()` 会在代码中引入魔法字符串（magic string）。因此，我们推荐如下所示创建你自己的类：

```typescript
@@filename(auth/local-auth.guard)
import { Injectable } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class LocalAuthGuard extends AuthGuard('local') {}
```

现在，我们可以更新 `/auth/login` 路由处理器，使用 `LocalAuthGuard`：

```typescript
@UseGuards(LocalAuthGuard)
@Post('auth/login')
async login(@Request() req) {
  return req.user;
}
```

#### 注销路由

要实现注销功能，我们可以创建一个额外的路由，调用 `res.logout()` 来清除用户的会话（Session）。这种方式是基于会话认证（session-based authentication）中常见的做法，但并不适用于 JWT。

```typescript
@UseGuards(LocalAuthGuard)
@Post('auth/logout')
async logout(@Request() req) {
  return req.logout();
}
```

#### JWT 功能

接下来我们将进入认证系统中的 JWT 部分。让我们回顾并细化一下需求：

- 允许用户通过用户名和密码进行身份验证，并返回一个 JWT，用于后续访问受保护的 API 接口。我们已经基本实现了这个需求。要完成它，我们还需要编写签发 JWT 的相关代码。
- 创建基于 JWT 有效性（作为 Bearer Token）保护的 API 路由。

我们需要安装几个额外的包来支持 JWT 相关功能：

```bash
$ npm install --save @nestjs/jwt passport-jwt
$ npm install --save-dev @types/passport-jwt
```

`@nestjs/jwt` 包（详见 [这里](https://github.com/nestjs/jwt)）是一个用于操作 JWT 的工具包。`passport-jwt` 是实现 JWT 策略的 Passport 包，`@types/passport-jwt` 则为 TypeScript 提供类型定义。

下面我们来详细看看 `POST /auth/login` 请求的处理流程。我们为该路由添加了由 passport-local 策略提供的内置 `AuthGuard`（认证守卫）。这意味着：

1. **只有当用户通过验证后，路由处理器才会被调用**
2. `req` 参数会包含一个 `user` 属性（在 passport-local 认证流程中由 Passport 自动填充）

基于以上内容，我们现在可以生成一个真正的 JWT，并在该路由中返回。为了保持服务的模块化，我们将在 `authService` 中处理 JWT 的生成。请打开 `auth` 文件夹下的 `auth.service.ts` 文件，添加 `login()` 方法，并按如下方式引入 `JwtService`：

```typescript
@@filename(auth/auth.service)
import { Injectable } from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService
  ) {}

  async validateUser(username: string, pass: string): Promise<any> {
    const user = await this.usersService.findOne(username);
    if (user && user.password === pass) {
      const { password, ...result } = user;
      return result;
    }
    return null;
  }

  async login(user: any) {
    const payload = { username: user.username, sub: user.userId };
    return {
      access_token: this.jwtService.sign(payload),
    };
  }
}

@@switch
import { Injectable, Dependencies } from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';

@Dependencies(UsersService, JwtService)
@Injectable()
export class AuthService {
  constructor(usersService, jwtService) {
    this.usersService = usersService;
    this.jwtService = jwtService;
  }

  async validateUser(username, pass) {
    const user = await this.usersService.findOne(username);
    if (user && user.password === pass) {
      const { password, ...result } = user;
      return result;
    }
    return null;
  }

  async login(user) {
    const payload = { username: user.username, sub: user.userId };
    return {
      access_token: this.jwtService.sign(payload),
    };
  }
}
```

我们使用了 `@nestjs/jwt` 库，它提供了 `sign()` 方法，用于根据 `user` 对象的部分属性生成 JWT 。随后，我们将其作为一个仅包含 `access_token` 属性的简单对象返回。注意：我们选择使用 `sub` 属性来存储 `userId`，以符合 JWT 标准。不要忘记在 `AuthService` 中注入 JwtService 提供者。

现在，我们需要更新 `AuthModule`，引入新的依赖并配置 `JwtModule`。

首先，在 `auth` 文件夹下创建 `constants.ts` 文件，并添加如下代码：

```typescript
@@filename(auth/constants)
export const jwtConstants = {
  secret: 'DO NOT USE THIS VALUE. INSTEAD, CREATE A COMPLEX SECRET AND KEEP IT SAFE OUTSIDE OF THE SOURCE CODE.',
};
@@switch
export const jwtConstants = {
  secret: 'DO NOT USE THIS VALUE. INSTEAD, CREATE A COMPLEX SECRET AND KEEP IT SAFE OUTSIDE OF THE SOURCE CODE.',
};
```

我们将使用该常量在 JWT 签名和验证步骤之间共享密钥。

> Warning **警告** **请勿公开暴露此密钥**。这里为了演示代码的作用才将密钥明文展示，但在生产环境中，**你必须通过机密管理工具、环境变量或配置服务等方式妥善保护此密钥**。

接下来，打开 `auth` 文件夹下的 `auth.module.ts` 文件，并将其更新为如下内容：

```typescript
@@filename(auth/auth.module)
import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { LocalStrategy } from './local.strategy';
import { UsersModule } from '../users/users.module';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { jwtConstants } from './constants';

@Module({
  imports: [
    UsersModule,
    PassportModule,
    JwtModule.register({
      secret: jwtConstants.secret,
      signOptions: { expiresIn: '60s' },
    }),
  ],
  providers: [AuthService, LocalStrategy],
  exports: [AuthService],
})
export class AuthModule {}
@@switch
import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { LocalStrategy } from './local.strategy';
import { UsersModule } from '../users/users.module';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { jwtConstants } from './constants';

@Module({
  imports: [
    UsersModule,
    PassportModule,
    JwtModule.register({
      secret: jwtConstants.secret,
      signOptions: { expiresIn: '60s' },
    }),
  ],
  providers: [AuthService, LocalStrategy],
  exports: [AuthService],
})
export class AuthModule {}
```

我们通过 `register()` 方法配置 `JwtModule`，传入一个配置对象。更多关于 Nest `JwtModule` 的信息请参见[这里](https://github.com/nestjs/jwt/blob/master/README.md)，更多配置项说明请参见[这里](https://github.com/auth0/node-jsonwebtoken#usage)。

现在我们可以更新 `/auth/login` 路由，使其返回 JWT。

```typescript
@@filename(app.controller)
import { Controller, Request, Post, UseGuards } from '@nestjs/common';
import { LocalAuthGuard } from './auth/local-auth.guard';
import { AuthService } from './auth/auth.service';

@Controller()
export class AppController {
  constructor(private authService: AuthService) {}

  @UseGuards(LocalAuthGuard)
  @Post('auth/login')
  async login(@Request() req) {
    return this.authService.login(req.user);
  }
}
@@switch
import { Controller, Bind, Request, Post, UseGuards } from '@nestjs/common';
import { LocalAuthGuard } from './auth/local-auth.guard';
import { AuthService } from './auth/auth.service';

@Controller()
export class AppController {
  constructor(private authService: AuthService) {}

  @UseGuards(LocalAuthGuard)
  @Post('auth/login')
  @Bind(Request())
  async login(req) {
    return this.authService.login(req.user);
  }
}
```

让我们再次使用 cURL 测试这些路由。你可以使用 `UsersService` 中硬编码的任意 `user` 对象进行测试。

```bash
$ # 向 /auth/login 发送 POST 请求
$ curl -X POST http://localhost:3000/auth/login -d '{"username": "john", "password": "changeme"}' -H "Content-Type: application/json"
$ # 返回结果 -> {"access_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}
$ # 注意：上方 JWT 已截断
```

#### 实现 Passport JWT

现在我们可以解决最后一个需求：通过要求请求中必须携带有效的 JWT 来保护接口。Passport 同样可以帮助我们实现这一点。它提供了用于保护 RESTful 接口的 [passport-jwt](https://github.com/mikenicholson/passport-jwt) 策略。首先，在 `auth` 文件夹下创建一个名为 `jwt.strategy.ts` 的文件，并添加如下代码：

```typescript
@@filename(auth/jwt.strategy)
import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { jwtConstants } from './constants';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: jwtConstants.secret,
    });
  }

  async validate(payload: any) {
    return { userId: payload.sub, username: payload.username };
  }
}
@@switch
import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { jwtConstants } from './constants';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: jwtConstants.secret,
    });
  }

  async validate(payload) {
    return { userId: payload.sub, username: payload.username };
  }
}
```

在 `JwtStrategy` 中，我们遵循了前文介绍的所有 Passport 策略的通用实现方式。该策略需要一些初始化配置，因此我们通过在 `super()` 调用中传递一个选项对象来完成。你可以在 [这里](https://github.com/mikenicholson/passport-jwt#configure-strategy) 阅读所有可用选项的详细说明。本例中，主要配置如下：

- `jwtFromRequest`：指定如何从 `请求对象（Request Object）` 中提取 JWT。我们采用标准做法，即在 API 请求的 Authorization 请求头（Header）中以 Bearer Token 方式传递。更多提取方式可参考 [这里](https://github.com/mikenicholson/passport-jwt#extracting-the-jwt-from-the-request)。
- `ignoreExpiration`：为明确起见，这里设置为默认值 `false`，即将 JWT 是否过期的校验责任交由 Passport 模块处理。这意味着如果路由收到已过期的 JWT，请求会被拒绝，并返回 `401 Unauthorized` 响应。Passport 会自动帮我们处理这一流程。
- `secretOrKey`：我们这里直接传递对称加密密钥用于签名 token。对于生产环境，更推荐使用 PEM 编码的公钥等方式，详见 [这里](https://github.com/mikenicholson/passport-jwt#configure-strategy)。无论如何，**切勿将密钥暴露在公共环境**。

`validate()` 方法值得重点说明。对于 jwt-strategy，Passport 首先会校验 JWT 的签名并解码 JSON，然后将解码后的 JSON 作为唯一参数传递给我们的 `validate()` 方法。基于 JWT 的签名机制，**我们可以保证收到的 token 是我们自己签发且属于有效用户的**。

因此，在 `validate()` 回调中，我们只需简单地返回一个包含 `userId` 和 `username` 属性的对象即可。再次提醒，Passport 会根据 `validate()` 方法的返回值构建 `user` 对象，并将其挂载到 `请求对象（Request Object）` 上。

此外，你也可以返回一个数组，第一个值用于创建 `user` 对象，第二个值用于创建 `authInfo` 对象。

值得一提的是，这种实现方式为我们后续扩展业务逻辑预留了"钩子"。例如，我们可以在 `validate()` 方法中查询数据库，获取更多用户信息，从而让 `user` 对象更加丰富；也可以在这里做进一步的 token 校验，比如将 `userId` 与已吊销 token 列表比对，实现 token 撤销机制。本示例采用的是快速的"无状态 JWT"模型，每次 API 调用都基于 JWT 的有效性即时授权，并在 `请求对象（Request Object）` 管道中提供一小部分用户信息（如 `userId` 和 `username`）。

将新的 `JwtStrategy` 作为 provider 添加到 `AuthModule` 中：

```typescript
@@filename(auth/auth.module)
import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { LocalStrategy } from './local.strategy';
import { JwtStrategy } from './jwt.strategy';
import { UsersModule } from '../users/users.module';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { jwtConstants } from './constants';

@Module({
  imports: [
    UsersModule,
    PassportModule,
    JwtModule.register({
      secret: jwtConstants.secret,
      signOptions: { expiresIn: '60s' },
    }),
  ],
  providers: [AuthService, LocalStrategy, JwtStrategy],
  exports: [AuthService],
})
export class AuthModule {}
@@switch
import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { LocalStrategy } from './local.strategy';
import { JwtStrategy } from './jwt.strategy';
import { UsersModule } from '../users/users.module';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { jwtConstants } from './constants';

@Module({
  imports: [
    UsersModule,
    PassportModule,
    JwtModule.register({
      secret: jwtConstants.secret,
      signOptions: { expiresIn: '60s' },
    }),
  ],
  providers: [AuthService, LocalStrategy, JwtStrategy],
  exports: [AuthService],
})
export class AuthModule {}
```

通过引入与签发 JWT 时相同的密钥，我们确保 Passport 执行的 **验证** 阶段和 AuthService 执行的 **签发** 阶段使用的是同一个密钥。

最后，我们定义一个继承自内置 `AuthGuard` 的 `JwtAuthGuard` 类：

```typescript
@@filename(auth/jwt-auth.guard)
import { Injectable } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {}
```

#### 实现受保护路由与 JWT 策略守卫

现在我们可以实现受保护的路由及其相关的守卫。

打开 `app.controller.ts` 文件，并按如下方式进行更新：

```typescript
@@filename(app.controller)
import { Controller, Get, Request, Post, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from './auth/jwt-auth.guard';
import { LocalAuthGuard } from './auth/local-auth.guard';
import { AuthService } from './auth/auth.service';

@Controller()
export class AppController {
  constructor(private authService: AuthService) {}

  @UseGuards(LocalAuthGuard)
  @Post('auth/login')
  async login(@Request() req) {
    return this.authService.login(req.user);
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  getProfile(@Request() req) {
    return req.user;
  }
}
@@switch
import { Controller, Dependencies, Bind, Get, Request, Post, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from './auth/jwt-auth.guard';
import { LocalAuthGuard } from './auth/local-auth.guard';
import { AuthService } from './auth/auth.service';

@Dependencies(AuthService)
@Controller()
export class AppController {
  constructor(authService) {
    this.authService = authService;
  }

  @UseGuards(LocalAuthGuard)
  @Post('auth/login')
  @Bind(Request())
  async login(req) {
    return this.authService.login(req.user);
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  @Bind(Request())
  getProfile(req) {
    return req.user;
  }
}
```

在这里，我们再次应用了 `@nestjs/passport` 模块在配置 passport-jwt 模块时自动为我们提供的 `AuthGuard`。该守卫（Guard）通过默认名称 `jwt` 被引用。当我们的 `GET /profile` 路由被访问时，守卫会自动调用我们自定义配置的 passport-jwt 策略，验证 JWT，并将 `user` 属性赋值到 `请求对象` 上。

请确保应用正在运行，并使用 `cURL` 测试这些路由。

```bash
$ # GET /profile
$ curl http://localhost:3000/profile
$ # 结果 -> {"statusCode":401,"message":"Unauthorized"}

$ # POST /auth/login
$ curl -X POST http://localhost:3000/auth/login -d '{"username": "john", "password": "changeme"}' -H "Content-Type: application/json"
$ # 结果 -> {"access_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2Vybm... }

$ # 使用上一步返回的 access_token 作为 Bearer 令牌访问 GET /profile
$ curl http://localhost:3000/profile -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2Vybm..."
$ # 结果 -> {"userId":1,"username":"john"}
```

请注意，在 `AuthModule` 中，我们将 JWT 的过期时间配置为 `60 秒`。这个过期时间通常来说太短了，关于令牌过期和刷新机制的详细处理超出了本文的范围。我们之所以这样设置，是为了演示 JWT 及 passport-jwt 策略的一个重要特性。如果你在认证后等待 60 秒再尝试请求 `GET /profile`，你会收到 `401 Unauthorized`（未授权）响应。这是因为 Passport 会自动检查 JWT 的过期时间，无需你在应用中手动处理。

至此，我们已经完成了 JWT 身份验证的实现。JavaScript 客户端（如 Angular/React/Vue）以及其他 JavaScript 应用现在可以安全地与我们的 API 服务器进行身份验证和通信。

#### 扩展守卫

在大多数情况下，直接使用内置的 `AuthGuard` 类就已经足够了。然而，在某些场景下，你可能希望扩展默认的错误处理或身份验证逻辑。为此，你可以继承内置类，并在子类中重写相关方法。

```typescript
import { ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common'
import { AuthGuard } from '@nestjs/passport'

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  canActivate(context: ExecutionContext) {
    // 在这里添加自定义身份验证逻辑
    // 例如，可以调用 super.logIn(request) 来建立会话。
    return super.canActivate(context)
  }

  handleRequest(err, user, info) {
    // 你可以根据 "info" 或 "err" 参数抛出异常
    if (err || !user) {
      throw err || new UnauthorizedException()
    }
    return user
  }
}
```

除了扩展默认的错误处理和身份验证逻辑之外，我们还可以让身份验证通过一系列策略进行链式处理。第一个成功、重定向或抛出错误的策略会终止整个链。身份验证失败时会依次尝试每个策略，如果所有策略都失败，则最终身份验证失败。

```typescript
export class JwtAuthGuard extends AuthGuard(['strategy_jwt_1', 'strategy_jwt_2', '...']) { ... }
```

#### 全局启用身份验证

如果你希望绝大多数接口默认都受到保护，可以将身份验证守卫（Guard）注册为[全局守卫](/guards#binding-guards)。这样就无需在每个控制器上都使用 `@UseGuards()` 装饰器，只需为需要公开访问的路由单独标记即可。

首先，在任意模块中，使用如下方式将 `JwtAuthGuard` 注册为全局守卫：

```typescript
providers: [
  {
    provide: APP_GUARD,
    useClass: JwtAuthGuard,
  },
],
```

这样配置后，Nest 会自动将 `JwtAuthGuard` 绑定到所有接口。

接下来，我们需要提供一种机制，用于声明哪些路由是公开的。为此，可以使用 `SetMetadata` 装饰器工厂函数创建一个自定义装饰器。

```typescript
import { SetMetadata } from '@nestjs/common'

export const IS_PUBLIC_KEY = 'isPublic'
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true)
```

在上面的代码中，我们导出了两个常量。一个是元数据键 `IS_PUBLIC_KEY`，另一个是我们自定义的装饰器 `Public`（你也可以根据项目需要命名为 `SkipAuth` 或 `AllowAnon` 等）。

现在有了自定义的 `@Public()` 装饰器后，可以用它来标记任何需要公开访问的方法，例如：

```typescript
@Public()
@Get()
findAll() {
  return [];
}
```

最后，我们需要让 `JwtAuthGuard` 在检测到 "isPublic" 元数据时直接放行。为此，可以使用 `Reflector` 类（详细说明见[此处](/guards#putting-it-all-together)）。

```typescript
@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(private reflector: Reflector) {
    super()
  }

  canActivate(context: ExecutionContext) {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ])
    if (isPublic) {
      return true
    }
    return super.canActivate(context)
  }
}
```

#### 请求作用域策略

Passport API 的核心机制是将策略注册到全局实例。因此，策略本身并不是为依赖请求的选项或按请求动态实例化而设计的（详细说明请参见[请求作用域](/fundamentals/injection-scopes)提供者）。当你将策略配置为请求作用域时，Nest 并不会实例化它，因为它并未绑定到具体的路由上。实际上，无法物理区分每个请求应执行哪些"请求作用域"策略。

不过，我们可以通过一些方式，在策略内部动态解析请求作用域的提供者。为此，我们可以利用 [模块引用](/fundamentals/module-ref)功能。

首先，打开 `local.strategy.ts` 文件，并以常规方式注入 `ModuleRef`：

```typescript
constructor(private moduleRef: ModuleRef) {
  super({
    passReqToCallback: true,
  });
}
```

> info **提示** `ModuleRef` 类需从 `@nestjs/core` 包中导入。

请确保如上所示，将 `passReqToCallback` 配置属性设置为 `true`。

下一步，将使用请求实例来获取当前的上下文标识符（context id），而不是生成新的标识符（关于请求上下文的更多内容请参见[此处](/fundamentals/module-ref#getting-current-sub-tree)）。

现在，在 `LocalStrategy` 类的 `validate()` 方法中，使用 `ContextIdFactory` 类的 `getByRequest()` 方法，根据请求对象创建上下文 id，并将其传递给 `resolve()` 方法：

```typescript
async validate(
  request: Request,
  username: string,
  password: string,
) {
  const contextId = ContextIdFactory.getByRequest(request);
  // "AuthService" 是一个请求作用域的提供者
  const authService = await this.moduleRef.resolve(AuthService, contextId);
  ...
}
```

在上面的示例中，`resolve()` 方法会异步返回 `AuthService` 提供者的请求作用域实例（假设 `AuthService` 已被标记为请求作用域提供者）。

#### 自定义 Passport

你可以通过 `register()` 方法传递任何标准的 Passport 自定义选项。可用的选项取决于你实现的具体策略。例如：

```typescript
PassportModule.register({ session: true })
```

你还可以在策略构造函数中传递一个配置对象来进行个性化设置。
以本地策略为例，你可以这样传递参数：

```typescript
constructor(private authService: AuthService) {
  super({
    usernameField: 'email',
    passwordField: 'password',
  });
}
```

更多属性名称请参考官方 [Passport 网站](http://www.passportjs.org/docs/oauth/)。

#### 命名策略

在实现策略时，你可以通过给 `PassportStrategy` 函数传递第二个参数为其指定名称。如果不指定，每个策略会有默认名称（例如 jwt 策略默认为 'jwt'）：

```typescript
export class JwtStrategy extends PassportStrategy(Strategy, 'myjwt')
```

之后，你可以通过装饰器 `@UseGuards(AuthGuard('myjwt'))` 来引用该策略。

#### GraphQL

如果你想在 [GraphQL](https://docs.nestjs.com/graphql/quick-start) 中使用 AuthGuard，需要继承内置的 `AuthGuard` 类，并重写 `getRequest()` 方法。

```typescript
@Injectable()
export class GqlAuthGuard extends AuthGuard('jwt') {
  getRequest(context: ExecutionContext) {
    const ctx = GqlExecutionContext.create(context)
    return ctx.getContext().req
  }
}
```

如果你想在 GraphQL 的 resolver 中获取当前已认证用户，可以定义一个 `@CurrentUser()` 装饰器：

```typescript
import { createParamDecorator, ExecutionContext } from '@nestjs/common'
import { GqlExecutionContext } from '@nestjs/graphql'

export const CurrentUser = createParamDecorator((data: unknown, context: ExecutionContext) => {
  const ctx = GqlExecutionContext.create(context)
  return ctx.getContext().req.user
})
```

要在 resolver 中使用上述装饰器，只需将其作为查询或变更的参数即可：

```typescript
@Query(() => User)
@UseGuards(GqlAuthGuard)
whoAmI(@CurrentUser() user: User) {
  return this.usersService.findById(user.id);
}
```

对于 passport-local 策略，你还需要将 GraphQL 上下文中的参数添加到请求体，以便 Passport 能够进行验证。否则会出现未授权错误。

```typescript
@Injectable()
export class GqlLocalAuthGuard extends AuthGuard('local') {
  getRequest(context: ExecutionContext) {
    const gqlExecutionContext = GqlExecutionContext.create(context)
    const gqlContext = gqlExecutionContext.getContext()
    const gqlArgs = gqlExecutionContext.getArgs()

    gqlContext.req.body = { ...gqlContext.req.body, ...gqlArgs }
    return gqlContext.req
  }
}
```
