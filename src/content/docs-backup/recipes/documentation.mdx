### 文档生成工具

**Compodoc** 是一款用于 Angular 应用的文档生成工具。由于 Nest 与 Angular 在项目结构和代码结构上有诸多相似之处，因此 **Compodoc** 同样适用于 Nest 应用。

#### 安装

在现有的 Nest 项目中集成 Compodoc 十分简单。首先，在操作系统终端中通过以下命令添加开发依赖：

```bash
$ npm i -D @compodoc/compodoc
```

#### 生成文档

使用以下命令生成项目文档（需要 npm 6 及以上版本以支持 `npx`）。更多选项请参阅[官方文档](https://compodoc.app/guides/usage.html)。

```bash
$ npx @compodoc/compodoc -p tsconfig.json -s
```

然后在浏览器中访问 [http://localhost:8080](http://localhost:8080)。你将看到初始的 Nest 命令行工具（Nest CLI）项目文档页面：

<figure>
  <img src="/assets/documentation-compodoc-1.jpg" />
</figure>
<figure>
  <img src="/assets/documentation-compodoc-2.jpg" />
</figure>

#### 参与贡献

你可以在 [这里](https://github.com/compodoc/compodoc) 参与 Compodoc 项目的开发与贡献。
