### 拦截器（Interceptor）

[常规拦截器](/interceptors) 与微服务拦截器之间没有区别。下面的示例演示了如何手动实例化一个方法作用域的拦截器。与基于 HTTP 的应用程序类似，你也可以使用控制器作用域的拦截器（即在控制器类前添加 `@UseInterceptors()` 装饰器（Decorator））。

```typescript
@@filename()
@UseInterceptors(new TransformInterceptor())
@MessagePattern({ cmd: 'sum' })
accumulate(data: number[]): number {
  return (data || []).reduce((a, b) => a + b);
}
@@switch
@UseInterceptors(new TransformInterceptor())
@MessagePattern({ cmd: 'sum' })
accumulate(data) {
  return (data || []).reduce((a, b) => a + b);
}
```
