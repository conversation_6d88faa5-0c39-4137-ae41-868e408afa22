### Helmet

[Helmet](https://github.com/helmetjs/helmet) 可以通过合理设置 HTTP Header，帮助你的应用防范一些常见的 Web 安全漏洞。一般来说，Helmet 是一组较小的中间件函数的集合，这些函数会设置与安全相关的 HTTP Header（详细说明可参见 [官方文档](https://github.com/helmetjs/helmet#how-it-works)）。

> info **提示** 需要注意的是，无论是将 `helmet` 作为全局中间件应用，还是注册为局部中间件，都必须在其他调用 `app.use()` 或可能调用 `app.use()` 的初始化函数之前进行。这是因为底层平台会根据中间件和路由的定义顺序来决定其生效范围。如果你在定义路由之后再使用像 `helmet` 或 `cors` 这样的中间件，那么这些中间件只会作用于其后定义的路由，而不会影响之前已定义的路由。

#### 在 Express（默认）中使用

首先安装所需的依赖包。

```bash
$ npm i --save helmet
```

安装完成后，将其作为全局中间件应用。

```typescript
import helmet from 'helmet'
// 在你的初始化文件中
app.use(helmet())
```

> warning **警告** 当你同时使用 `helmet`、`@apollo/server`（4.x）以及 [Apollo Sandbox](https://docs.nestjs.com/graphql/quick-start#apollo-sandbox) 时，Apollo Sandbox 可能会因为 [CSP（内容安全策略）](https://developer.mozilla.org/zh-CN/docs/Web/HTTP/CSP) 配置导致无法正常访问。为了解决该问题，请按照如下方式配置 CSP：
>
> ```typescript
> app.use(
>   helmet({
>     crossOriginEmbedderPolicy: false,
>     contentSecurityPolicy: {
>       directives: {
>         imgSrc: [`'self'`, 'data:', 'apollo-server-landing-page.cdn.apollographql.com'],
>         scriptSrc: [`'self'`, `https: 'unsafe-inline'`],
>         manifestSrc: [`'self'`, 'apollo-server-landing-page.cdn.apollographql.com'],
>         frameSrc: [`'self'`, 'sandbox.embed.apollographql.com'],
>       },
>     },
>   })
> )
> ```

#### 在 Fastify 中使用

如果你使用的是 `FastifyAdapter`，请安装 [@fastify/helmet](https://github.com/fastify/fastify-helmet) 包：

```bash
$ npm i --save @fastify/helmet
```

[fastify-helmet](https://github.com/fastify/fastify-helmet) 不应作为中间件使用，而应作为 [Fastify 插件](https://www.fastify.io/docs/latest/Reference/Plugins/) 通过 `app.register()` 方法注册：

```typescript
import helmet from '@fastify/helmet'
// 在你的初始化文件中
await app.register(helmet)
```

> warning **警告** 当你同时使用 `apollo-server-fastify` 和 `@fastify/helmet` 时，GraphQL playground 可能会因为 [CSP](https://developer.mozilla.org/zh-CN/docs/Web/HTTP/CSP) 配置导致无法访问。为了解决该冲突，请按照如下方式配置 CSP：
>
> ```typescript
> await app.register(fastifyHelmet, {
>   contentSecurityPolicy: {
>     directives: {
>       defaultSrc: [`'self'`, 'unpkg.com'],
>       styleSrc: [
>         `'self'`,
>         `'unsafe-inline'`,
>         'cdn.jsdelivr.net',
>         'fonts.googleapis.com',
>         'unpkg.com',
>       ],
>       fontSrc: [`'self'`, 'fonts.gstatic.com', 'data:'],
>       imgSrc: [`'self'`, 'data:', 'cdn.jsdelivr.net'],
>       scriptSrc: [`'self'`, `https: 'unsafe-inline'`, `cdn.jsdelivr.net`, `'unsafe-eval'`],
>     },
>   },
> })
>
> // 如果你完全不打算使用 CSP，可以这样配置：
> await app.register(fastifyHelmet, {
>   contentSecurityPolicy: false,
> })
> ```
