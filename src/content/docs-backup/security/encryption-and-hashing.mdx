### 加密与哈希

**加密（Encryption）** 是对信息进行编码的过程。该过程会将原始信息（明文，plaintext）转换为另一种形式，即密文（ciphertext）。理想情况下，只有被授权的用户才能将密文解码回明文，从而访问原始信息。加密本身并不能阻止信息被拦截，但可以让潜在的拦截者无法理解内容。加密是一个双向过程；被加密的信息可以通过正确的密钥解密。

**哈希（Hashing）** 是将给定数据通过哈希函数（hash function）转换为另一个值的过程。哈希函数会根据数学算法生成新的值。哈希操作完成后，理论上无法通过输出结果反推出输入内容。

#### 加密

Node.js 提供了内置的 [crypto 模块](https://nodejs.org/api/crypto.html)，你可以用它来加密和解密字符串、数字、Buffer、流等。Nest 本身没有在此模块之上额外封装其他包，以避免引入不必要的抽象。

下面以 AES（高级加密标准，Advanced Encryption System） `'aes-256-ctr'` 算法的 CTR 加密模式为例：

```typescript
import { createCipheriv, randomBytes, scrypt } from 'crypto'
import { promisify } from 'util'

const iv = randomBytes(16)
const password = 'Password used to generate key'

// 密钥长度取决于算法。
// 对于 aes256，这里是 32 字节。
const key = (await promisify(scrypt)(password, 'salt', 32)) as Buffer
const cipher = createCipheriv('aes-256-ctr', key, iv)

const textToEncrypt = 'Nest'
const encryptedText = Buffer.concat([cipher.update(textToEncrypt), cipher.final()])
```

现在我们来解密 `encryptedText` ：

```typescript
import { createDecipheriv } from 'crypto'

const decipher = createDecipheriv('aes-256-ctr', key, iv)
const decryptedText = Buffer.concat([decipher.update(encryptedText), decipher.final()])
```

#### 哈希

对于哈希操作，我们推荐使用 [bcrypt](https://www.npmjs.com/package/bcrypt) 或 [argon2](https://www.npmjs.com/package/argon2) 这两个包。Nest 本身没有对这些模块进行额外封装，以避免引入不必要的抽象（让学习曲线更平缓）。

下面以 `bcrypt` 为例，演示如何对随机密码进行哈希。

首先安装所需依赖：

```shell
$ npm i bcrypt
$ npm i -D @types/bcrypt
```

安装完成后，你可以像下面这样使用 `hash` 函数：

```typescript
import * as bcrypt from 'bcrypt'

const saltOrRounds = 10
const password = 'random_password'
const hash = await bcrypt.hash(password, saltOrRounds)
```

如需生成盐值，可以使用 `genSalt` 函数：

```typescript
const salt = await bcrypt.genSalt()
```

如需比较/校验密码，可以使用 `compare` 函数：

```typescript
const isMatch = await bcrypt.compare(password, hash)
```

你可以在 [这里](https://www.npmjs.com/package/bcrypt) 阅读更多可用函数的介绍。
