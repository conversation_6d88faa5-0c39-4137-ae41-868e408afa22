### 授权（Authorization）

**授权（Authorization）** 指的是确定用户可以执行哪些操作的过程。例如，管理员用户可以创建、编辑和删除帖子，而非管理员用户仅被授权阅读帖子。

授权与身份验证（Authentication）是正交且独立的。不过，授权依赖于身份验证机制。

处理授权有多种不同的方法和策略。具体采用哪种方式，取决于项目的实际应用需求。本章将介绍几种可适配不同需求的授权实现方式。

#### 基础 RBAC 实现

基于角色的访问控制（Role-based access control，**RBAC**）是一种围绕角色和权限定义的、与策略无关的访问控制机制。本节将演示如何使用 Nest 的 [守卫（Guard）](/guards) 实现一个非常基础的 RBAC 机制。

首先，创建一个 `Role` 枚举（enum），用于表示系统中的角色：

```typescript
@@filename(role.enum)
export enum Role {
  User = 'user',
  Admin = 'admin',
}
```

> info **提示** 在更复杂的系统中，角色信息通常会存储在数据库中，或由外部身份验证提供方获取。

有了这个枚举后，我们可以创建一个 `@Roles()` 装饰器（Decorator）。该装饰器允许为特定资源指定所需的角色。

```typescript
@@filename(roles.decorator)
import { SetMetadata } from '@nestjs/common';
import { Role } from '../enums/role.enum';

export const ROLES_KEY = 'roles';
export const Roles = (...roles: Role[]) => SetMetadata(ROLES_KEY, roles);
@@switch
import { SetMetadata } from '@nestjs/common';

export const ROLES_KEY = 'roles';
export const Roles = (...roles) => SetMetadata(ROLES_KEY, roles);
```

现在我们有了自定义的 `@Roles()` 装饰器，可以用它来装饰任意路由处理器（Route Handler）。

```typescript
@@filename(cats.controller)
@Post()
@Roles(Role.Admin)
create(@Body() createCatDto: CreateCatDto) {
  this.catsService.create(createCatDto);
}
@@switch
@Post()
@Roles(Role.Admin)
@Bind(Body())
create(createCatDto) {
  this.catsService.create(createCatDto);
}
```

最后，我们需要创建一个 `RolesGuard` 类，用于将当前用户拥有的角色与当前路由所需的角色进行比对。为了获取路由的角色（自定义元数据），我们将使用框架自带、由 `@nestjs/core` 包提供的 `Reflector` 辅助类。

```typescript
@@filename(roles.guard)
import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<Role[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (!requiredRoles) {
      return true;
    }
    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.some((role) => user.roles?.includes(role));
  }
}
@@switch
import { Injectable, Dependencies } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

@Injectable()
@Dependencies(Reflector)
export class RolesGuard {
  constructor(reflector) {
    this.reflector = reflector;
  }

  canActivate(context) {
    const requiredRoles = this.reflector.getAllAndOverride(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (!requiredRoles) {
      return true;
    }
    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.some((role) => user.roles.includes(role));
  }
}
```

> info **提示** 关于如何在上下文中灵活使用 `Reflector`，请参考“[反射与元数据](/fundamentals/execution-context#reflection-and-metadata)”章节。

> warning **注意** 本示例被称为“基础”实现，是因为我们只在路由处理器级别检查角色。在实际应用中，某些接口/处理器可能涉及多个操作，每个操作都需要特定的权限。此时，你需要在业务逻辑中自行实现角色检查机制，这样会导致权限与具体操作的关联分散在各处，维护难度增加。

本示例假设 `request.user` 包含用户实例及其拥有的角色（`roles` 属性）。在你的应用中，通常会在自定义的 **身份验证守卫（Authentication Guard）** 中完成该关联。更多细节请参见 [身份验证](/security/authentication) 章节。

为了保证本示例正常工作，你的 `User` 类应如下所示：

```typescript
class User {
  // ...其他属性
  roles: Role[]
}
```

最后，记得注册 `RolesGuard`，可以在控制器级别或全局注册。例如：

```typescript
providers: [
  {
    provide: APP_GUARD,
    useClass: RolesGuard,
  },
],
```

当权限不足的用户请求接口时，Nest 会自动返回如下响应：

```typescript
{
  "statusCode": 403,
  "message": "Forbidden resource",
  "error": "Forbidden"
}
```

> info **提示** 如果你希望返回不同的错误响应，可以抛出自定义异常，而不是直接返回布尔值。

<app-banner-courses-auth></app-banner-courses-auth>

#### 基于声明的授权（Claims-based authorization）

当身份被创建时，可信任方可以为其分配一个或多个声明（Claim）。声明是一个名称-值对，用于表示主体（subject）可以做什么，而不是主体是什么。

要在 Nest 中实现基于声明的授权，可以按照上文 [RBAC](/security/authorization#basic-rbac-implementation) 部分展示的步骤进行，唯一显著的区别在于：不再检查特定角色，而是比较**权限**。每个用户都会被分配一组权限。同样，每个资源或端点会定义访问所需的权限（例如，通过专用的 `@RequirePermissions()` 装饰器来指定）。

```typescript
@@filename(cats.controller)
@Post()
@RequirePermissions(Permission.CREATE_CAT)
create(@Body() createCatDto: CreateCatDto) {
  this.catsService.create(createCatDto);
}
@@switch
@Post()
@RequirePermissions(Permission.CREATE_CAT)
@Bind(Body())
create(createCatDto) {
  this.catsService.create(createCatDto);
}
```

> info **提示** 在上面的示例中，`Permission`（类似于我们在 RBAC 部分展示的 `Role`）是一个 TypeScript 枚举类型，包含了系统中所有可用的权限。

#### 集成 CASL

[CASL](https://casl.js.org/) 是一个同构的授权（Authorization）库，用于限制客户端可访问的资源。它设计为可渐进式集成，能够轻松扩展，既支持简单的声明式权限，也支持完整的基于主体和属性的授权。

首先，安装 `@casl/ability` 包：

```bash
$ npm i @casl/ability
```

> info **提示** 本示例选择了 CASL，但你也可以根据项目需求和个人偏好选择其他库，如 `accesscontrol` 或 `acl`。

安装完成后，为了演示 CASL 的机制，我们先定义两个实体类：`User` 和 `Article`。

```typescript
class User {
  id: number
  isAdmin: boolean
}
```

`User` 类包含两个属性：`id`（唯一用户标识符）和 `isAdmin`（是否为管理员）。

```typescript
class Article {
  id: number
  isPublished: boolean
  authorId: number
}
```

`Article` 类有三个属性，分别是 `id`（唯一文章标识符）、`isPublished`（文章是否已发布）和 `authorId`（作者用户的 ID）。

现在，我们来梳理并细化本示例的需求：

- 管理员可以管理（增/查/改/删）所有实体
- 普通用户对所有内容仅有只读权限
- 用户可以更新自己撰写的文章（`article.authorId === userId`）
- 已发布的文章不可被删除（`article.isPublished === true`）

基于上述需求，我们首先创建一个 `Action` 枚举，表示用户可对实体执行的所有操作：

```typescript
export enum Action {
  Manage = 'manage',
  Create = 'create',
  Read = 'read',
  Update = 'update',
  Delete = 'delete',
}
```

> warning **注意** `manage` 是 CASL 中的特殊关键字，表示"任意操作"。

为了封装 CASL 库，我们现在生成 `CaslModule` 和 `CaslAbilityFactory`。

```bash
$ nest g module casl
$ nest g class casl/casl-ability.factory
```

接下来，我们可以在 `CaslAbilityFactory` 中定义 `createForUser()` 方法。该方法会为指定用户创建 Ability 对象：

```typescript
type Subjects = InferSubjects<typeof Article | typeof User> | 'all'

export type AppAbility = MongoAbility<[Action, Subjects]>

@Injectable()
export class CaslAbilityFactory {
  createForUser(user: User) {
    const { can, cannot, build } = new AbilityBuilder(createMongoAbility)

    if (user.isAdmin) {
      can(Action.Manage, 'all') // 对所有内容拥有读写权限
    } else {
      can(Action.Read, 'all') // 对所有内容仅有只读权限
    }

    can(Action.Update, Article, { authorId: user.id })
    cannot(Action.Delete, Article, { isPublished: true })

    return build({
      // 详细说明请参阅 https://casl.js.org/v6/en/guide/subject-type-detection#use-classes-as-subject-types
      detectSubjectType: (item) => item.constructor as ExtractSubjectType<Subjects>,
    })
  }
}
```

> warning **注意** `all` 是 CASL 中的特殊关键字，表示"任意主体"。

> info **提示** 自 CASL v6 起，`MongoAbility` 成为默认的能力类，取代了旧版的 `Ability`，以更好地支持基于条件的权限（采用 MongoDB 风格语法）。尽管名称中带有 Mongo，但它并不依赖于 MongoDB，只是用类似 Mongo 的语法进行条件判断。

> info **提示** `MongoAbility`、`AbilityBuilder`、`AbilityClass` 和 `ExtractSubjectType` 类均由 `@casl/ability` 包导出。

> info **提示** `detectSubjectType` 选项让 CASL 能够识别对象的主体类型。更多信息请参阅 [CASL 文档](https://casl.js.org/v6/en/guide/subject-type-detection#use-classes-as-subject-types)。

在上述示例中，我们通过 `AbilityBuilder` 类创建了 `MongoAbility` 实例。正如你所见，`can` 和 `cannot` 方法参数相同但含义相反，`can` 允许执行某操作，`cannot` 禁止。两者最多可接收 4 个参数。详细用法请参考 [CASL 官方文档](https://casl.js.org/v6/en/guide/intro)。

最后，记得将 `CaslAbilityFactory` 添加到 `CaslModule` 的 `providers` 和 `exports` 数组中：

```typescript
import { Module } from '@nestjs/common'
import { CaslAbilityFactory } from './casl-ability.factory'

@Module({
  providers: [CaslAbilityFactory],
  exports: [CaslAbilityFactory],
})
export class CaslModule {}
```

这样，我们就可以在任何引入了 `CaslModule` 的类中通过标准构造函数注入方式使用 `CaslAbilityFactory`：

```typescript
constructor(private caslAbilityFactory: CaslAbilityFactory) {}
```

然后在类中这样使用：

```typescript
const ability = this.caslAbilityFactory.createForUser(user)
if (ability.can(Action.Read, 'all')) {
  // "user" 拥有所有内容的读取权限
}
```

> info **提示** 了解更多 `MongoAbility` 类相关内容，请参阅 [CASL 官方文档](https://casl.js.org/v6/en/guide/intro)。

举例来说，假设有一个非管理员用户。此时，该用户可以读取文章，但不能新建或删除文章：

```typescript
const user = new User()
user.isAdmin = false

const ability = this.caslAbilityFactory.createForUser(user)
ability.can(Action.Read, Article) // true
ability.can(Action.Delete, Article) // false
ability.can(Action.Create, Article) // false
```

> info **提示** 虽然 `MongoAbility` 和 `AbilityBuilder` 都提供了 `can` 和 `cannot` 方法，但它们的用途不同，参数也略有差异。

同时，正如我们在需求中所述，用户应当可以更新自己撰写的文章：

```typescript
const user = new User()
user.id = 1

const article = new Article()
article.authorId = user.id

const ability = this.caslAbilityFactory.createForUser(user)
ability.can(Action.Update, article) // true

article.authorId = 2
ability.can(Action.Update, article) // false
```

如你所见，`MongoAbility` 实例让我们可以非常直观地检查权限。同样，`AbilityBuilder` 也允许我们以类似方式定义权限和条件。更多示例请参阅官方文档。

#### 进阶：实现 `PoliciesGuard`（策略守卫）

本节将演示如何构建一个更为复杂的守卫（Guard），用于检查用户是否满足可在方法级别配置的特定**授权策略（authorization policies）**。你也可以扩展它以支持类级别的策略配置。本例中，我们仅以 CASL 包为例进行说明，但实际使用中并不一定要选择该库。此外，我们将使用在上一节中创建的 `CaslAbilityFactory` 提供者（Provider）。

首先，让我们明确需求。目标是提供一种机制，允许为每个路由处理器（Route Handler）指定策略检查。我们将同时支持对象和函数两种方式（适用于简单检查或偏好函数式风格代码的开发者）。

让我们先定义策略处理器的接口：

```typescript
import { AppAbility } from '../casl/casl-ability.factory'

interface IPolicyHandler {
  handle(ability: AppAbility): boolean
}

type PolicyHandlerCallback = (ability: AppAbility) => boolean

export type PolicyHandler = IPolicyHandler | PolicyHandlerCallback
```

如上所述，我们提供了两种定义策略处理器的方式：一种是对象（实现了 `IPolicyHandler` 接口的类实例），另一种是函数（符合 `PolicyHandlerCallback` 类型）。

有了这些定义，我们可以创建一个 `@CheckPolicies()` 装饰器（Decorator）。该装饰器允许指定访问特定资源必须满足的策略。

```typescript
export const CHECK_POLICIES_KEY = 'check_policy'
export const CheckPolicies = (...handlers: PolicyHandler[]) =>
  SetMetadata(CHECK_POLICIES_KEY, handlers)
```

现在，让我们创建一个 `PoliciesGuard`（策略守卫），用于提取并执行所有绑定到路由处理器的策略处理器。

```typescript
@Injectable()
export class PoliciesGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private caslAbilityFactory: CaslAbilityFactory
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const policyHandlers =
      this.reflector.get<PolicyHandler[]>(CHECK_POLICIES_KEY, context.getHandler()) || []

    const { user } = context.switchToHttp().getRequest()
    const ability = this.caslAbilityFactory.createForUser(user)

    return policyHandlers.every((handler) => this.execPolicyHandler(handler, ability))
  }

  private execPolicyHandler(handler: PolicyHandler, ability: AppAbility) {
    if (typeof handler === 'function') {
      return handler(ability)
    }
    return handler.handle(ability)
  }
}
```

> info **提示** 在本例中，我们假设 `request.user` 包含用户实例。在你的应用中，你可能会在自定义**身份验证守卫（authentication guard）**中创建这种关联 - 详见[身份验证](/security/authentication)章节获取更多详情。

让我们分解一下这个例子。`policyHandlers` 是通过 `@CheckPolicies()` 装饰器分配给方法的处理器数组。接下来，我们使用 `CaslAbilityFactory#create` 方法构建 `Ability` 对象，用于验证用户是否有足够权限执行特定操作。我们将该对象传递给策略处理器，它可能是一个函数或实现了 `IPolicyHandler` 接口的类实例，后者暴露返回布尔值的 `handle()` 方法。最后，我们使用 `Array#every` 方法确保每个处理器都返回 `true` 值。

最后，要测试这个守卫，将它绑定到任何路由处理器，并注册一个内联策略处理器（函数式方法），如下所示：

```typescript
@Get()
@UseGuards(PoliciesGuard)
@CheckPolicies((ability: AppAbility) => ability.can(Action.Read, Article))
findAll() {
  return this.articlesService.findAll();
}
```

或者，我们可以定义一个实现 `IPolicyHandler` 接口的类：

```typescript
export class ReadArticlePolicyHandler implements IPolicyHandler {
  handle(ability: AppAbility) {
    return ability.can(Action.Read, Article)
  }
}
```

并按如下方式使用：

```typescript
@Get()
@UseGuards(PoliciesGuard)
@CheckPolicies(new ReadArticlePolicyHandler())
findAll() {
  return this.articlesService.findAll();
}
```

> warning **注意** 由于我们必须使用 `new` 关键字就地实例化策略处理器，`ReadArticlePolicyHandler` 类无法使用依赖注入（Dependency Injection）。这个问题可以通过 `ModuleRef#get` 方法解决（详情参见[此处](/fundamentals/module-ref)）。基本上，你需要允许通过 `@CheckPolicies()` 装饰器传递 `Type<IPolicyHandler>` 类型，而不是注册函数和实例。然后，在守卫内部，你可以使用类型引用获取实例：`moduleRef.get(YOUR_HANDLER_TYPE)` 或者甚至使用 `ModuleRef#create` 方法动态实例化它。
