### 跨源资源共享（CORS）

跨源资源共享（CORS，Cross-origin resource sharing）是一种允许从其他域请求资源的机制。在底层，Nest 会根据所用平台自动使用 Express 的 [cors](https://github.com/expressjs/cors) 或 Fastify 的 [@fastify/cors](https://github.com/fastify/fastify-cors) 包。这些包提供了多种可自定义的选项，您可以根据实际需求进行配置。

#### 快速上手

要启用跨源资源共享，只需在 Nest 应用对象上调用 `enableCors()` 方法。

```typescript
const app = await NestFactory.create(AppModule)
app.enableCors()
await app.listen(process.env.PORT ?? 3000)
```

`enableCors()` 方法可以接收一个可选的配置对象参数。该对象的可用属性请参考官方 [CORS 配置文档](https://github.com/expressjs/cors#configuration-options)。另外，您还可以传入一个 [回调函数](https://github.com/expressjs/cors#configuring-cors-asynchronously)，根据每个请求动态（异步）生成配置对象。

另外，也可以通过 `create()` 方法的 options 对象启用 CORS。将 `cors` 属性设置为 `true`，即可使用默认配置开启跨源资源共享。
或者，将 [CORS 配置对象](https://github.com/expressjs/cors#configuration-options) 或 [回调函数](https://github.com/expressjs/cors#configuring-cors-asynchronously) 作为 `cors` 属性的值传入，以自定义其行为。

```typescript
const app = await NestFactory.create(AppModule, { cors: true })
await app.listen(process.env.PORT ?? 3000)
```
