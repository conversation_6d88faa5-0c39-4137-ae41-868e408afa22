### 共享模型（Sharing models）

> warning **警告** 本章节仅适用于代码优先（code first）方式。

使用 TypeScript 作为项目后端的最大优势之一，就是能够通过一个通用的 TypeScript 包，在基于 TypeScript 的前端应用中复用相同的模型。

但这也带来了一个问题：使用代码优先方式创建的模型会被大量 GraphQL 相关的装饰器修饰。这些装饰器在前端中并无意义，反而会影响性能。

#### 使用模型 shim（model shim）

为了解决这个问题，NestJS 提供了一个"shim（垫片）"，允许你通过 `webpack`（或类似工具）的配置，将原有装饰器替换为无效代码。
要使用这个 shim，只需在 `@nestjs/graphql` 包和 shim 之间配置一个别名（alias）。

例如，在 webpack 中可以这样配置：

```typescript
resolve: { // 参考：https://webpack.js.org/configuration/resolve/
  alias: {
      "@nestjs/graphql": path.resolve(__dirname, "../node_modules/@nestjs/graphql/dist/extra/graphql-model-shim")
  }
}
```

> info **提示** [TypeORM](/techniques/database) 包也有类似的 shim，可以在[这里](https://github.com/typeorm/typeorm/blob/master/extra/typeorm-model-shim.js)找到。
