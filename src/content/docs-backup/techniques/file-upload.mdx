### 文件上传

为了处理文件上传，Nest 提供了一个基于 [multer](https://github.com/expressjs/multer) 中间件包（Express 框架专用）的内置模块。Multer 用于处理 `multipart/form-data` 格式的数据，这种格式主要用于通过 HTTP `POST` 请求上传文件。该模块支持高度自定义，您可以根据应用需求灵活调整其行为。

> warning **警告** Multer 无法处理非 multipart 格式（`multipart/form-data`）的数据。此外，请注意该包与 `FastifyAdapter` 不兼容。

为了获得更好的类型安全，我们建议安装 Multer 的类型定义包：

```shell
$ npm i -D @types/multer
```

安装该包后，我们就可以使用 `Express.Multer.File` 类型（可通过如下方式导入：`import { Express } from 'express'`）。

#### 基本示例

要上传单个文件，只需在路由处理器上使用 `FileInterceptor()` 拦截器，并通过 `@UploadedFile()` 装饰器从请求中提取 `file`。

```typescript
@@filename()
@Post('upload')
@UseInterceptors(FileInterceptor('file'))
uploadFile(@UploadedFile() file: Express.Multer.File) {
  console.log(file);
}
@@switch
@Post('upload')
@UseInterceptors(FileInterceptor('file'))
@Bind(UploadedFile())
uploadFile(file) {
  console.log(file);
}
```

> info **提示** `FileInterceptor()` 装饰器由 `@nestjs/platform-express` 包导出，`@UploadedFile()` 装饰器由 `@nestjs/common` 包导出。

`FileInterceptor()` 装饰器接收两个参数：

- `fieldName`：字符串，指定 HTML 表单中用于存放文件的字段名
- `options`：可选对象，类型为 `MulterOptions`。该对象与 multer 构造函数使用的参数一致（详细信息见 [此处](https://github.com/expressjs/multer#multeropts)）。

> warning **警告** `FileInterceptor()` 可能与 Google Firebase 等第三方云服务提供商不兼容。

#### 文件校验

在处理文件上传时，通常我们需要对传入文件的元数据进行校验，比如文件大小或文件的 MIME 类型。为此，你可以自定义一个[管道（Pipe）](https://docs.nestjs.com/pipes)，并将其绑定到带有 `UploadedFile` 装饰器（Decorator）的参数上。下面的示例演示了如何实现一个基础的文件大小校验管道：

```typescript
import { PipeTransform, Injectable, ArgumentMetadata } from '@nestjs/common'

@Injectable()
export class FileSizeValidationPipe implements PipeTransform {
  transform(value: any, metadata: ArgumentMetadata) {
    // "value" 是一个包含文件属性和元数据的对象
    const oneKb = 1000
    return value.size < oneKb
  }
}
```

你可以将其与 `FileInterceptor` 一起使用，示例如下：

```typescript
@Post('file')
@UseInterceptors(FileInterceptor('file'))
uploadFileAndValidate(@UploadedFile(
  new FileSizeValidationPipe(),
  // 这里还可以添加其他管道
) file: Express.Multer.File, ) {
  return file;
}
```

Nest 提供了一个内置管道，用于处理常见场景，并便于标准化地添加新的校验逻辑。这个管道叫做 `ParseFilePipe`，你可以这样使用：

```typescript
@Post('file')
uploadFileAndPassValidation(
  @Body() body: SampleDto,
  @UploadedFile(
    new ParseFilePipe({
      validators: [
        // ... 在这里放置一组文件校验器实例
      ]
    })
  )
  file: Express.Multer.File,
) {
  return {
    body,
    file: file.buffer.toString(),
  };
}
```

如你所见，必须指定一个文件验证器（FileValidator）数组，这些验证器会被 `ParseFilePipe` 管道依次执行。我们稍后会介绍验证器的接口，但值得一提的是，这个管道还支持另外两个可选选项：

<table>
  <tr>
    <td>
      <code>errorHttpStatusCode</code>
    </td>
    <td>
      当<b>任意</b>验证器失败时抛出的 HTTP 状态码。默认值为 <code>400</code>（BAD
      REQUEST，错误请求）。
    </td>
  </tr>
  <tr>
    <td>
      <code>exceptionFactory</code>
    </td>
    <td>一个工厂函数，接收错误信息并返回一个错误对象。</td>
  </tr>
</table>

现在，让我们回到 `FileValidator` 接口。要将验证器集成到该管道中，你可以使用内置实现，也可以自定义 `FileValidator`。请参考以下示例：

```typescript
export abstract class FileValidator<TValidationOptions = Record<string, any>> {
  constructor(protected readonly validationOptions: TValidationOptions) {}

  /**
   * 根据构造函数中传入的选项，判断该文件是否有效。
   * @param file 来自请求对象的文件
   */
  abstract isValid(file?: any): boolean | Promise<boolean>

  /**
   * 当验证失败时，构建错误信息。
   * @param file 来自请求对象的文件
   */
  abstract buildErrorMessage(file: any): string
}
```

> info **提示** > `FileValidator` 接口的 `isValid` 方法支持异步验证。如果你使用 express（默认）作为驱动，可以将 `file` 参数类型指定为 `Express.Multer.File`，以获得更好的类型安全。

`FileValidator` 是一个常规类，可以访问文件对象，并根据客户端提供的选项对其进行校验。Nest 内置了两种 `FileValidator`（文件校验器）实现，你可以在项目中直接使用：

- `MaxFileSizeValidator` —— 检查指定文件的大小是否小于给定值（以 `bytes` 字节为单位）
- `FileTypeValidator` —— 检查指定文件的 mime-type 是否与给定字符串或正则表达式匹配。默认情况下，会通过文件内容的 [magic number](https://www.ibm.com/support/pages/what-magic-number) 进行校验

要了解如何将这些校验器与前文提到的 `FileParsePipe`（文件解析管道）结合使用，我们来看一个基于上例修改的代码片段：

```typescript
@UploadedFile(
  new ParseFilePipe({
    validators: [
      new MaxFileSizeValidator({ maxSize: 1000 }),
      new FileTypeValidator({ fileType: 'image/jpeg' }),
    ],
  }),
)
file: Express.Multer.File,
```

> info **提示** 如果校验器数量较多，或者它们的选项让文件变得杂乱，你可以将这个数组定义在单独的文件中，并以命名常量（如 `fileValidators`）的方式导入。

最后，你还可以使用特殊的 `ParseFilePipeBuilder`（文件解析管道构建器）类来组合和构建校验器。通过如下方式使用它，可以避免手动实例化每个校验器，只需直接传递它们的选项即可：

```typescript
@UploadedFile(
  new ParseFilePipeBuilder()
    .addFileTypeValidator({
      fileType: 'jpeg',
    })
    .addMaxSizeValidator({
      maxSize: 1000
    })
    .build({
      errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY
    }),
)
file: Express.Multer.File,
```

> info **提示** 默认情况下，文件是必需的。如果你希望文件变为可选，可以在 `build` 函数的选项中添加 `fileIsRequired: false` 参数（与 `errorHttpStatusCode` 同级）。

#### 文件数组上传

要上传一个文件数组（即使用同一个字段名标识的多个文件），可以使用 `FilesInterceptor()` 装饰器（注意装饰器名称中的复数 **Files**）。该装饰器接收三个参数：

- `fieldName`：如上所述，用于指定字段名
- `maxCount`：可选参数，定义可接收的最大文件数量
- `options`：可选参数，`MulterOptions` 对象，具体说明见上文

使用 `FilesInterceptor()` 时，可以通过 `@UploadedFiles()` 装饰器从请求对象中提取文件数组。

```typescript
@@filename()
@Post('upload')
@UseInterceptors(FilesInterceptor('files'))
uploadFile(@UploadedFiles() files: Array<Express.Multer.File>) {
  console.log(files);
}
@@switch
@Post('upload')
@UseInterceptors(FilesInterceptor('files'))
@Bind(UploadedFiles())
uploadFile(files) {
  console.log(files);
}
```

> info **提示** `FilesInterceptor()` 装饰器由 `@nestjs/platform-express` 包导出，`@UploadedFiles()` 装饰器由 `@nestjs/common` 包导出。

#### 多文件上传

如需上传多个文件（每个字段名不同），请使用 `FileFieldsInterceptor()` 装饰器。该装饰器接收两个参数：

- `uploadedFields`：一个对象数组，每个对象都必须包含一个 `name` 属性（字符串，指定字段名），还可以包含可选的 `maxCount` 属性（如上所述）
- `options`：可选的 `MulterOptions` 配置对象（如上所述）

使用 `FileFieldsInterceptor()` 时，可以通过 `@UploadedFiles()` 装饰器从请求中提取文件。

```typescript
@@filename()
@Post('upload')
@UseInterceptors(FileFieldsInterceptor([
  { name: 'avatar', maxCount: 1 },
  { name: 'background', maxCount: 1 },
]))
uploadFile(@UploadedFiles() files: { avatar?: Express.Multer.File[], background?: Express.Multer.File[] }) {
  console.log(files);
}
@@switch
@Post('upload')
@Bind(UploadedFiles())
@UseInterceptors(FileFieldsInterceptor([
  { name: 'avatar', maxCount: 1 },
  { name: 'background', maxCount: 1 },
]))
uploadFile(files) {
  console.log(files);
}
```

#### 任意字段文件上传

如需上传所有字段（字段名任意），请使用 `AnyFilesInterceptor()` 装饰器。该装饰器可接收一个可选的 `options` 配置对象（如上所述）。

使用 `AnyFilesInterceptor()` 时，可以通过 `@UploadedFiles()` 装饰器从请求中提取文件。

```typescript
@@filename()
@Post('upload')
@UseInterceptors(AnyFilesInterceptor())
uploadFile(@UploadedFiles() files: Array<Express.Multer.File>) {
  console.log(files);
}
@@switch
@Post('upload')
@Bind(UploadedFiles())
@UseInterceptors(AnyFilesInterceptor())
uploadFile(files) {
  console.log(files);
}
```

#### 不接收文件

如果你希望接收 `multipart/form-data`（多部分表单数据），但**不允许上传任何文件**，可以使用 `NoFilesInterceptor` 拦截器。该拦截器会将 multipart 数据作为属性添加到请求体（request body）中。如果请求中包含文件，将会抛出 `BadRequestException`（错误请求异常）。

```typescript
@Post('upload')
@UseInterceptors(NoFilesInterceptor())
handleMultiPartData(@Body() body) {
  console.log(body)
}
```

#### 默认选项

你可以像上文所述，在文件拦截器中指定 multer 选项。若要设置全局默认选项，可以在导入 `MulterModule`（Multer 模块）时调用其静态方法 `register()`，并传入支持的选项。所有可用选项请参考 [这里](https://github.com/expressjs/multer#multeropts)。

```typescript
MulterModule.register({
  dest: './upload',
})
```

> info **提示** `MulterModule` 类由 `@nestjs/platform-express` 包导出。

#### 异步配置

当你需要以异步方式（而非静态方式）设置 `MulterModule` 选项时，可以使用 `registerAsync()` 方法。与大多数动态模块（Dynamic Module）一样，Nest 提供了多种处理异步配置的技术手段。

其中一种方式是使用工厂函数：

```typescript
MulterModule.registerAsync({
  useFactory: () => ({
    dest: './upload',
  }),
})
```

与其他[工厂提供者（factory providers）](https://docs.nestjs.com/fundamentals/custom-providers#factory-providers-usefactory)类似，我们的工厂函数可以是 `async`，并且可以通过 `inject` 注入依赖。

```typescript
MulterModule.registerAsync({
  imports: [ConfigModule],
  useFactory: async (configService: ConfigService) => ({
    dest: configService.get<string>('MULTER_DEST'),
  }),
  inject: [ConfigService],
})
```

另外，你也可以通过类而不是工厂函数来配置 `MulterModule`，如下所示：

```typescript
MulterModule.registerAsync({
  useClass: MulterConfigService,
})
```

上述写法会在 `MulterModule` 内部实例化 `MulterConfigService`，并使用它来创建所需的选项对象。需要注意的是，在这个例子中，`MulterConfigService` 必须实现 `MulterOptionsFactory` 接口，如下所示。`MulterModule` 会在所提供类的实例对象上调用 `createMulterOptions()` 方法。

```typescript
@Injectable()
class MulterConfigService implements MulterOptionsFactory {
  createMulterOptions(): MulterModuleOptions {
    return {
      dest: './upload',
    }
  }
}
```

如果你希望复用现有的选项提供者（options provider），而不是在 `MulterModule` 内部创建一个私有副本，可以使用 `useExisting` 语法。

```typescript
MulterModule.registerAsync({
  imports: [ConfigModule],
  useExisting: ConfigService,
})
```

你还可以通过 `registerAsync()` 方法传递所谓的 `extraProviders`。这些提供者会与模块的提供者合并。

```typescript
MulterModule.registerAsync({
  imports: [ConfigModule],
  useClass: ConfigService,
  extraProviders: [MyAdditionalProvider],
})
```

当你希望为工厂函数或类构造函数提供额外依赖时，这种方式非常有用。

#### 示例

你可以在[这里](https://github.com/nestjs/nest/tree/master/sample/29-file-upload)查看一个完整的示例。
