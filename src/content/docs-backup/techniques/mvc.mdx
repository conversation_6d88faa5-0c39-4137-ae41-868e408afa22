### MVC 模式

Nest 默认在底层使用了 [Express](https://github.com/expressjs/express) 库。因此，所有适用于 Express 的 MVC（模型-视图-控制器）模式的用法，同样适用于 Nest。

首先，我们可以使用 [命令行工具（CLI）](https://github.com/nestjs/nest-cli) 脚手架快速创建一个简单的 Nest 应用：

```bash
$ npm i -g @nestjs/cli
$ nest new project
```

为了创建一个 MVC 应用，我们还需要安装一个 [模板引擎（template engine）](https://expressjs.com/en/guide/using-template-engines.html) 来渲染 HTML 视图：

```bash
$ npm install --save hbs
```

这里我们使用了 `hbs`（[Handlebars](https://github.com/pillarjs/hbs#readme)）模板引擎，你也可以根据实际需求选择其他模板引擎。安装完成后，需要通过以下代码配置 Express 实例：

```typescript
@@filename(main)
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(
    AppModule,
  );

  app.useStaticAssets(join(__dirname, '..', 'public'));
  app.setBaseViewsDir(join(__dirname, '..', 'views'));
  app.setViewEngine('hbs');

  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
@@switch
import { NestFactory } from '@nestjs/core';
import { join } from 'path';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(
    AppModule,
  );

  app.useStaticAssets(join(__dirname, '..', 'public'));
  app.setBaseViewsDir(join(__dirname, '..', 'views'));
  app.setViewEngine('hbs');

  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
```

在上述代码中，我们告知 [Express](https://github.com/expressjs/express)（Node.js 框架）：`public` 目录用于存放静态资源，`views` 目录用于存放视图模板，渲染 HTML 输出时使用 `hbs` 模板引擎。

#### 模板渲染

现在，让我们创建一个 `views` 目录，并在其中添加一个 `index.hbs` 模板。在该模板中，我们将输出从控制器传递过来的 `message` ：

```html
<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>App</title>
  </head>
  <body>
    {{ "{{ message }}" }}
  </body>
</html>
```

接下来，打开 `app.controller` 文件，并将 `root()` 方法替换为以下代码：

```typescript
@@filename(app.controller)
import { Get, Controller, Render } from '@nestjs/common';

@Controller()
export class AppController {
  @Get()
  @Render('index')
  root() {
    return { message: 'Hello world!' };
  }
}
```

在上述代码中，我们通过 `@Render()` 装饰器指定了要使用的模板，并且路由处理器方法的返回值会传递给模板进行渲染。请注意，返回值是一个包含 `message` 属性的对象，这与我们在模板中创建的 `message` 占位符相对应。

当应用正在运行时，打开浏览器并访问 `http://localhost:3000`，你应该可以看到 `Hello world!` 消息。

#### 动态模板渲染

如果应用程序逻辑需要动态决定渲染哪个模板，则应使用 `@Res()` 装饰器（Decorator），并在路由处理器中传递视图名称，而不是在 `@Render()` 装饰器中指定：

> info **提示** 当 Nest 检测到 `@Res()` 装饰器时，会注入特定库的响应对象（Response Object）。我们可以使用该对象动态渲染模板。详细了解响应对象 API，请参见 [这里](https://expressjs.com/en/api.html)。

```typescript
@@filename(app.controller)
import { Get, Controller, Res, Render } from '@nestjs/common';
import { Response } from 'express';
import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private appService: AppService) {}

  @Get()
  root(@Res() res: Response) {
    return res.render(
      this.appService.getViewName(),
      { message: 'Hello world!' },
    );
  }
}
```

#### 示例

一个可用的示例项目可在 [这里](https://github.com/nestjs/nest/tree/master/sample/15-mvc) 查看。

#### 在 Fastify 中使用

如本[章节](/techniques/performance)所述，我们可以将任何兼容的 HTTP 提供者（HTTP provider）与 Nest 一起使用。其中一个常用库就是 [Fastify](https://github.com/fastify/fastify)。要使用 Fastify 创建 MVC 应用，需要安装以下依赖包：

```bash
$ npm i --save @fastify/static @fastify/view handlebars
```

接下来的步骤与使用 Express 时几乎一致，只是针对平台有一些细微差别。安装完成后，打开 `main.ts` 文件，并更新其内容如下：

```typescript
@@filename(main)
import { NestFactory } from '@nestjs/core';
import { NestFastifyApplication, FastifyAdapter } from '@nestjs/platform-fastify';
import { AppModule } from './app.module';
import { join } from 'path';

async function bootstrap() {
  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter(),
  );
  app.useStaticAssets({
    root: join(__dirname, '..', 'public'),
    prefix: '/public/',
  });
  app.setViewEngine({
    engine: {
      handlebars: require('handlebars'),
    },
    templates: join(__dirname, '..', 'views'),
  });
  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
@@switch
import { NestFactory } from '@nestjs/core';
import { FastifyAdapter } from '@nestjs/platform-fastify';
import { AppModule } from './app.module';
import { join } from 'path';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, new FastifyAdapter());
  app.useStaticAssets({
    root: join(__dirname, '..', 'public'),
    prefix: '/public/',
  });
  app.setViewEngine({
    engine: {
      handlebars: require('handlebars'),
    },
    templates: join(__dirname, '..', 'views'),
  });
  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
```

Fastify API 与其他平台有一些差异，但这些方法调用的最终效果是一样的。需要注意的是，在使用 Fastify 时，传递给 `@Render()` 装饰器的模板名称必须包含文件扩展名。

你可以这样设置：

```typescript
@@filename(app.controller)
import { Get, Controller, Render } from '@nestjs/common';

@Controller()
export class AppController {
  @Get()
  @Render('index.hbs')
  root() {
    return { message: 'Hello world!' };
  }
}
```

另外，你也可以使用 `@Res()` 装饰器，直接注入响应对象，并指定要渲染的视图，如下所示：

```typescript
import { Res } from '@nestjs/common';
import { FastifyReply } from 'fastify';

@Get()
root(@Res() res: FastifyReply) {
  return res.view('index.hbs', { title: 'Hello world!' });
}
```

当应用正在运行时，打开浏览器并访问 `http://localhost:3000`，你应该可以看到 `Hello world!` 的消息。

#### 示例

一个可用的示例项目可在 [这里](https://github.com/nestjs/nest/tree/master/sample/17-mvc-fastify) 查看。
