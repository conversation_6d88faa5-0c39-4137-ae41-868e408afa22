### 验证（Validation）

在 Web 应用中，验证传入数据的正确性是一项最佳实践。为了自动验证传入请求，Nest 提供了多种开箱即用的管道（Pipe）：

- `验证管道（ValidationPipe）`
- `ParseIntPipe`
- `ParseBoolPipe`
- `ParseArrayPipe`
- `ParseUUIDPipe`

`验证管道（ValidationPipe）` 利用了功能强大的 [class-validator](https://github.com/typestack/class-validator) 包及其声明式验证装饰器。通过 `验证管道`，可以方便地为所有传入客户端的数据强制执行验证规则，这些规则通过在每个模块的本地类或数据传输对象（DTO）声明中以注解方式定义。

#### 概述

在 [管道](/pipes) 章节中，我们已经介绍了如何构建简单的管道，并将其绑定到控制器、方法或全局应用，以演示其工作原理。建议先回顾该章节，以便更好地理解本章内容。本章将聚焦于 `验证管道` 的各种**实际应用场景**，并展示如何使用其高级自定义功能。

#### 使用内置的验证管道（ValidationPipe）

要开始使用验证功能，首先需要安装相关依赖：

```bash
$ npm i --save class-validator class-transformer
```

> info **提示** `验证管道（ValidationPipe）` 从 `@nestjs/common` 包中导出。

由于该管道依赖 [`class-validator`](https://github.com/typestack/class-validator) 和 [`class-transformer`](https://github.com/typestack/class-transformer) 库，因此可用的配置选项非常丰富。你可以通过传递配置对象来设置这些选项。以下是内置的主要选项：

```typescript
export interface ValidationPipeOptions extends ValidatorOptions {
  transform?: boolean
  disableErrorMessages?: boolean
  exceptionFactory?: (errors: ValidationError[]) => any
}
```

此外，还可以使用所有 `class-validator` 的选项（继承自 `ValidatorOptions` 接口）：

<table>
  <tbody>
    <tr>
      <th>选项</th>
      <th>类型</th>
      <th>说明</th>
    </tr>
    <tr>
      <td>
        <code>enableDebugMessages</code>
      </td>
      <td>
        <code>boolean</code>
      </td>
      <td>如果设置为 true，验证器将在出现问题时在控制台输出额外的警告信息。</td>
    </tr>
    <tr>
      <td>
        <code>skipUndefinedProperties</code>
      </td>
      <td>
        <code>boolean</code>
      </td>
      <td>如果设置为 true，则验证器会跳过所有在被验证对象中为 undefined 的属性。</td>
    </tr>
    <tr>
      <td>
        <code>skipNullProperties</code>
      </td>
      <td>
        <code>boolean</code>
      </td>
      <td>如果设置为 true，则验证器会跳过所有在被验证对象中为 null 的属性。</td>
    </tr>
    <tr>
      <td>
        <code>skipMissingProperties</code>
      </td>
      <td>
        <code>boolean</code>
      </td>
      <td>如果设置为 true，则验证器会跳过所有在被验证对象中为 null 或 undefined 的属性。</td>
    </tr>
    <tr>
      <td>
        <code>whitelist</code>
      </td>
      <td>
        <code>boolean</code>
      </td>
      <td>如果设置为 true，验证器会移除所有未使用任何验证装饰器的属性。</td>
    </tr>
    <tr>
      <td>
        <code>forbidNonWhitelisted</code>
      </td>
      <td>
        <code>boolean</code>
      </td>
      <td>如果设置为 true，验证器不会移除未在白名单中的属性，而是直接抛出异常。</td>
    </tr>
    <tr>
      <td>
        <code>forbidUnknownValues</code>
      </td>
      <td>
        <code>boolean</code>
      </td>
      <td>如果设置为 true，尝试验证未知对象时会立即失败。</td>
    </tr>
    <tr>
      <td>
        <code>disableErrorMessages</code>
      </td>
      <td>
        <code>boolean</code>
      </td>
      <td>如果设置为 true，验证错误将不会返回给客户端。</td>
    </tr>
    <tr>
      <td>
        <code>errorHttpStatusCode</code>
      </td>
      <td>
        <code>number</code>
      </td>
      <td>
        该设置允许你指定在发生错误时使用哪种异常类型。默认情况下会抛出{' '}
        <code>BadRequestException</code>。
      </td>
    </tr>
    <tr>
      <td>
        <code>exceptionFactory</code>
      </td>
      <td>
        <code>Function</code>
      </td>
      <td>接收一个包含所有验证错误的数组，并返回要抛出的异常对象。</td>
    </tr>
    <tr>
      <td>
        <code>groups</code>
      </td>
      <td>
        <code>string[]</code>
      </td>
      <td>在验证对象时使用的分组。</td>
    </tr>
    <tr>
      <td>
        <code>always</code>
      </td>
      <td>
        <code>boolean</code>
      </td>
      <td>
        设置装饰器 <code>always</code> 选项的默认值。该默认值可在装饰器选项中被覆盖。
      </td>
    </tr>
    <tr>
      <td>
        <code>strictGroups</code>
      </td>
      <td>
        <code>boolean</code>
      </td>
      <td>
        如果未提供 <code>groups</code> 或其为空，则会忽略所有至少包含一个分组的装饰器。
      </td>
    </tr>
    <tr>
      <td>
        <code>dismissDefaultMessages</code>
      </td>
      <td>
        <code>boolean</code>
      </td>
      <td>
        如果设置为 true，验证时不会使用默认消息。若未显式设置，错误消息始终为 <code>undefined</code>
        。
      </td>
    </tr>
    <tr>
      <td>
        <code>validationError.target</code>
      </td>
      <td>
        <code>boolean</code>
      </td>
      <td>
        指定是否在 <code>ValidationError</code> 中暴露 target。
      </td>
    </tr>
    <tr>
      <td>
        <code>validationError.value</code>
      </td>
      <td>
        <code>boolean</code>
      </td>
      <td>
        指定是否在 <code>ValidationError</code> 中暴露被验证的值。
      </td>
    </tr>
    <tr>
      <td>
        <code>stopAtFirstError</code>
      </td>
      <td>
        <code>boolean</code>
      </td>
      <td>如果设置为 true，验证某个属性时遇到第一个错误后将停止验证。默认为 false。</td>
    </tr>
  </tbody>
</table>

> info **Notice** Find more information about the `class-validator` package in its [repository](https://github.com/typestack/class-validator).

#### 自动验证（Auto-validation）

我们首先将在应用程序级别绑定验证管道（ValidationPipe），这样可以确保所有端点都能防止接收不正确的数据。

```typescript
async function bootstrap() {
  const app = await NestFactory.create(AppModule)
  app.useGlobalPipes(new ValidationPipe())
  await app.listen(process.env.PORT ?? 3000)
}
bootstrap()
```

为了测试我们的验证管道，让我们创建一个基础的端点。

```typescript
@Post()
create(@Body() createUserDto: CreateUserDto) {
  return 'This action adds a new user';
}
```

> info **提示** 由于 TypeScript 不会存储泛型或接口的元数据，当你在数据传输对象（DTO，Data Transfer Object）中使用它们时，验证管道（ValidationPipe）可能无法正确验证传入数据。因此，建议在 DTO 中使用具体的类。

> info **提示** 在导入你的数据传输对象（DTO）时，不能只使用 type-only 导入，因为这类导入在运行时会被移除。也就是说，请记得使用 `import { CreateUserDto }`，而不是 `import type { CreateUserDto }`。

现在我们可以在 `CreateUserDto` 中添加一些验证规则。这些规则是通过 `class-validator` 包提供的装饰器（Decorator）来实现的，详细说明可参考 [这里](https://github.com/typestack/class-validator#validation-decorators)。通过这种方式，任何使用 `CreateUserDto` 的路由都会自动强制执行这些验证规则。

```typescript
import { IsEmail, IsNotEmpty } from 'class-validator'

export class CreateUserDto {
  @IsEmail()
  email: string

  @IsNotEmpty()
  password: string
}
```

有了这些规则后，如果请求体中的 `email` 属性无效，请求到达端点时，应用会自动返回 `400 Bad Request` 状态码，并附带如下响应体：

```json
{
  "statusCode": 400,
  "error": "Bad Request",
  "message": ["email must be an email"]
}
```

除了可以验证请求体外，验证管道（ValidationPipe）还可以用于其他请求对象（Request Object）属性。例如，假设我们希望在端点路径中接收 `:id` 参数。为了确保该请求参数只能为数字，可以使用如下方式：

```typescript
@Get(':id')
findOne(@Param() params: FindOneParams) {
  return 'This action returns a user';
}
```

`FindOneParams` 类似于数据传输对象（DTO），它也是一个通过 `class-validator` 定义验证规则的类。示例如下：

```typescript
import { IsNumberString } from 'class-validator'

export class FindOneParams {
  @IsNumberString()
  id: string
}
```

#### 关闭详细错误信息

错误信息有助于解释请求中出现了哪些问题。然而，在某些生产环境中，通常会选择关闭详细错误信息。你可以通过向 `验证管道（ValidationPipe）` 传递一个选项对象来实现：

```typescript
app.useGlobalPipes(
  new ValidationPipe({
    disableErrorMessages: true,
  })
)
```

这样一来，响应体中将不会显示详细的错误信息。

#### 属性剥离

我们的 `验证管道（ValidationPipe）` 还可以过滤掉那些不应被方法处理器接收的属性。在这种情况下，我们可以**白名单**（whitelist）允许的属性，任何未包含在白名单中的属性都会自动从结果对象中移除。例如，如果处理器期望接收 `email` 和 `password` 属性，但请求中还包含了 `age` 属性，那么该属性会被自动从最终的数据传输对象（DTO）中移除。要启用此行为，只需将 `whitelist` 选项设置为 `true`：

```typescript
app.useGlobalPipes(
  new ValidationPipe({
    whitelist: true,
  })
)
```

当设置为 true 时，所有未在验证类中使用装饰器标记的非白名单属性都会被自动移除。

另外，你也可以选择在请求包含非白名单属性时，直接阻止请求继续处理，并向用户返回错误响应。要启用此功能，需要将 `forbidNonWhitelisted` 选项属性设置为 `true`，并同时启用 `whitelist`。

<app-banner-courses></app-banner-courses>

#### 转换请求载荷对象

通过网络传入的请求载荷通常是普通的 JavaScript 对象。`验证管道（ValidationPipe）` 可以自动将这些载荷转换为根据其数据传输对象（Data Transfer Object，DTO）类类型化的对象。要启用自动转换功能，只需将 `transform` 选项设置为 `true`。你可以在方法级别进行设置：

```typescript
@@filename(cats.controller)
@Post()
@UsePipes(new ValidationPipe({ transform: true }))
async create(@Body() createCatDto: CreateCatDto) {
  this.catsService.create(createCatDto);
}
```

如果希望全局启用此行为，可以在全局管道中设置该选项：

```typescript
app.useGlobalPipes(
  new ValidationPipe({
    transform: true,
  })
)
```

启用自动转换选项后，`验证管道（ValidationPipe）` 还会对原始类型（primitive types）进行类型转换。在下方的示例中，`findOne()` 方法接收一个参数，该参数表示提取出来的 `id` 路径参数（Path Parameter）：

```typescript
@Get(':id')
findOne(@Param('id') id: number) {
  console.log(typeof id === 'number'); // true
  return 'This action returns a user';
}
```

默认情况下，所有路径参数和查询参数（Query Parameter）通过网络传递时都是 `string` 类型。在上述示例中，我们在方法签名中将 `id` 的类型指定为 `number`。因此，`验证管道（ValidationPipe）` 会尝试自动将字符串标识符转换为数字。

#### 显式类型转换

在上面的部分，我们展示了 `验证管道（ValidationPipe）` 如何根据期望类型隐式转换查询参数和路径参数。但需要注意，这一特性依赖于自动转换功能已启用。

另外（如果未启用自动转换），你也可以通过显式方式进行类型转换，比如使用 `ParseIntPipe` 或 `ParseBoolPipe`（无需使用 `ParseStringPipe`，因为如前所述，所有路径参数和查询参数默认都是 `string` 类型）。

```typescript
@Get(':id')
findOne(
  @Param('id', ParseIntPipe) id: number,
  @Query('sort', ParseBoolPipe) sort: boolean,
) {
  console.log(typeof id === 'number'); // true
  console.log(typeof sort === 'boolean'); // true
  return 'This action returns a user';
}
```

> info **提示** `ParseIntPipe` 和 `ParseBoolPipe` 都由 `@nestjs/common` 包导出。

#### 映射类型

在开发 **CRUD**（创建/读取/更新/删除）等功能时，通常需要基于某个基础实体类型构建不同的变体。Nest 提供了一些实用函数，可以对类型进行转换，帮助你更方便地完成这项工作。

> **警告** 如果你的应用使用了 `@nestjs/swagger` 包，请参阅[本章节](/openapi/mapped-types)以获取关于映射类型的更多信息。同样地，如果你使用了 `@nestjs/graphql` 包，请参阅[本章节](/graphql/mapped-types)。这两个包都大量依赖类型系统，因此需要使用不同的导入方式。如果你使用了 `@nestjs/mapped-types`（而不是根据应用类型选择 `@nestjs/swagger` 或 `@nestjs/graphql`），可能会遇到各种未记录的副作用。

在构建输入验证类型（也称为数据传输对象（Data Transfer Object，DTO））时，通常需要基于同一个类型分别创建 **create** 和 **update** 版本。例如，**create** 版本可能要求所有字段都是必填的，而 **update** 版本则可以将所有字段设为可选。

Nest 提供了 `PartialType()` 工具函数，帮助你更轻松地完成这项工作，并减少模板代码（Boilerplate）。

`PartialType()` 函数会返回一个类型（类），其所有属性都变为可选。例如，假设我们有如下的 **create** 类型：

```typescript
export class CreateCatDto {
  name: string
  age: number
  breed: string
}
```

默认情况下，这些字段都是必填的。要创建一个拥有相同字段但全部为可选的新类型，可以使用 `PartialType()`，并将类引用（`CreateCatDto`）作为参数传入：

```typescript
export class UpdateCatDto extends PartialType(CreateCatDto) {}
```

> info **提示** `PartialType()` 函数需从 `@nestjs/mapped-types` 包中导入。

`PickType()` 函数可以从输入类型中挑选一组属性，构造出一个新的类型（类）。例如，假设我们有如下类型：

```typescript
export class CreateCatDto {
  name: string
  age: number
  breed: string
}
```

我们可以使用 `PickType()` 工具函数，从该类中挑选部分属性：

```typescript
export class UpdateCatAgeDto extends PickType(CreateCatDto, ['age'] as const) {}
```

> info **提示** `PickType()` 函数需从 `@nestjs/mapped-types` 包中导入。

`OmitType()` 函数会从输入类型中挑选所有属性，然后移除指定的键，构造出一个新类型。例如，假设我们有如下类型：

```typescript
export class CreateCatDto {
  name: string
  age: number
  breed: string
}
```

我们可以生成一个派生类型，包含除了 `name` 以外的所有属性，如下所示。在这个用法中，`OmitType` 的第二个参数是属性名数组。

```typescript
export class UpdateCatDto extends OmitType(CreateCatDto, ['name'] as const) {}
```

> info **提示** `OmitType()` 函数需从 `@nestjs/mapped-types` 包中导入。

`IntersectionType()` 函数可以将两个类型合并为一个新类型（类）。例如，假设我们有如下两个类型：

```typescript
export class CreateCatDto {
  name: string
  breed: string
}

export class AdditionalCatInfo {
  color: string
}
```

我们可以生成一个新类型，包含两个类型中的所有属性。

```typescript
export class UpdateCatDto extends IntersectionType(CreateCatDto, AdditionalCatInfo) {}
```

> info **提示** `IntersectionType()` 函数需从 `@nestjs/mapped-types` 包中导入。

这些类型映射工具函数是可以组合使用的。例如，下面的写法会生成一个类型（类），它拥有 `CreateCatDto` 类型中除了 `name` 以外的所有属性，并且这些属性都是可选的：

```typescript
export class UpdateCatDto extends PartialType(OmitType(CreateCatDto, ['name'] as const)) {}
```

#### 解析与验证数组

TypeScript 不会存储关于泛型或接口的元数据，因此当你在数据传输对象（DTO）中使用它们时，验证管道（ValidationPipe）可能无法正确验证传入的数据。例如，在下面的代码中，`createUserDtos` 无法被正确验证：

```typescript
@Post()
createBulk(@Body() createUserDtos: CreateUserDto[]) {
  return 'This action adds new users';
}
```

要对数组进行验证，可以创建一个专门的类，其中包含一个用于包裹数组的属性，或者直接使用 `ParseArrayPipe` 管道：

```typescript
@Post()
createBulk(
  @Body(new ParseArrayPipe({ items: CreateUserDto }))
  createUserDtos: CreateUserDto[],
) {
  return 'This action adds new users';
}
```

此外，`ParseArrayPipe` 在解析查询参数时也非常有用。比如我们有一个 `findByIds()` 方法，用于根据作为查询参数传递的标识符返回用户：

```typescript
@Get()
findByIds(
  @Query('ids', new ParseArrayPipe({ items: Number, separator: ',' }))
  ids: number[],
) {
  return 'This action returns users by ids';
}
```

上述写法可以验证来自 HTTP `GET` 请求的查询参数，例如：

```bash
GET /?ids=1,2,3
```

#### WebSocket 通信和微服务

虽然本章主要以 HTTP 风格的应用（如 Express 或 Fastify）为例进行讲解，但无论使用哪种传输方式，`验证管道（ValidationPipe）` 在 WebSocket 通信和微服务中同样适用。

#### 了解更多

想要进一步了解自定义验证器、错误消息以及 `class-validator` 包中可用的装饰器，请查阅 [相关文档](https://github.com/typestack/class-validator)。
