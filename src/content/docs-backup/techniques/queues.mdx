### 队列（Queues）

队列（Queues）是一种强大的设计模式，能够帮助你应对常见的应用扩展性和性能挑战。以下是队列可以帮助你解决的一些典型问题：

- 平滑处理高峰期任务。例如，如果用户可以在任意时间发起资源密集型任务，你可以将这些任务添加到队列中，而不是同步执行。随后，工作进程（worker processes）可以以受控的方式从队列中拉取任务进行处理。随着应用规模的扩大，你可以轻松增加新的队列消费者（Queue consumers），以提升后端任务处理能力。
- 拆分可能阻塞 Node.js 事件循环的单体任务。例如，如果某个用户请求需要进行音频转码等 CPU 密集型操作，你可以将该任务委托给其他进程处理，从而让面向用户的进程保持响应性。
- 为不同服务之间提供可靠的通信通道。例如，你可以在一个进程或服务中将任务（作业，job）加入队列，在另一个进程或服务中消费这些任务。你可以通过监听状态事件（status events）来获知作业生命周期（job life cycle）中的完成、错误或其他状态变化。当队列生产者（Queue producers）或消费者（consumers）发生故障时，其状态会被保留，节点重启后任务处理可以自动恢复。

Nest 提供了 `@nestjs/bullmq` 包用于集成 BullMQ，以及 `@nestjs/bull` 包用于集成 Bull。这两个包都是在各自底层库之上开发的抽象/封装，由同一团队维护。Bull 目前处于维护模式，团队主要专注于修复 bug，而 BullMQ 正在积极开发，采用了现代 TypeScript 实现，并具备不同的功能特性。如果 Bull 能满足你的需求，它依然是一个可靠且经过实战考验的选择。Nest 的这两个包让你可以非常方便地将 BullMQ 或 Bull 队列集成到 Nest 应用中。

BullMQ 和 Bull 都使用 [Redis](https://redis.io/) 来持久化作业数据，因此你需要在系统上安装 Redis。由于它们基于 Redis，你的队列架构可以完全分布式且与平台无关。例如，你可以在一个（或多个）节点上运行一些队列<a href="techniques/queues#producers">生产者</a>、<a href="techniques/queues#consumers">消费者</a>和<a href="techniques/queues#event-listeners">监听器</a>，在其他网络节点上的其他 Node.js 平台上运行更多的生产者、消费者和监听器。

本章将介绍 `@nestjs/bullmq` 和 `@nestjs/bull` 这两个包。我们也推荐你阅读 [BullMQ](https://docs.bullmq.io/) 和 [Bull](https://github.com/OptimalBits/bull/blob/master/REFERENCE.md) 的官方文档，以获取更多背景信息和具体实现细节。

#### BullMQ 安装

要开始使用 BullMQ，我们首先需要安装所需的依赖包。

```bash
$ npm install --save @nestjs/bullmq bullmq
```

安装完成后，我们可以将 `BullModule` 导入到根模块 `AppModule` 中。

```typescript
@@filename(app.module)
import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';

@Module({
  imports: [
    BullModule.forRoot({
      connection: {
        host: 'localhost',
        port: 6379,
      },
    }),
  ],
})
export class AppModule {}
```

`forRoot()` 方法用于注册一个 `bullmq` 包的配置对象，该对象将被应用中注册的所有队列（Queue）使用（除非另有指定）。以下是配置对象中的部分属性，供参考：

- `connection: ConnectionOptions` - 用于配置 Redis 连接的选项。详见 [连接配置](https://docs.bullmq.io/guide/connections)。可选。
- `prefix: string` - 所有队列键的前缀。可选。
- `defaultJobOptions: JobOpts` - 控制新作业默认设置的选项。详见 [JobOpts](https://github.com/OptimalBits/bull/blob/master/REFERENCE.md#queueadd)。可选。
- `settings: AdvancedSettings` - 队列高级配置选项。通常无需更改。详见 [AdvancedSettings](https://github.com/OptimalBits/bull/blob/master/REFERENCE.md#queue)。可选。
- `extraOptions` - 模块初始化的额外选项。详见 [手动注册](https://docs.nestjs.com/techniques/queues#manual-registration)

所有选项均为可选，提供了对队列行为的详细控制。这些选项会直接传递给 BullMQ 的 `Queue` 构造函数。关于这些选项及更多内容，详见 [官方文档](https://api.docs.bullmq.io/interfaces/v4.QueueOptions.html)。

要注册一个队列（Queue），可以如下导入 `BullModule.registerQueue()` 动态模块：

```typescript
BullModule.registerQueue({
  name: 'audio',
})
```

> info **提示**
> 通过向 `registerQueue()` 方法传递多个用逗号分隔的配置对象，可以创建多个队列。

`registerQueue()` 方法用于实例化和/或注册队列。队列会在连接到同一个 Redis 数据库且凭据相同的模块和进程间共享。每个队列通过其 name 属性唯一标识。队列名称既用作注入令牌（用于将队列注入到控制器或提供者中），也作为装饰器参数，将消费者类和监听器与队列关联。

你还可以为特定队列覆盖部分预配置选项，例如：

```typescript
BullModule.registerQueue({
  name: 'audio',
  connection: {
    port: 6380,
  },
})
```

BullMQ 还支持作业（Job）之间的父子关系。这一功能使你可以创建作业流（Flow），即作业可以组成任意深度的树状结构。详细内容请参见 [官方文档](https://docs.bullmq.io/guide/flows)。

要添加作业流，可以这样做：

```typescript
BullModule.registerFlowProducer({
  name: 'flowProducerName',
})
```

由于作业会持久化在 Redis 中，每当某个特定名称的队列被实例化（例如应用启动或重启时），它会尝试处理之前未完成会话中遗留的旧作业。

每个队列可以有一个或多个生产者、消费者和监听器。消费者会以特定顺序（默认 FIFO，先进先出；也支持 LIFO 或按优先级）从队列中获取作业。如何控制队列处理顺序，请参见<a href="techniques/queues#consumers">此处</a>。

<app-banner-enterprise></app-banner-enterprise>

#### 命名配置

如果你的队列需要连接多个不同的 Redis 实例，可以使用一种称为**命名配置（named configurations）**的技术。该特性允许你以指定的键注册多个配置，然后在队列选项中引用这些配置。

例如，假设你的应用中有部分队列需要使用除默认 Redis 实例之外的其他 Redis 实例，你可以如下注册该实例的配置：

```typescript
BullModule.forRoot('alternative-config', {
  connection: {
    port: 6381,
  },
})
```

在上面的示例中，`'alternative-config'` 只是一个配置键（可以是任意字符串）。

完成上述配置后，你现在可以在 `registerQueue()` 的选项对象中通过 `configKey` 指定该配置：

```typescript
BullModule.registerQueue({
  configKey: 'alternative-config',
  name: 'video',
})
```

#### 生产者（Producers）

作业生产者负责向队列添加作业。生产者通常是应用服务（Nest 提供者（Provider））。要向队列添加作业，首先需要像下面这样在服务中注入队列：

```typescript
import { Injectable } from '@nestjs/common'
import { Queue } from 'bullmq'
import { InjectQueue } from '@nestjs/bullmq'

@Injectable()
export class AudioService {
  constructor(@InjectQueue('audio') private audioQueue: Queue) {}
}
```

> info **提示** `@InjectQueue()` 装饰器通过队列名称（即在 `registerQueue()` 方法调用中提供的名称，例如 `'audio'`）来标识队列。

接下来，可以通过调用队列的 `add()` 方法并传入自定义的作业对象来添加作业。作业以可序列化的 JavaScript 对象形式表示（因为它们会被存储在 Redis 数据库中）。你可以根据业务语义自定义作业对象的结构。同时，还需要为作业指定一个名称。这样可以创建专门的[消费者](/techniques/queues#consumers)，仅处理具有特定名称的作业。

```typescript
const job = await this.audioQueue.add('transcode', {
  foo: 'bar',
})
```

#### 任务选项

任务（Job）可以携带额外的选项。你可以在 `Queue.add()` 方法中，将一个选项对象作为 `job` 参数之后的参数传入。常用的任务选项属性包括：

- `priority`：`number` - （可选）优先级数值。范围从 1（最高优先级）到 MAX_INT（最低优先级）。注意，使用优先级会对性能产生一定影响，因此请谨慎使用。
- `delay`：`number` - 等待一段时间（以毫秒为单位）后再处理该任务。注意，为了保证延迟的准确性，服务器和客户端的时钟应保持同步。
- `attempts`：`number` - 在任务完成前，最多尝试执行的次数。
- `repeat`：`RepeatOpts` - 按照 cron 规则重复执行任务。详见 [RepeatOpts](https://github.com/OptimalBits/bull/blob/master/REFERENCE.md#queueadd)。
- `backoff`：`number | BackoffOpts` - 任务失败时自动重试的退避（Backoff）设置。详见 [BackoffOpts](https://github.com/OptimalBits/bull/blob/master/REFERENCE.md#queueadd)。
- `lifo`：`boolean` - 如果为 true，则将任务添加到队列的右端（即后进先出 LIFO），默认为 false。
- `jobId`：`number` | `string` - 覆盖任务 ID。默认情况下，任务 ID 是唯一的整数，但你可以通过此设置自定义。如果你使用该选项，需要确保 jobId 的唯一性。如果尝试添加已存在 ID 的任务，则不会被添加。
- `removeOnComplete`：`boolean | number` - 如果为 true，任务成功完成后会被移除。若为数字，则表示保留的已完成任务数量。默认行为是保留任务在已完成集合中。
- `removeOnFail`：`boolean | number` - 如果为 true，任务在所有尝试失败后会被移除。若为数字，则表示保留的失败任务数量。默认行为是保留任务在失败集合中。
- `stackTraceLimit`：`number` - 限制记录在堆栈跟踪中的行数。

下面是一些自定义任务选项的示例。

要延迟任务的开始，可以使用 `delay` 配置属性。

```typescript
const job = await this.audioQueue.add(
  'transcode',
  {
    foo: 'bar',
  },
  { delay: 3000 } // 延迟 3 秒执行
)
```

如果希望将任务添加到队列的右端（以**后进先出（LIFO）**方式处理任务），可以将配置对象的 `lifo` 属性设置为 `true`。

```typescript
const job = await this.audioQueue.add(
  'transcode',
  {
    foo: 'bar',
  },
  { lifo: true }
)
```

要设置任务优先级，可以使用 `priority` 属性。

```typescript
const job = await this.audioQueue.add(
  'transcode',
  {
    foo: 'bar',
  },
  { priority: 2 }
)
```

完整的选项列表请参考 API 文档：[这里](https://api.docs.bullmq.io/types/v4.JobsOptions.html) 和 [这里](https://api.docs.bullmq.io/interfaces/v4.BaseJobOptions.html)。

#### 消费者（Consumer）

消费者是一个**类**，用于定义处理添加到队列中的任务（job）的方法，或监听队列上的事件，或两者兼而有之。可以使用 `@Processor()` 装饰器声明一个消费者类，示例如下：

```typescript
import { Processor } from '@nestjs/bullmq'

@Processor('audio')
export class AudioConsumer {}
```

> info **提示** 消费者必须作为 `提供者（Provider）` 注册，这样 `@nestjs/bullmq` 包才能正确识别。

装饰器的字符串参数（如 `'audio'`）表示要将该类方法关联到的队列名称。

```typescript
import { Processor, WorkerHost } from '@nestjs/bullmq'
import { Job } from 'bullmq'

@Processor('audio')
export class AudioConsumer extends WorkerHost {
  async process(job: Job<any, any, string>): Promise<any> {
    let progress = 0
    for (let i = 0; i < 100; i++) {
      await doSomething(job.data)
      progress += 1
      await job.updateProgress(progress)
    }
    return {}
  }
}
```

当 worker 处于空闲状态且队列中有任务需要处理时，`process` 方法会被调用。该处理方法接收 `job` 对象作为唯一参数。处理方法返回的值会被存储在任务对象中，之后可以在监听任务完成事件时访问。

`Job` 对象提供了多种方法用于与其状态进行交互。例如，上述代码中使用了 `progress()` 方法来更新任务的进度。完整的 `Job` 对象 API 参考请见 [这里](https://api.docs.bullmq.io/classes/v4.Job.html)。

在旧版本 Bull 中，可以通过为 `@Process()` 装饰器传递特定 `name`，指定某个处理方法**仅处理**特定类型（具有特定 `name`）的任务，如下所示：

> warning **警告** 该用法在 BullMQ 中已不再支持，详情请继续阅读。

```typescript
@Process('transcode')
async transcode(job: Job<unknown>) { ... }
```

由于该行为在 BullMQ 中容易引起混淆，因此不再支持。现在，你需要通过 switch 语句，根据不同的任务名称调用不同的服务或逻辑：

```typescript
import { Processor, WorkerHost } from '@nestjs/bullmq'
import { Job } from 'bullmq'

@Processor('audio')
export class AudioConsumer extends WorkerHost {
  async process(job: Job<any, any, string>): Promise<any> {
    switch (job.name) {
      case 'transcode': {
        let progress = 0
        for (i = 0; i < 100; i++) {
          await doSomething(job.data)
          progress += 1
          await job.progress(progress)
        }
        return {}
      }
      case 'concatenate': {
        await doSomeLogic2()
        break
      }
    }
  }
}
```

相关内容可参考 BullMQ 文档的 [named processor](https://docs.bullmq.io/patterns/named-processor) 部分。

#### 请求作用域消费者

当消费者被标记为请求作用域（关于注入作用域的更多信息可参考[此处](/fundamentals/injection-scopes#provider-scope)），每个作业（job）都会专属创建该类的新实例。作业完成后，该实例会被垃圾回收。

```typescript
@Processor({
  name: 'audio',
  scope: Scope.REQUEST,
})
```

由于请求作用域的消费者类是动态实例化且仅作用于单个作业，因此你可以通过标准方式在构造函数中注入 `JOB_REF`。

```typescript
constructor(@Inject(JOB_REF) jobRef: Job) {
  console.log(jobRef);
}
```

> info **提示** `JOB_REF` 令牌需从 `@nestjs/bullmq` 包中导入。

#### 事件监听器

当队列或作业状态发生变化时，BullMQ 会生成一系列有用的事件。这些事件可以在 Worker 层通过 `@OnWorkerEvent(event)` 装饰器进行订阅，或在 Queue 层通过专用监听器类和 `@OnQueueEvent(event)` 装饰器进行订阅。

Worker 事件必须在<a href="techniques/queues#consumers">消费者</a>类（即带有 `@Processor()` 装饰器的类）中声明。要监听某个事件，可使用 `@OnWorkerEvent(event)` 装饰器并指定需要处理的事件。例如，若要监听 `audio` 队列中作业进入 active 状态时触发的事件，可参考如下写法：

```typescript
import { Processor, Process, OnWorkerEvent } from '@nestjs/bullmq'
import { Job } from 'bullmq'

@Processor('audio')
export class AudioConsumer {
  @OnWorkerEvent('active')
  onActive(job: Job) {
    console.log(`正在处理作业 ${job.id}，类型为 ${job.name}，数据为 ${job.data}...`)
  }

  // ...
}
```

你可以在 WorkerListener 的[官方文档](https://api.docs.bullmq.io/interfaces/v4.WorkerListener.html)中查看完整的事件列表及其参数。

QueueEvent 监听器需使用 `@QueueEventsListener(queue)` 装饰器，并继承 `@nestjs/bullmq` 提供的 `QueueEventsHost` 类。要监听某个事件，可使用 `@OnQueueEvent(event)` 装饰器并指定需要处理的事件。例如，若要监听 `audio` 队列中作业进入 active 状态时触发的事件，可参考如下写法：

```typescript
import { QueueEventsHost, QueueEventsListener, OnQueueEvent } from '@nestjs/bullmq'

@QueueEventsListener('audio')
export class AudioEventsListener extends QueueEventsHost {
  @OnQueueEvent('active')
  onActive(job: { jobId: string; prev?: string }) {
    console.log(`正在处理作业 ${job.jobId}...`)
  }

  // ...
}
```

> info **提示** QueueEvent 监听器必须作为 `提供者（provider）` 注册，这样 `@nestjs/bullmq` 包才能正确识别。

你可以在 QueueEventsListener 的[官方文档](https://api.docs.bullmq.io/interfaces/v4.QueueEventsListener.html)中查看完整的事件列表及其参数。

#### 队列管理

队列（Queue）提供了 API，允许你执行诸如暂停、恢复、获取不同状态任务数量等管理操作。完整的队列 API 可参考 [这里](https://api.docs.bullmq.io/classes/v4.Queue.html)。你可以直接在 `Queue` 对象上调用这些方法，下面以暂停和恢复队列为例进行说明。

通过调用 `pause()` 方法可以暂停队列。被暂停的队列不会处理新的任务，但当前正在处理的任务会继续执行直到完成。

```typescript
await audioQueue.pause()
```

要恢复已暂停的队列，可以使用 `resume()` 方法，如下所示：

```typescript
await audioQueue.resume()
```

#### 独立进程处理

任务处理器（Job handler）也可以在独立（fork 出的）进程中运行（[参考来源](https://docs.bullmq.io/guide/workers/sandboxed-processors)）。这样做有以下几个优点：

- 进程是沙箱环境，如果崩溃不会影响主 worker。
- 可以运行阻塞代码而不影响队列（任务不会卡住）。
- 能更好地利用多核 CPU。
- 对 Redis 的连接数更少。

```typescript
@@filename(app.module)
import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { join } from 'path';

@Module({
  imports: [
    BullModule.registerQueue({
      name: 'audio',
      processors: [join(__dirname, 'processor.js')],
    }),
  ],
})
export class AppModule {}
```

> warning **警告** 请注意，由于你的函数会在独立进程中执行，依赖注入（Dependency Injection）和 IoC 容器将不可用。这意味着你的处理器函数需要自行包含（或创建）所需的所有外部依赖实例。

#### 异步配置

有时你可能希望以异步方式传递 `bullmq` 选项，而不是静态配置。此时，可以使用 `forRootAsync()` 方法，该方法提供了多种处理异步配置的方式。同样地，如果你希望以异步方式传递队列选项，可以使用 `registerQueueAsync()` 方法。

其中一种方式是使用工厂函数：

```typescript
BullModule.forRootAsync({
  useFactory: () => ({
    connection: {
      host: 'localhost',
      port: 6379,
    },
  }),
})
```

我们的工厂函数行为与其他[异步提供者](https://docs.nestjs.com/fundamentals/async-providers)类似（例如，它可以是 `async`，并且能够通过 `inject` 注入依赖）。

```typescript
BullModule.forRootAsync({
  imports: [ConfigModule],
  useFactory: async (configService: ConfigService) => ({
    connection: {
      host: configService.get('QUEUE_HOST'),
      port: configService.get('QUEUE_PORT'),
    },
  }),
  inject: [ConfigService],
})
```

另外，你也可以使用 `useClass` 语法：

```typescript
BullModule.forRootAsync({
  useClass: BullConfigService,
})
```

上述写法会在 `BullModule` 内部实例化 `BullConfigService`，并通过调用 `createSharedConfiguration()` 方法来提供配置对象。需要注意的是，这意味着 `BullConfigService` 必须实现 `SharedBullConfigurationFactory` 接口，如下所示：

```typescript
@Injectable()
class BullConfigService implements SharedBullConfigurationFactory {
  createSharedConfiguration(): BullModuleOptions {
    return {
      connection: {
        host: 'localhost',
        port: 6379,
      },
    }
  }
}
```

如果你希望避免在 `BullModule` 内部创建 `BullConfigService` 实例，而是复用其他模块中已导入的提供者，可以使用 `useExisting` 语法。

```typescript
BullModule.forRootAsync({
  imports: [ConfigModule],
  useExisting: ConfigService,
})
```

这种写法与 `useClass` 类似，但有一个关键区别 —— `BullModule` 会查找已导入模块，复用现有的 `ConfigService`，而不是新建一个实例。

同样地，如果你希望以异步方式传递队列选项，可以使用 `registerQueueAsync()` 方法，只需注意将 `name` 属性放在工厂函数外部。

```typescript
BullModule.registerQueueAsync({
  name: 'audio',
  useFactory: () => ({
    redis: {
      host: 'localhost',
      port: 6379,
    },
  }),
})
```

#### 手动注册

默认情况下，`BullModule` 会在 `onModuleInit` 生命周期钩子（Lifecycle Hook）中自动注册 BullMQ 组件（队列、处理器和事件监听服务）。但在某些场景下，这种自动注册行为可能并不理想。若需禁止自动注册，可以在 `BullModule` 中启用 `manualRegistration` 选项，示例如下：

```typescript
BullModule.forRoot({
  extraOptions: {
    manualRegistration: true,
  },
})
```

如需手动注册这些组件，可注入 `BullRegistrar`，并在合适的生命周期钩子（如 `OnModuleInit` 或 `OnApplicationBootstrap`）中调用其 `register` 方法。

```typescript
import { Injectable, OnModuleInit } from '@nestjs/common'
import { BullRegistrar } from '@nestjs/bullmq'

@Injectable()
export class AudioService implements OnModuleInit {
  constructor(private bullRegistrar: BullRegistrar) {}

  onModuleInit() {
    if (yourConditionHere) {
      this.bullRegistrar.register()
    }
  }
}
```

只有在调用了 `BullRegistrar#register` 方法后，BullMQ 组件才会生效 —— 否则不会有任何任务被处理。

#### 安装 Bull

> warning **注意** 如果你决定使用 BullMQ，请跳过本节及后续相关章节。

要开始使用 Bull，首先需要安装相关依赖包。

```bash
$ npm install --save @nestjs/bull bull
```

安装完成后，我们可以在根模块 `AppModule` 中导入 `BullModule`。

```typescript
@@filename(app.module)
import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';

@Module({
  imports: [
    BullModule.forRoot({
      redis: {
        host: 'localhost',
        port: 6379,
      },
    }),
  ],
})
export class AppModule {}
```

`forRoot()` 方法用于注册一个 `bull` 包的配置对象，该对象会被应用中注册的所有队列（除非单独指定）所使用。配置对象包含以下属性：

- `limiter: RateLimiter` - 控制队列任务处理速率的选项。详见 [RateLimiter](https://github.com/OptimalBits/bull/blob/master/REFERENCE.md#queue)。可选。
- `redis: RedisOpts` - 配置 Redis 连接的选项。详见 [RedisOpts](https://github.com/OptimalBits/bull/blob/master/REFERENCE.md#queue)。可选。
- `prefix: string` - 所有队列键的前缀。可选。
- `defaultJobOptions: JobOpts` - 控制新任务默认设置的选项。详见 [JobOpts](https://github.com/OptimalBits/bull/blob/master/REFERENCE.md#queueadd)。可选。**注意：如果你通过 FlowProducer 调度任务，这些设置不会生效。详见 [bullmq#1034](https://github.com/taskforcesh/bullmq/issues/1034) 说明。**
- `settings: AdvancedSettings` - 队列高级配置选项。通常无需更改。详见 [AdvancedSettings](https://github.com/OptimalBits/bull/blob/master/REFERENCE.md#queue)。可选。

所有选项均为可选项，提供了对队列行为的详细控制。这些选项会直接传递给 Bull 的 `Queue` 构造函数。更多选项说明请参考 [这里](https://github.com/OptimalBits/bull/blob/master/REFERENCE.md#queue)。

要注册队列，可以如下导入 `BullModule.registerQueue()` 动态模块：

```typescript
BullModule.registerQueue({
  name: 'audio',
})
```

> info **提示** 通过向 `registerQueue()` 方法传递多个逗号分隔的配置对象，可以创建多个队列。

`registerQueue()` 方法用于实例化和/或注册队列。队列会在连接到同一个 Redis 数据库且凭据相同的模块和进程间共享。每个队列通过其 name 属性唯一标识。队列名称既用作注入令牌（用于在控制器/提供者中注入队列），也作为装饰器参数，将消费者类和监听器与队列关联。

你还可以为特定队列覆盖部分预配置选项，例如：

```typescript
BullModule.registerQueue({
  name: 'audio',
  redis: {
    port: 6380,
  },
})
```

由于任务会持久化在 Redis 中，每当某个特定名称的队列被实例化（例如应用启动/重启时），它会尝试处理前一次未完成会话中遗留的旧任务。

每个队列可以有一个或多个生产者、消费者和监听器。消费者会以特定顺序（默认 FIFO，也可为 LIFO 或按优先级）从队列中获取任务。如何控制队列处理顺序将在<a href="techniques/queues#consumers">此处</a>详细讨论。

<app-banner-enterprise></app-banner-enterprise>

#### 命名配置

如果你的队列需要连接多个 Redis 实例，可以使用一种称为**命名配置（named configurations）**的技术。该特性允许你以指定的键注册多个配置，然后可以在队列选项中引用这些配置。

例如，假设你的应用中有一个额外的 Redis 实例（除了默认实例之外），并且有部分队列需要使用这个实例，你可以按如下方式注册其配置：

```typescript
BullModule.forRoot('alternative-config', {
  redis: {
    port: 6381,
  },
})
```

在上面的示例中，`'alternative-config'` 只是一个配置键（可以是任意字符串）。

完成上述配置后，你现在可以在 `registerQueue()` 的选项对象中通过 `configKey` 指定该配置：

```typescript
BullModule.registerQueue({
  configKey: 'alternative-config',
  name: 'video',
})
```

#### 生产者（Producers）

任务生产者（Job producers）会向队列中添加任务。生产者通常是应用服务（Nest 提供者（Provider））。要向队列添加任务，首先需要像下面这样在服务中注入队列：

```typescript
import { Injectable } from '@nestjs/common'
import { Queue } from 'bull'
import { InjectQueue } from '@nestjs/bull'

@Injectable()
export class AudioService {
  constructor(@InjectQueue('audio') private audioQueue: Queue) {}
}
```

> info **提示** `@InjectQueue()` 装饰器通过队列名称（即在 `registerQueue()` 方法中提供的名称，例如 `'audio'`）来标识队列。

接下来，可以通过调用队列的 `add()` 方法并传入自定义的任务对象来添加任务。任务以可序列化的 JavaScript 对象形式表示（因为它们会被存储在 Redis 数据库中）。你可以根据业务语义自定义任务对象的结构。

```typescript
const job = await this.audioQueue.add({
  foo: 'bar',
})
```

#### 命名任务（Named jobs）

任务可以拥有唯一的名称。这允许你创建专门的<a href="techniques/queues#consumers">消费者</a>，只处理具有特定名称的任务。

```typescript
const job = await this.audioQueue.add('transcode', {
  foo: 'bar',
})
```

> Warning **警告** 当你使用命名任务时，必须为队列中添加的每个唯一名称创建对应的处理器，否则队列会提示缺少该任务名称的处理器。更多关于消费命名任务的信息请参见<a href="techniques/queues#consumers">此处</a>。

#### 任务选项

任务可以携带额外的选项。你可以在 `Queue.add()` 方法中，将一个选项对象作为 `job` 参数之后的第二个参数传入。任务选项的属性包括：

- `priority`：`number` - （可选）优先级数值。范围从 1（最高优先级）到 MAX_INT（最低优先级）。注意，使用优先级会对性能产生一定影响，请谨慎使用。
- `delay`：`number` - 延迟处理任务的时间（以毫秒为单位）。注意，为了保证延迟的准确性，服务器和客户端的时钟应保持同步。
- `attempts`：`number` - 任务在完成前的最大重试次数。
- `repeat`：`RepeatOpts` - 按照 cron 规则重复任务。详见 [RepeatOpts](https://github.com/OptimalBits/bull/blob/master/REFERENCE.md#queueadd)。
- `backoff`：`number | BackoffOpts` - 任务失败时自动重试的退避（Backoff）设置。详见 [BackoffOpts](https://github.com/OptimalBits/bull/blob/master/REFERENCE.md#queueadd)。
- `lifo`：`boolean` - 如果为 true，则将任务添加到队列的右端（即后进先出 LIFO），默认值为 false。
- `timeout`：`number` - 超过指定毫秒数后，任务将因超时错误而失败。
- `jobId`：`number` | `string` - 自定义任务 ID。默认情况下，任务 ID 是唯一的整数，但你可以通过此设置自定义。请确保 jobId 的唯一性。如果尝试添加已存在的 ID，则该任务不会被添加。
- `removeOnComplete`：`boolean | number` - 如果为 true，任务成功完成后将被移除。若为数字，则表示保留的已完成任务数量。默认行为是保留任务在已完成集合中。
- `removeOnFail`：`boolean | number` - 如果为 true，任务在所有重试失败后将被移除。若为数字，则表示保留的失败任务数量。默认行为是保留任务在失败集合中。
- `stackTraceLimit`：`number` - 限制记录在堆栈跟踪中的行数。

以下是自定义任务选项的几个示例。

要延迟任务的开始，可以使用 `delay` 配置属性：

```typescript
const job = await this.audioQueue.add(
  {
    foo: 'bar',
  },
  { delay: 3000 } // 延迟 3 秒执行
)
```

如果希望将任务添加到队列的右端（以**后进先出 LIFO** 的方式处理），可以将配置对象的 `lifo` 属性设置为 `true`：

```typescript
const job = await this.audioQueue.add(
  {
    foo: 'bar',
  },
  { lifo: true }
)
```

要为任务设置优先级，可以使用 `priority` 属性：

```typescript
const job = await this.audioQueue.add(
  {
    foo: 'bar',
  },
  { priority: 2 }
)
```

#### 消费者（Consumer）

消费者是一个**类**，用于定义处理添加到队列中的任务（job）的方法，或监听队列上的事件的方法，或者两者兼有。可以使用 `@Processor()` 装饰器声明一个消费者类，示例如下：

```typescript
import { Processor } from '@nestjs/bull'

@Processor('audio')
export class AudioConsumer {}
```

> info **提示** 消费者必须作为 `提供者（Provider）` 注册，这样 `@nestjs/bull` 包才能正确识别。

装饰器中的字符串参数（如 `'audio'`）表示要将该类的方法关联到的队列名称。

在消费者类中，可以通过为处理方法添加 `@Process()` 装饰器来声明任务处理器。

```typescript
import { Processor, Process } from '@nestjs/bull'
import { Job } from 'bull'

@Processor('audio')
export class AudioConsumer {
  @Process()
  async transcode(job: Job<unknown>) {
    let progress = 0
    for (let i = 0; i < 100; i++) {
      await doSomething(job.data)
      progress += 1
      await job.progress(progress)
    }
    return {}
  }
}
```

被装饰的方法（如 `transcode()`）会在 worker 空闲且队列中有待处理任务时被调用。该处理器方法接收 `job` 对象作为唯一参数。处理器方法返回的值会被存储在任务对象中，之后可以在监听任务完成事件时访问。

`Job` 对象提供了多种方法用于与其状态交互。例如，上述代码中使用了 `progress()` 方法来更新任务的进度。完整的 `Job` 对象 API 参考请参见 [这里](https://github.com/OptimalBits/bull/blob/master/REFERENCE.md#job)。

你可以通过为 `@Process()` 装饰器传递特定的 `name`，指定某个处理方法**只处理**特定类型（具有特定 `name`）的任务。你可以在同一个消费者类中声明多个 `@Process()` 处理器，分别对应每种任务类型（`name`）。当你使用命名任务时，请确保每个名称都有对应的处理器。

```typescript
@Process('transcode')
async transcode(job: Job<unknown>) { ... }
```

> warning **警告** 当为同一个队列定义多个消费者时，`@Process({ concurrency: 1 })` 中的 `concurrency`（并发数）选项不会生效。最小的并发数会等于定义的消费者数量。即使 `@Process()` 处理器使用不同的 `name` 处理命名任务，这一规则同样适用。

#### 请求作用域消费者

当消费者被标记为请求作用域（详细了解依赖注入作用域可参考[此处](/fundamentals/injection-scopes#provider-scope)），每个任务都会专门创建该类的新实例。任务完成后，该实例会被垃圾回收机制自动销毁。

```typescript
@Processor({
  name: 'audio',
  scope: Scope.REQUEST,
})
```

由于请求作用域的消费者类是动态实例化且仅绑定到单个任务，因此你可以通过标准方式在构造函数中注入 `JOB_REF`。

```typescript
constructor(@Inject(JOB_REF) jobRef: Job) {
  console.log(jobRef);
}
```

> info **提示** `JOB_REF` 令牌需从 `@nestjs/bull` 包中导入。

#### 事件监听器

当队列或任务状态发生变化时，Bull 会生成一系列有用的事件。Nest 提供了一组装饰器，用于订阅这些标准核心事件。这些装饰器由 `@nestjs/bull` 包导出。

事件监听器必须声明在<a href="techniques/queues#consumers">消费者</a>类中（即，被 `@Processor()` 装饰器修饰的类）。要监听某个事件，可以使用下表中的装饰器之一，在类中声明对应的事件处理方法。例如，若要监听 `audio` 队列中任务进入 active 状态时触发的事件，可以参考如下写法：

```typescript
import { Processor, Process, OnQueueActive } from '@nestjs/bull';
import { Job } from 'bull';

@Processor('audio')
export class AudioConsumer {

  @OnQueueActive()
  onActive(job: Job) {
    console.log(
      `Processing job ${job.id} of type ${job.name} with data ${job.data}...`,
    );
  }
  ...
```

由于 Bull 运行在分布式（多节点）环境中，因此引入了事件本地性（event locality）的概念。该概念指出，事件既可能完全在单个进程内被触发，也可能在多个进程共享的队列上被触发。**本地事件（local event）** 指的是在本地进程的队列上执行操作或状态变更时产生的事件。换句话说，当事件生产者和消费者都在同一个进程中时，所有队列上的事件都是本地事件。

当队列被多个进程共享时，就会出现 **全局事件（global event）** 的可能性。若希望某个进程中的监听器能够接收到由另一个进程触发的事件通知，则必须注册为全局事件。

事件处理器会在其对应事件被触发时被调用。处理器的调用签名见下表，能够访问与事件相关的信息。下文将讨论本地事件和全局事件处理器签名的一个关键区别。

<table>
  <tr>
    <th>本地事件监听器</th>
    <th>全局事件监听器</th>
    <th>处理方法签名 / 触发时机</th>
  </tr>
  <tr>
    <td>
      <code>@OnQueueError()</code>
    </td>
    <td>
      <code>@OnGlobalQueueError()</code>
    </td>
    <td>
      <code>handler(error: Error)</code> - 发生错误时触发。<code>error</code> 包含触发的错误信息。
    </td>
  </tr>
  <tr>
    <td>
      <code>@OnQueueWaiting()</code>
    </td>
    <td>
      <code>@OnGlobalQueueWaiting()</code>
    </td>
    <td>
      <code>handler(jobId: number | string)</code> - 当有作业（Job）等待被空闲的工作进程处理时触发。
      <code>jobId</code> 为进入此状态的作业 ID。
    </td>
  </tr>
  <tr>
    <td>
      <code>@OnQueueActive()</code>
    </td>
    <td>
      <code>@OnGlobalQueueActive()</code>
    </td>
    <td>
      <code>handler(job: Job)</code> - 任务 <code>job</code> 已开始处理。
    </td>
  </tr>
  <tr>
    <td>
      <code>@OnQueueStalled()</code>
    </td>
    <td>
      <code>@OnGlobalQueueStalled()</code>
    </td>
    <td>
      <code>handler(job: Job)</code> - 任务 <code>job</code>{' '}
      被标记为阻塞（stalled）。这对于调试崩溃或事件循环暂停的作业工作进程非常有用。
    </td>
  </tr>
  <tr>
    <td>
      <code>@OnQueueProgress()</code>
    </td>
    <td>
      <code>@OnGlobalQueueProgress()</code>
    </td>
    <td>
      <code>handler(job: Job, progress: number)</code> - 任务 <code>job</code> 的进度已更新为{' '}
      <code>progress</code>。
    </td>
  </tr>
  <tr>
    <td>
      <code>@OnQueueCompleted()</code>
    </td>
    <td>
      <code>@OnGlobalQueueCompleted()</code>
    </td>
    <td>
      <code>handler(job: Job, result: any)</code> 任务 <code>job</code> 已成功完成，结果为{' '}
      <code>result</code>。
    </td>
  </tr>
  <tr>
    <td>
      <code>@OnQueueFailed()</code>
    </td>
    <td>
      <code>@OnGlobalQueueFailed()</code>
    </td>
    <td>
      <code>handler(job: Job, err: Error)</code> 任务 <code>job</code> 失败，原因是 <code>err</code>
      。
    </td>
  </tr>
  <tr>
    <td>
      <code>@OnQueuePaused()</code>
    </td>
    <td>
      <code>@OnGlobalQueuePaused()</code>
    </td>
    <td>
      <code>handler()</code> 队列已被暂停。
    </td>
  </tr>
  <tr>
    <td>
      <code>@OnQueueResumed()</code>
    </td>
    <td>
      <code>@OnGlobalQueueResumed()</code>
    </td>
    <td>
      <code>handler(job: Job)</code> 队列已恢复。
    </td>
  </tr>
  <tr>
    <td>
      <code>@OnQueueCleaned()</code>
    </td>
    <td>
      <code>@OnGlobalQueueCleaned()</code>
    </td>
    <td>
      <code>handler(jobs: Job[], type: string)</code> 旧任务已从队列中清理。<code>jobs</code>{' '}
      是被清理的任务数组，<code>type</code> 表示被清理的任务类型。
    </td>
  </tr>
  <tr>
    <td>
      <code>@OnQueueDrained()</code>
    </td>
    <td>
      <code>@OnGlobalQueueDrained()</code>
    </td>
    <td>
      <code>handler()</code> 当队列已处理完所有等待中的任务时触发（即使仍有一些延迟任务尚未处理）。
    </td>
  </tr>
  <tr>
    <td>
      <code>@OnQueueRemoved()</code>
    </td>
    <td>
      <code>@OnGlobalQueueRemoved()</code>
    </td>
    <td>
      <code>handler(job: Job)</code> 任务 <code>job</code> 已被成功移除。
    </td>
  </tr>
</table>

当监听全局事件时，方法签名与本地事件监听略有不同。具体来说，任何在本地版本中接收 `job` 对象的方法签名，在全局版本中会接收一个 `jobId`（number 类型）。如果你需要获取实际的 `job` 对象，可以使用 `Queue#getJob` 方法。由于该方法是异步的，因此事件处理函数应声明为 `async`。例如：

```typescript
@OnGlobalQueueCompleted()
async onGlobalCompleted(jobId: number, result: any) {
  const job = await this.immediateQueue.getJob(jobId);
  console.log('(Global) on completed: job ', job.id, ' -> result: ', result);
}
```

> info **提示**  
> 要访问 `Queue` 对象（以便调用 `getJob()` 方法），你需要将其依赖注入到当前类中。同时，该队列必须已经在你注入它的模块（Module）中完成注册。

除了这些特定的事件监听装饰器外，你还可以结合使用通用的 `@OnQueueEvent()` 装饰器和 `BullQueueEvents` 或 `BullQueueGlobalEvents` 枚举来监听事件。你可以在[这里](https://github.com/OptimalBits/bull/blob/master/REFERENCE.md#events)阅读更多关于事件的内容。

#### 队列管理

队列（Queue）提供了 API，允许你执行诸如暂停和恢复、获取不同状态下任务数量等管理操作。完整的队列 API 可参考 [这里](https://github.com/OptimalBits/bull/blob/master/REFERENCE.md#queue)。你可以直接在 `Queue` 对象上调用这些方法，下方以暂停/恢复为例进行演示。

通过调用 `pause()` 方法可以暂停队列。被暂停的队列不会处理新的任务，但当前正在处理的任务会继续执行直到完成。

```typescript
await audioQueue.pause()
```

要恢复已暂停的队列，可以使用 `resume()` 方法，如下所示：

```typescript
await audioQueue.resume()
```

#### 独立进程处理

任务处理器（Job handler）也可以在独立（fork）进程中运行（[参考来源](https://github.com/OptimalBits/bull#separate-processes)）。这样做有以下几个优点：

- 进程被沙箱隔离，即使崩溃也不会影响主 worker 进程。
- 可以运行阻塞代码而不影响队列（任务不会卡住）。
- 能更好地利用多核 CPU。
- 对 Redis 的连接数更少。

```ts
@@filename(app.module)
import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { join } from 'path';

@Module({
  imports: [
    BullModule.registerQueue({
      name: 'audio',
      processors: [join(__dirname, 'processor.js')],
    }),
  ],
})
export class AppModule {}
```

请注意，由于你的函数会在独立进程中执行，依赖注入（Dependency Injection）机制（以及 IoC 容器）将不可用。这意味着你的处理器函数需要自行包含（或创建）所有所需的外部依赖实例。

```ts
@@filename(processor)
import { Job, DoneCallback } from 'bull';

export default function (job: Job, cb: DoneCallback) {
  console.log(`[${process.pid}] ${JSON.stringify(job.data)}`);
  cb(null, 'It works');
}
```

#### 异步配置

有时你可能希望以异步方式（而非静态方式）传递 `bull` 选项。此时，可以使用 `forRootAsync()` 方法，该方法提供了多种处理异步配置的方式。

其中一种方式是使用工厂函数：

```typescript
BullModule.forRootAsync({
  useFactory: () => ({
    redis: {
      host: 'localhost',
      port: 6379,
    },
  }),
})
```

我们的工厂函数行为与其他[异步提供者](https://docs.nestjs.com/fundamentals/async-providers)类似（例如，它可以是 `async`，并且能够通过 `inject` 注入依赖）。

```typescript
BullModule.forRootAsync({
  imports: [ConfigModule],
  useFactory: async (configService: ConfigService) => ({
    redis: {
      host: configService.get('QUEUE_HOST'),
      port: configService.get('QUEUE_PORT'),
    },
  }),
  inject: [ConfigService],
})
```

另外，你也可以使用 `useClass` 语法：

```typescript
BullModule.forRootAsync({
  useClass: BullConfigService,
})
```

上述写法会在 `BullModule` 内部实例化 `BullConfigService`，并通过调用 `createSharedConfiguration()` 方法来提供配置对象。需要注意的是，这意味着 `BullConfigService` 必须实现 `SharedBullConfigurationFactory` 接口，如下所示：

```typescript
@Injectable()
class BullConfigService implements SharedBullConfigurationFactory {
  createSharedConfiguration(): BullModuleOptions {
    return {
      redis: {
        host: 'localhost',
        port: 6379,
      },
    }
  }
}
```

如果你希望避免在 `BullModule` 内部创建 `BullConfigService` 实例，而是复用其他模块中已导入的提供者，可以使用 `useExisting` 语法。

```typescript
BullModule.forRootAsync({
  imports: [ConfigModule],
  useExisting: ConfigService,
})
```

这种写法与 `useClass` 类似，但有一个关键区别 —— `BullModule` 会查找已导入的模块，复用现有的 `ConfigService`，而不是新建一个实例。

同样地，如果你想以异步方式传递队列选项，可以使用 `registerQueueAsync()` 方法，只需注意将 `name` 属性放在工厂函数外部。

```typescript
BullModule.registerQueueAsync({
  name: 'audio',
  useFactory: () => ({
    redis: {
      host: 'localhost',
      port: 6379,
    },
  }),
})
```

#### 示例

你可以在[这里](https://github.com/nestjs/nest/tree/master/sample/26-queues)查看完整示例。
