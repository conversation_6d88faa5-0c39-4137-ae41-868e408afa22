### HTTP 模块

[Axios](https://github.com/axios/axios) 是一个功能丰富且被广泛使用的 HTTP 客户端库。Nest 对 Axios 进行了封装，并通过内置的 `HttpModule`（HTTP 模块）对外暴露。`HttpModule` 会导出 `HttpService` 类，该类基于 Axios 提供了多种用于发起 HTTP 请求的方法。同时，该库会将 HTTP 响应结果转换为 `Observable`。

> info **提示** 你也可以直接使用任何通用的 Node.js HTTP 客户端库，例如 [got](https://github.com/sindresorhus/got) 或 [undici](https://github.com/nodejs/undici)。

#### 安装依赖

要开始使用，需要先安装相关依赖包。

```bash
$ npm i --save @nestjs/axios axios
```

#### 快速上手

安装完成后，若要使用 `HttpService`，首先需要导入 `HttpModule`。

```typescript
@Module({
  imports: [HttpModule],
  providers: [CatsService],
})
export class CatsModule {}
```

接下来，通过常规的构造函数依赖注入方式注入 `HttpService`。

> info **提示** `HttpModule` 和 `HttpService` 都是从 `@nestjs/axios` 包中导入的。

```typescript
@@filename()
@Injectable()
export class CatsService {
  constructor(private readonly httpService: HttpService) {}

  findAll(): Observable<AxiosResponse<Cat[]>> {
    return this.httpService.get('http://localhost:3000/cats');
  }
}
@@switch
@Injectable()
@Dependencies(HttpService)
export class CatsService {
  constructor(httpService) {
    this.httpService = httpService;
  }

  findAll() {
    return this.httpService.get('http://localhost:3000/cats');
  }
}
```

> info **提示** `AxiosResponse` 是由 `axios` 包导出的接口（`$ npm i axios`）。

所有 `HttpService` 的方法都会返回一个被 `Observable` 包裹的 `AxiosResponse` 对象。

#### 配置

[Axios](https://github.com/axios/axios) 可以通过多种选项进行配置，从而自定义 `HttpService` 的行为。你可以在 [这里](https://github.com/axios/axios#request-config) 阅读更多相关内容。要配置底层的 Axios 实例，可以在导入 `HttpModule` 时，向其 `register()` 方法传递一个可选的配置对象。该配置对象会被直接传递给底层的 Axios 构造函数。

```typescript
@Module({
  imports: [
    HttpModule.register({
      timeout: 5000,
      maxRedirects: 5,
    }),
  ],
  providers: [CatsService],
})
export class CatsModule {}
```

#### 异步配置

当你需要以异步方式（而非静态方式）传递模块选项时，可以使用 `registerAsync()` 方法。与大多数动态模块一样，Nest 提供了多种处理异步配置的技术手段。

其中一种方式是使用工厂函数：

```typescript
HttpModule.registerAsync({
  useFactory: () => ({
    timeout: 5000,
    maxRedirects: 5,
  }),
})
```

与其他工厂提供者类似，我们的工厂函数也可以是 [async](https://docs.nestjs.com/fundamentals/custom-providers#factory-providers-usefactory) 异步函数，并且可以通过 `inject` 注入依赖。

```typescript
HttpModule.registerAsync({
  imports: [ConfigModule],
  useFactory: async (configService: ConfigService) => ({
    timeout: configService.get('HTTP_TIMEOUT'),
    maxRedirects: configService.get('HTTP_MAX_REDIRECTS'),
  }),
  inject: [ConfigService],
})
```

另外，你也可以通过类（class）而不是工厂函数来配置 `HttpModule`，如下所示：

```typescript
HttpModule.registerAsync({
  useClass: HttpConfigService,
})
```

上述写法会在 `HttpModule` 内部实例化 `HttpConfigService`，并使用它来创建选项对象。需要注意的是，在这个例子中，`HttpConfigService` 必须实现 `HttpModuleOptionsFactory` 接口，如下所示。`HttpModule` 会在所提供类的实例上调用 `createHttpOptions()` 方法。

```typescript
@Injectable()
class HttpConfigService implements HttpModuleOptionsFactory {
  createHttpOptions(): HttpModuleOptions {
    return {
      timeout: 5000,
      maxRedirects: 5,
    }
  }
}
```

如果你希望复用已有的选项提供者，而不是在 `HttpModule` 内部创建一个私有副本，可以使用 `useExisting` 语法。

```typescript
HttpModule.registerAsync({
  imports: [ConfigModule],
  useExisting: HttpConfigService,
})
```

你还可以通过 `registerAsync()` 方法传递所谓的 `extraProviders`。这些提供者会与模块内的其他提供者合并。

```typescript
HttpModule.registerAsync({
  imports: [ConfigModule],
  useClass: HttpConfigService,
  extraProviders: [MyAdditionalProvider],
})
```

当你希望为工厂函数或类构造函数提供额外依赖时，这种方式非常有用。

#### 直接使用 Axios

如果你觉得 `HttpModule.register` 的配置选项无法满足你的需求，或者你只是想直接访问由 `@nestjs/axios` 创建的底层 Axios 实例，可以通过 `HttpService#axiosRef` 属性来实现，如下所示：

```typescript
@Injectable()
export class CatsService {
  constructor(private readonly httpService: HttpService) {}

  findAll(): Promise<AxiosResponse<Cat[]>> {
    return this.httpService.axiosRef.get('http://localhost:3000/cats')
    //                      ^ AxiosInstance 接口
  }
}
```

#### 完整示例

由于 `HttpService` 方法的返回值是 Observable，因此我们可以使用 `rxjs` 的 `firstValueFrom` 或 `lastValueFrom`，以 Promise 的形式获取请求的数据。

```typescript
import { catchError, firstValueFrom } from 'rxjs'

@Injectable()
export class CatsService {
  private readonly logger = new Logger(CatsService.name)
  constructor(private readonly httpService: HttpService) {}

  async findAll(): Promise<Cat[]> {
    const { data } = await firstValueFrom(
      this.httpService.get<Cat[]>('http://localhost:3000/cats').pipe(
        catchError((error: AxiosError) => {
          this.logger.error(error.response.data)
          throw '发生错误！'
        })
      )
    )
    return data
  }
}
```

> info **提示**
> 访问 RxJS 官方文档，了解 [`firstValueFrom`](https://rxjs.dev/api/index/function/firstValueFrom) 和 [`lastValueFrom`](https://rxjs.dev/api/index/function/lastValueFrom) 之间的区别。
