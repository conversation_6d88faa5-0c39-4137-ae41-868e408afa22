### 缓存（Caching）

缓存是一种强大且简单的**技术手段**，可以显著提升应用程序的性能。作为临时存储层，缓存能够更快地访问常用数据，减少重复获取或计算相同信息的需求，从而带来更快的响应速度和整体效率的提升。

#### 安装

要在 Nest 中开始使用缓存功能，需要安装 `@nestjs/cache-manager` 包以及 `cache-manager` 包。

```bash
$ npm install @nestjs/cache-manager cache-manager
```

默认情况下，所有内容都存储在内存中。由于 `cache-manager` 底层使用了 [Keyv](https://keyv.org/docs/)，你可以通过安装相应的包，轻松切换为更高级的存储方案（如 Redis）。后文会详细介绍相关内容。

#### 内存缓存

要在应用中启用缓存，只需导入 `CacheModule` 并通过 `register()` 方法进行配置：

```typescript
import { Module } from '@nestjs/common'
import { CacheModule } from '@nestjs/cache-manager'
import { AppController } from './app.controller'

@Module({
  imports: [CacheModule.register()],
  controllers: [AppController],
})
export class AppModule {}
```

上述配置会以默认设置初始化内存缓存，让你可以立即开始缓存数据。

#### 与缓存存储交互

如需操作缓存管理器实例，可通过如下方式，使用 `CACHE_MANAGER` 注入令牌将其注入到你的类中：

```typescript
constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}
```

> info **提示** `Cache` 类需从 `cache-manager` 包导入，而 `CACHE_MANAGER` 注入令牌则来自 `@nestjs/cache-manager` 包。

`Cache` 实例（来自 `cache-manager` 包）上的 `get` 方法用于从缓存中获取数据。如果缓存中不存在该项，则会返回 `null`。

```typescript
const value = await this.cacheManager.get('key')
```

要向缓存中添加数据，请使用 `set` 方法：

```typescript
await this.cacheManager.set('key', 'value')
```

> warning **注意** 内存缓存存储仅支持 [结构化克隆算法](https://developer.mozilla.org/zh-CN/docs/Web/API/Web_Workers_API/Structured_clone_algorithm#javascript_types) 支持的数据类型。

你可以为某个特定键手动指定 TTL（过期时间，单位为毫秒），示例如下：

```typescript
await this.cacheManager.set('key', 'value', 1000)
```

其中 `1000` 表示 TTL 为 1000 毫秒 —— 即该缓存项将在 1 秒后过期。

如需禁用缓存过期，可将 `ttl` 配置属性设置为 `0`：

```typescript
await this.cacheManager.set('key', 'value', 0)
```

要从缓存中移除某项，请使用 `del` 方法：

```typescript
await this.cacheManager.del('key')
```

如需清空整个缓存，请使用 `clear` 方法：

```typescript
await this.cacheManager.clear()
```

#### 自动缓存响应

> warning **警告** 在 [GraphQL](/graphql/quick-start) 应用中，拦截器会针对每个字段解析器单独执行。因此，`CacheModule`（其通过拦截器缓存响应）在此场景下无法正常工作。

要启用自动缓存响应，只需在需要缓存数据的地方绑定 `CacheInterceptor` 拦截器。

```typescript
@Controller()
@UseInterceptors(CacheInterceptor)
export class AppController {
  @Get()
  findAll(): string[] {
    return []
  }
}
```

> warning **警告** 仅 `GET` 类型的接口会被缓存。此外，注入原生响应对象（`@Res()`）的 HTTP 服务器路由无法使用缓存拦截器。详见
>
> <a href="https://docs.nestjs.com/interceptors#response-mapping">响应映射</a> 获取更多信息。

为减少样板代码，你也可以将 `CacheInterceptor` 全局绑定到所有接口：

```typescript
import { Module } from '@nestjs/common'
import { CacheModule, CacheInterceptor } from '@nestjs/cache-manager'
import { AppController } from './app.controller'
import { APP_INTERCEPTOR } from '@nestjs/core'

@Module({
  imports: [CacheModule.register()],
  controllers: [AppController],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: CacheInterceptor,
    },
  ],
})
export class AppModule {}
```

#### 有效期（TTL）

`ttl` 的默认值为 `0`，表示缓存永不过期。要自定义 [TTL（Time to live，存活时间）](https://en.wikipedia.org/wiki/Time_to_live)，可以在 `register()` 方法中通过 `ttl` 选项进行设置，如下所示：

```typescript
CacheModule.register({
  ttl: 5000, // 毫秒
})
```

#### 全局使用模块

当你希望在其他模块中使用 `CacheModule` 时，需要像导入其他 Nest 模块一样进行导入。或者，也可以通过在选项对象中设置 `isGlobal` 属性为 `true`，将其声明为[全局模块](https://docs.nestjs.com/modules#global-modules)。这样一来，只需在根模块（例如 `AppModule`）中加载一次 `CacheModule`，之后在其他模块中无需再次导入。

```typescript
CacheModule.register({
  isGlobal: true,
})
```

#### 全局缓存重写

启用全局缓存后，缓存项会根据路由路径自动生成的 `CacheKey` 进行存储。你可以在每个方法级别上通过 `@CacheKey()` 和 `@CacheTTL()` 装饰器重写部分缓存设置，从而为单独的控制器方法定制缓存策略。这在使用[不同的缓存存储](https://docs.nestjs.com/techniques/caching#different-stores)时尤其有用。

你可以在控制器级别应用 `@CacheTTL()` 装饰器，为整个控制器设置缓存的 TTL。当控制器和方法级别都设置了缓存 TTL 时，以方法级别的设置为准。

```typescript
@Controller()
@CacheTTL(50)
export class AppController {
  @CacheKey('custom_key')
  @CacheTTL(20)
  findAll(): string[] {
    return []
  }
}
```

> info **提示** `@CacheKey()` 和 `@CacheTTL()` 装饰器需从 `@nestjs/cache-manager` 包中导入。

`@CacheKey()` 装饰器可以单独使用，也可以与 `@CacheTTL()` 搭配使用，反之亦然。你可以只重写 `@CacheKey()` 或只重写 `@CacheTTL()`。未被装饰器重写的设置将使用全局注册时的默认值（参见[自定义缓存](https://docs.nestjs.com/techniques/caching#customize-caching)）。

#### WebSocket 和微服务

你也可以将 `CacheInterceptor` 应用于 WebSocket 订阅者以及微服务（Microservices）的消息模式（无论使用哪种传输方式）。

```typescript
@@filename()
@CacheKey('events')
@UseInterceptors(CacheInterceptor)
@SubscribeMessage('events')
handleEvent(client: Client, data: string[]): Observable<string[]> {
  return [];
}
@@switch
@CacheKey('events')
@UseInterceptors(CacheInterceptor)
@SubscribeMessage('events')
handleEvent(client, data) {
  return [];
}
```

不过，必须额外使用 `@CacheKey()` 装饰器来指定用于存储和检索缓存数据的键。同时请注意，**不应缓存所有内容**。执行业务操作（而不仅仅是查询数据）的操作不应被缓存。

此外，你可以通过 `@CacheTTL()` 装饰器指定缓存的过期时间（TTL），该值会覆盖全局默认 TTL。

```typescript
@@filename()
@CacheTTL(10)
@UseInterceptors(CacheInterceptor)
@SubscribeMessage('events')
handleEvent(client: Client, data: string[]): Observable<string[]> {
  return [];
}
@@switch
@CacheTTL(10)
@UseInterceptors(CacheInterceptor)
@SubscribeMessage('events')
handleEvent(client, data) {
  return [];
}
```

> info **提示** `@CacheTTL()` 装饰器可以单独使用，也可以与 `@CacheKey()` 搭配使用。

#### 调整追踪方式

默认情况下，Nest 会使用请求 URL（在 HTTP 应用中）或缓存键（在 WebSocket 和微服务应用中，通过 `@CacheKey()` 装饰器设置）来将缓存记录与端点关联。然而，有时你可能希望基于其他因素进行追踪，例如使用 HTTP 请求头（如 `Authorization`，以便正确识别 `profile` 端点）。

要实现这一点，可以创建 `CacheInterceptor（缓存拦截器）` 的子类，并重写 `trackBy()` 方法。

```typescript
@Injectable()
class HttpCacheInterceptor extends CacheInterceptor {
  trackBy(context: ExecutionContext): string | undefined {
    return 'key'
  }
}
```

#### 使用其他缓存存储

切换到不同的缓存存储非常简单。首先，安装相应的包。例如，如果你想使用 Redis，可以安装 `@keyv/redis` 包：

```bash
$ npm install @keyv/redis
```

安装完成后，你可以像下面这样为 `CacheModule（缓存模块）` 注册多个存储：

```typescript
import { Module } from '@nestjs/common'
import { CacheModule } from '@nestjs/cache-manager'
import { AppController } from './app.controller'
import { createKeyv } from '@keyv/redis'
import { Keyv } from 'keyv'
import { CacheableMemory } from 'cacheable'

@Module({
  imports: [
    CacheModule.registerAsync({
      useFactory: async () => {
        return {
          stores: [
            new Keyv({
              store: new CacheableMemory({ ttl: 60000, lruSize: 5000 }),
            }),
            createKeyv('redis://localhost:6379'),
          ],
        }
      },
    }),
  ],
  controllers: [AppController],
})
export class AppModule {}
```

在上述示例中，我们注册了两个存储：`CacheableMemory` 和 `KeyvRedis`。`CacheableMemory` 是一个简单的内存存储，而 `KeyvRedis` 则是 Redis 存储。通过 `stores` 数组可以指定你想要使用的存储。数组中的第一个存储为默认存储，其余为备用存储。

想了解更多可用存储的信息，请查阅 [Keyv 文档](https://keyv.org/docs/)。

#### 异步配置

有时你可能希望在运行时异步传递模块（Module）选项，而不是在编译时静态传递。在这种情况下，可以使用 `registerAsync()` 方法。该方法提供了多种方式来处理异步配置。

其中一种方式是使用工厂函数（factory function）：

```typescript
CacheModule.registerAsync({
  useFactory: () => ({
    ttl: 5,
  }),
})
```

我们的工厂函数与其他异步模块工厂（asynchronous module factories）类似（它可以是 `async` 的，并且能够通过 `inject` 注入依赖）。

```typescript
CacheModule.registerAsync({
  imports: [ConfigModule],
  useFactory: async (configService: ConfigService) => ({
    ttl: configService.get('CACHE_TTL'),
  }),
  inject: [ConfigService],
})
```

另外，你也可以使用 `useClass` 方式：

```typescript
CacheModule.registerAsync({
  useClass: CacheConfigService,
})
```

上述写法会在 `CacheModule` 内部实例化 `CacheConfigService`，并使用它来获取配置选项对象。`CacheConfigService` 需要实现 `CacheOptionsFactory` 接口，以便提供配置选项：

```typescript
@Injectable()
class CacheConfigService implements CacheOptionsFactory {
  createCacheOptions(): CacheModuleOptions {
    return {
      ttl: 5,
    }
  }
}
```

如果你希望使用从其他模块（Module）导入的现有配置服务提供者，可以使用 `useExisting` 语法：

```typescript
CacheModule.registerAsync({
  imports: [ConfigModule],
  useExisting: ConfigService,
})
```

这种方式与 `useClass` 类似，但有一个关键区别 —— `CacheModule` 会在已导入的模块中查找并复用已创建的 `ConfigService`，而不是自行实例化。

> info **提示** `CacheModule#register`、`CacheModule#registerAsync` 以及 `CacheOptionsFactory` 都支持可选的泛型（类型参数），用于限定特定存储（store）的配置选项，从而提升类型安全性。

你还可以通过 `registerAsync()` 方法传递所谓的 `extraProviders`。这些提供者（Provider）会与模块（Module）内的其他提供者合并。

```typescript
CacheModule.registerAsync({
  imports: [ConfigModule],
  useClass: ConfigService,
  extraProviders: [MyAdditionalProvider],
})
```

当你希望为工厂函数或类构造函数提供额外依赖时，这种方式非常有用。

#### 示例

可用的完整示例请参考 [这里](https://github.com/nestjs/nest/tree/master/sample/20-cache)。
