### 文件流式传输

> info **注意** 本章节介绍如何在 **HTTP 应用程序** 中实现文件流式传输。以下示例不适用于 GraphQL 或微服务应用。

有时候，你可能希望通过 RESTful 接口将文件返回给客户端。在 Nest 中，通常可以这样实现：

```ts
@Controller('file')
export class FileController {
  @Get()
  getFile(@Res() res: Response) {
    const file = createReadStream(join(process.cwd(), 'package.json'))
    file.pipe(res)
  }
}
```

但这样做会导致你无法使用控制器后置的拦截器逻辑。为了解决这个问题，你可以返回一个 `StreamableFile` 实例，底层框架会自动处理响应的流式传输。

#### StreamableFile 类

`StreamableFile` 是一个用于持有待返回流的类。你可以通过向 `StreamableFile` 构造函数传入 `Buffer` 或 `Stream` 来创建新的实例。

> info **提示** `StreamableFile` 类可从 `@nestjs/common` 包中导入。

#### 跨平台支持

Fastify 默认支持直接发送文件，无需调用 `stream.pipe(res)`，因此无需使用 `StreamableFile` 类。不过，Nest 在两种平台类型中都支持 `StreamableFile`，因此如果你在 Express 和 Fastify 之间切换，无需担心两者的兼容性。

#### 示例

下面是一个简单示例，演示如何将 `package.json` 作为文件返回，而不是以 JSON 格式返回。当然，这一思路同样适用于图片、文档等任意类型的文件。

```ts
import { Controller, Get, StreamableFile } from '@nestjs/common'
import { createReadStream } from 'fs'
import { join } from 'path'

@Controller('file')
export class FileController {
  @Get()
  getFile(): StreamableFile {
    const file = createReadStream(join(process.cwd(), 'package.json'))
    return new StreamableFile(file)
  }
}
```

默认情况下，响应头 `Content-Type` 的值为 `application/octet-stream`。如果你需要自定义该值，可以使用 `StreamableFile` 的 `type` 选项，或者通过 `res.set` 方法，亦或是使用 [`@Header()`](/controllers#headers) 装饰器，例如：

```ts
import { Controller, Get, StreamableFile, Res } from '@nestjs/common'
import { createReadStream } from 'fs'
import { join } from 'path'
import type { Response } from 'express' // 假设我们使用的是 Express（Node.js 框架）HTTP 适配器

@Controller('file')
export class FileController {
  @Get()
  getFile(): StreamableFile {
    const file = createReadStream(join(process.cwd(), 'package.json'))
    return new StreamableFile(file, {
      type: 'application/json',
      disposition: 'attachment; filename="package.json"',
      // 如果你希望自定义 Content-Length 的值而不是使用文件长度：
      // length: 123,
    })
  }

  // 或者这样：
  @Get()
  getFileChangingResponseObjDirectly(@Res({ passthrough: true }) res: Response): StreamableFile {
    const file = createReadStream(join(process.cwd(), 'package.json'))
    res.set({
      'Content-Type': 'application/json',
      'Content-Disposition': 'attachment; filename="package.json"',
    })
    return new StreamableFile(file)
  }

  // 还可以这样：
  @Get()
  @Header('Content-Type', 'application/json')
  @Header('Content-Disposition', 'attachment; filename="package.json"')
  getFileUsingStaticValues(): StreamableFile {
    const file = createReadStream(join(process.cwd(), 'package.json'))
    return new StreamableFile(file)
  }
}
```
