### 版本控制（Versioning）

> info **提示** 本章仅适用于基于 HTTP 的应用程序。

版本控制允许你在同一个应用程序中运行**不同版本**的控制器（Controller）或单独的路由（Route）。应用程序经常发生变化，出现重大变更（breaking changes）时，你可能需要在支持旧版本的同时引入新版本。

Nest 支持 4 种版本控制方式：

<table>
  <tbody>
    <tr>
      <td>
        <a href="techniques/versioning#uri-versioning-type">
          <code>URI 版本控制</code>
        </a>
      </td>
      <td>版本信息通过请求的 URI 传递（默认方式）</td>
    </tr>
    <tr>
      <td>
        <a href="techniques/versioning#header-versioning-type">
          <code>Header 版本控制</code>
        </a>
      </td>
      <td>通过自定义请求头（Header）指定版本</td>
    </tr>
    <tr>
      <td>
        <a href="techniques/versioning#media-type-versioning-type">
          <code>Media Type 版本控制</code>
        </a>
      </td>
      <td>
        通过请求的 <code>Accept</code> 头指定版本
      </td>
    </tr>
  </tbody>
</table>

#### URI 版本控制类型

URI 版本控制通过在请求的 URI 中传递版本号来实现，例如：`https://example.com/v1/route` 和 `https://example.com/v2/route`。

> warning **注意** 使用 URI 版本控制时，版本号会自动添加在 <a href="faq/global-prefix">全局路径前缀</a>（如果存在）之后，并位于控制器或路由路径之前。

要为你的应用程序启用 URI 版本控制，请按如下方式操作：

```typescript
@@filename(main)
const app = await NestFactory.create(AppModule);
// 或直接调用 "app.enableVersioning()"
app.enableVersioning({
  type: VersioningType.URI,
});
await app.listen(process.env.PORT ?? 3000);
```

> warning **注意** URI 中的版本号默认会自动加上 `v` 前缀，你可以通过设置 `prefix` 键为你想要的前缀，或者设置为 `false` 以禁用前缀。

> info **提示** `VersioningType` 枚举可用于 `type` 属性，并从 `@nestjs/common` 包中导入。

#### Header 版本控制类型

Header 版本控制（Header Versioning）使用自定义的、用户指定的请求头（header）来指定版本，头部的值即为本次请求所使用的版本。

Header 版本控制的 HTTP 请求示例：

要为你的应用启用 **Header 版本控制**，请按如下方式操作：

```typescript
@@filename(main)
const app = await NestFactory.create(AppModule);
app.enableVersioning({
  type: VersioningType.HEADER,
  header: 'Custom-Header',
});
await app.listen(process.env.PORT ?? 3000);
```

`header` 属性应设置为包含请求版本号的请求头名称。

> info **提示** `VersioningType` 枚举可用于 `type` 属性，并从 `@nestjs/common` 包中导入。

#### Media Type 版本控制类型

Media Type 版本控制（Media Type Versioning）使用请求的 `Accept` 头来指定版本。

在 `Accept` 头中，版本号与媒体类型之间用分号（`;`）分隔。随后应包含一个键值对，表示本次请求所使用的版本，例如：`Accept: application/json;v=2`。在确定版本时，key 更像是一个前缀，因此需要配置为包含 key 和分隔符。

要为你的应用启用 **Media Type 版本控制**，请按如下方式操作：

```typescript
@@filename(main)
const app = await NestFactory.create(AppModule);
app.enableVersioning({
  type: VersioningType.MEDIA_TYPE,
  key: 'v=',
});
await app.listen(process.env.PORT ?? 3000);
```

`key` 属性应设置为包含版本号的键值对中的 key 和分隔符。以 `Accept: application/json;v=2` 为例，`key` 属性应设置为 `v=`。

> info **提示** `VersioningType` 枚举可用于 `type` 属性，并从 `@nestjs/common` 包中导入。

#### 自定义版本控制类型

自定义版本控制（Custom Versioning）允许你使用请求中的任意部分来指定版本（或多个版本）。传入的请求会通过一个 `extractor`（提取器）函数进行分析，该函数返回一个字符串或字符串数组。

如果请求方提供了多个版本，`extractor` 函数可以返回一个字符串数组，数组中的版本号应按从大到小（从高到低）排序。版本匹配路由时，会按照从高到低的顺序依次匹配。

如果 `extractor` 返回空字符串或空数组，则不会匹配到任何路由，最终返回 404。

例如，如果传入请求声明支持版本 `1`、`2` 和 `3`，那么 `extractor` **必须** 返回 `[3, 2, 1]`。这样可以确保优先选择最高可用的路由版本。

如果提取到的版本为 `[3, 2, 1]`，但只存在版本 `2` 和 `1` 的路由，则会选择匹配版本 `2` 的路由（版本 `3` 会被自动忽略）。

> warning **注意** 基于 `extractor` 返回的数组选择最高匹配版本的机制，**在 Express 适配器中并不总是可靠**，这是由于其设计上的限制导致的。对于 Express，只要返回单个版本（字符串或只包含一个元素的数组）即可正常工作。Fastify 则可以正确支持最高版本优先和单一版本的选择。

要为你的应用启用**自定义版本控制**，只需创建一个 `extractor` 函数，并将其传递给应用程序，如下所示：

```typescript
@@filename(main)
// 示例 extractor，从自定义请求头中提取版本列表，并将其转换为已排序的数组。
// 本例使用 Fastify，但 Express 的请求处理方式类似。
const extractor = (request: FastifyRequest): string | string[] =>
  [request.headers['custom-versioning-field'] ?? '']
     .flatMap(v => v.split(','))
     .filter(v => !!v)
     .sort()
     .reverse()

const app = await NestFactory.create(AppModule);
app.enableVersioning({
  type: VersioningType.CUSTOM,
  extractor,
});
await app.listen(process.env.PORT ?? 3000);
```

#### 使用方式

版本控制允许你为控制器（Controller）、单独的路由（Route）进行版本管理，并且还为某些资源提供了选择不参与版本控制的方式。无论你的应用采用哪种版本控制类型，使用方式都是一致的。

> warning **注意** 如果应用启用了版本控制，但控制器或路由未指定版本，任何对该控制器/路由的请求都会返回 `404` 状态码。同样，如果收到的请求包含了没有对应控制器或路由的版本，也会返回 `404` 状态码。

#### 控制器版本

可以为控制器设置版本，从而为该控制器下的所有路由统一指定版本。

要为控制器添加版本，请按如下方式操作：

```typescript
@@filename(cats.controller)
@Controller({
  version: '1',
})
export class CatsControllerV1 {
  @Get('cats')
  findAll(): string {
    return 'This action returns all cats for version 1';
  }
}
@@switch
@Controller({
  version: '1',
})
export class CatsControllerV1 {
  @Get('cats')
  findAll() {
    return 'This action returns all cats for version 1';
  }
}
```

#### 路由版本

可以为单独的路由（Route）指定版本。此时，该路由的版本会覆盖控制器版本等其他可能影响该路由的版本设置。

要为单个路由添加版本，请按如下方式操作：

```typescript
@@filename(cats.controller)
import { Controller, Get, Version } from '@nestjs/common';

@Controller()
export class CatsController {
  @Version('1')
  @Get('cats')
  findAllV1(): string {
    return 'This action returns all cats for version 1';
  }

  @Version('2')
  @Get('cats')
  findAllV2(): string {
    return 'This action returns all cats for version 2';
  }
}
@@switch
import { Controller, Get, Version } from '@nestjs/common';

@Controller()
export class CatsController {
  @Version('1')
  @Get('cats')
  findAllV1() {
    return 'This action returns all cats for version 1';
  }

  @Version('2')
  @Get('cats')
  findAllV2() {
    return 'This action returns all cats for version 2';
  }
}
```

#### 多版本支持

你可以为控制器或路由同时应用多个版本。要实现多版本支持，只需将 version 属性设置为数组。

添加多个版本的方法如下：

```typescript
@@filename(cats.controller)
@Controller({
  version: ['1', '2'],
})
export class CatsController {
  @Get('cats')
  findAll(): string {
    return '此操作会返回第 1 或第 2 版的所有猫';
  }
}
@@switch
@Controller({
  version: ['1', '2'],
})
export class CatsController {
  @Get('cats')
  findAll() {
    return '此操作会返回第 1 或第 2 版的所有猫';
  }
}
```

#### 版本 "中立"

有些控制器或路由可能不关心版本，无论版本如何，其功能都保持一致。为此，你可以将 version 设置为 `VERSION_NEUTRAL` 符号。

无论请求中是否包含版本，传入的请求都会被映射到 `VERSION_NEUTRAL` 的控制器或路由。

> warning **注意** 对于 URI 版本控制，`VERSION_NEUTRAL` 资源的 URI 中不会包含版本信息。

添加版本中立的控制器或路由的方法如下：

```typescript
@@filename(cats.controller)
import { Controller, Get, VERSION_NEUTRAL } from '@nestjs/common';

@Controller({
  version: VERSION_NEUTRAL,
})
export class CatsController {
  @Get('cats')
  findAll(): string {
    return '此操作会返回所有版本的猫';
  }
}
@@switch
import { Controller, Get, VERSION_NEUTRAL } from '@nestjs/common';

@Controller({
  version: VERSION_NEUTRAL,
})
export class CatsController {
  @Get('cats')
  findAll() {
    return '此操作会返回所有版本的猫';
  }
}
```

#### 全局默认版本

如果你不希望为每个控制器（Controller）或单独的路由（Route）都指定版本，或者你希望为所有未指定版本的控制器或路由设置一个特定的默认版本，可以按照如下方式设置 `defaultVersion`：

```typescript
@@filename(main)
app.enableVersioning({
  // ...
  defaultVersion: '1'
  // 或者
  defaultVersion: ['1', '2']
  // 或者
  defaultVersion: VERSION_NEUTRAL
});
```

#### 中间件版本控制

[中间件（Middleware）](https://docs.nestjs.com/middleware) 也可以利用版本控制元数据（versioning metadata），为特定路由的某个版本配置中间件。要实现这一点，只需在 `MiddlewareConsumer.forRoutes()` 方法的参数中提供版本号即可：

```typescript
@@filename(app.module)
import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { LoggerMiddleware } from './common/middleware/logger.middleware';
import { CatsModule } from './cats/cats.module';
import { CatsController } from './cats/cats.controller';

@Module({
  imports: [CatsModule],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(LoggerMiddleware)
      .forRoutes({ path: 'cats', method: RequestMethod.GET, version: '2' });
  }
}
```

如上代码所示，`LoggerMiddleware` 只会应用于 `/cats` 路径下版本为 '2' 的端点。

> info **注意** 中间件支持本节介绍的所有版本控制类型：`URI`、`Header`、`Media Type` 或 `Custom`。
