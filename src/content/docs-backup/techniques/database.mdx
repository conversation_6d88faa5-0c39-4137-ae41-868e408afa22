### 数据库

Nest 是一个与数据库无关的框架，允许你轻松集成任意 SQL 或 NoSQL 数据库。你可以根据自己的偏好选择多种集成方式。从最通用的层面来看，将 Nest 连接到数据库，其实就是像在 [Express](https://expressjs.com/en/guide/database-integration.html) 或 Fastify 一样，加载对应数据库的 Node.js 驱动即可。

你也可以直接使用任意通用的 Node.js 数据库集成**库**或 ORM（对象关系映射，Object Relational Mapper），比如 [MikroORM](https://mikro-orm.io/)（参见 [MikroORM 实践](/recipes/mikroorm)）、[Sequelize](https://sequelize.org/)（参见 [Sequelize 集成](/techniques/database#sequelize-integration)）、[Knex.js](https://knexjs.org/)（参见 [Knex.js 教程](https://dev.to/nestjs/build-a-nestjs-module-for-knex-js-or-other-resource-based-libraries-in-5-minutes-12an)）、[TypeORM](https://github.com/typeorm/typeorm) 以及 [Prisma](https://www.github.com/prisma/prisma)（参见 [Prisma 实践](/recipes/prisma)），以获得更高层次的抽象操作体验。

为了方便开发，Nest 针对 TypeORM 和 Sequelize 提供了开箱即用的深度集成，分别通过 `@nestjs/typeorm` 和 `@nestjs/sequelize` 包实现，本章将详细介绍这两种集成方式。对于 Mongoose 的集成，请参见[本章节](/techniques/mongodb)。这些集成不仅让你更容易访问所选数据库，还带来了如模型/仓库注入、可测试性、异步配置等 NestJS 特有功能。

### TypeORM 集成

在集成 SQL 或 NoSQL 数据库时，Nest 提供了 `@nestjs/typeorm` 包。[TypeORM](https://github.com/typeorm/typeorm) 是目前最成熟的 TypeScript 对象关系映射（ORM）库。由于其本身采用 TypeScript 编写，因此与 Nest 框架高度兼容。

要开始使用 TypeORM，首先需要安装相关依赖。本章以主流的 [MySQL](https://www.mysql.com/) 关系型数据库管理系统（Relational DBMS）为例进行演示，但 TypeORM 也支持多种关系型数据库，如 PostgreSQL、Oracle、Microsoft SQL Server、SQLite，甚至还支持 MongoDB 等 NoSQL 数据库。无论你选择哪种数据库，操作流程基本一致，只需安装对应数据库的客户端 API 库即可。

```bash
$ npm install --save @nestjs/typeorm typeorm mysql2
```

安装完成后，我们可以在根模块 `AppModule` 中导入 `TypeOrmModule`。

```typescript
@@filename(app.module)
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: 'localhost',
      port: 3306,
      username: 'root',
      password: 'root',
      database: 'test',
      entities: [],
      synchronize: true,
    }),
  ],
})
export class AppModule {}
```

> warning **警告** 生产环境下请勿使用 `synchronize: true`，否则可能导致生产数据丢失。

`forRoot()` 方法支持所有由 [TypeORM](https://typeorm.io/data-source-options#common-data-source-options) 包中 `DataSource` 构造函数暴露的配置项。此外，还提供了如下额外配置项：

<table>
  <tbody>
    <tr>
      <td>
        <code>retryAttempts</code>
      </td>
      <td>
        尝试连接数据库的最大次数（默认值：<code>10</code>）
      </td>
    </tr>
    <tr>
      <td>
        <code>retryDelay</code>
      </td>
      <td>
        每次重试连接的间隔时间（毫秒，默认值：<code>3000</code>）
      </td>
    </tr>
    <tr>
      <td>
        <code>autoLoadEntities</code>
      </td>
      <td>
        若设为 <code>true</code>，则会自动加载实体（默认值：<code>false</code>）
      </td>
    </tr>
  </tbody>
</table>

> info **提示** 你可以在 [这里](https://typeorm.io/data-source-options) 了解更多数据源配置项。

一旦完成上述配置，TypeORM 的 `DataSource` 和 `EntityManager` 对象就可以在整个项目中被注入使用（无需额外导入任何模块），例如：

```typescript
@@filename(app.module)
import { DataSource } from 'typeorm';

@Module({
  imports: [TypeOrmModule.forRoot(), UsersModule],
})
export class AppModule {
  constructor(private dataSource: DataSource) {}
}
@@switch
import { DataSource } from 'typeorm';

@Dependencies(DataSource)
@Module({
  imports: [TypeOrmModule.forRoot(), UsersModule],
})
export class AppModule {
  constructor(dataSource) {
    this.dataSource = dataSource;
  }
}
```

#### 仓库模式（Repository pattern）

[TypeORM](https://github.com/typeorm/typeorm) 支持**仓库设计模式（repository design pattern）**，因此每个实体（Entity）都有自己的仓库（Repository）。这些仓库可以通过数据库数据源（data source）获取。

继续上面的示例，我们至少需要一个实体。下面定义 `User` 实体：

```typescript
@@filename(user.entity)
import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  firstName: string;

  @Column()
  lastName: string;

  @Column({ default: true })
  isActive: boolean;
}
```

> info **提示** 你可以在 [TypeORM 文档](https://typeorm.io/#/entities) 中了解更多关于实体（Entity）的内容。

`User` 实体文件位于 `users` 目录。该目录包含了与 `UsersModule` 相关的所有文件。你可以自行决定模型文件的存放位置，但我们推荐将其放在靠近其**领域**的对应模块目录下。

要开始使用 `User` 实体，需要在模块的 `forRoot()` 方法选项中的 `entities` 数组里声明它（除非你使用了静态 glob 路径）：

```typescript
@@filename(app.module)
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './users/user.entity';

@Module({
  imports: [
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: 'localhost',
      port: 3306,
      username: 'root',
      password: 'root',
      database: 'test',
      entities: [User],
      synchronize: true,
    }),
  ],
})
export class AppModule {}
```

接下来，我们来看一下 `UsersModule`：

```typescript
@@filename(users.module)
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { User } from './user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([User])],
  providers: [UsersService],
  controllers: [UsersController],
})
export class UsersModule {}
```

该模块通过 `forFeature()` 方法声明了当前作用域内注册的仓库（Repository）。这样，我们就可以在 `UsersService` 中通过 `@InjectRepository()` 装饰器注入 `UsersRepository`：

```typescript
@@filename(users.service)
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './user.entity';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
  ) {}

  findAll(): Promise<User[]> {
    return this.usersRepository.find();
  }

  findOne(id: number): Promise<User | null> {
    return this.usersRepository.findOneBy({ id });
  }

  async remove(id: number): Promise<void> {
    await this.usersRepository.delete(id);
  }
}
@@switch
import { Injectable, Dependencies } from '@nestjs/common';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from './user.entity';

@Injectable()
@Dependencies(getRepositoryToken(User))
export class UsersService {
  constructor(usersRepository) {
    this.usersRepository = usersRepository;
  }

  findAll() {
    return this.usersRepository.find();
  }

  findOne(id) {
    return this.usersRepository.findOneBy({ id });
  }

  async remove(id) {
    await this.usersRepository.delete(id);
  }
}
```

> warning **注意** 别忘了在根模块 `AppModule` 中导入 `UsersModule`。

如果你希望在导入了 `TypeOrmModule.forFeature` 的模块之外使用该仓库，需要重新导出由其生成的 provider。你可以像下面这样导出整个模块：

```typescript
@@filename(users.module)
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([User])],
  exports: [TypeOrmModule]
})
export class UsersModule {}
```

现在，如果我们在 `UserHttpModule` 中导入 `UsersModule`，就可以在后者的 provider 中使用 `@InjectRepository(User)` 了。

```typescript
@@filename(users-http.module)
import { Module } from '@nestjs/common';
import { UsersModule } from './users.module';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';

@Module({
  imports: [UsersModule],
  providers: [UsersService],
  controllers: [UsersController]
})
export class UserHttpModule {}
```

#### 关系（Relations）

关系是指在两个或多个数据表之间建立的关联。关系通常基于每个表中的公共字段，通常涉及主键和外键。

关系类型主要有三种：

<table>
  <tbody>
    <tr>
      <td>
        <code>一对一</code>
      </td>
      <td>
        主表中的每一行都与外表中的唯一一行相关联。定义此类关系时，请使用 <code>@OneToOne()</code>{' '}
        装饰器（Decorator）。
      </td>
    </tr>
    <tr>
      <td>
        <code>一对多 / 多对一</code>
      </td>
      <td>
        主表中的每一行可以与外表中的一行或多行相关联。定义此类关系时，请使用{' '}
        <code>@OneToMany()</code> 和 <code>@ManyToOne()</code> 装饰器（Decorator）。
      </td>
    </tr>
    <tr>
      <td>
        <code>多对多</code>
      </td>
      <td>
        主表中的每一行可以与外表中的多行相关联，外表中的每一行也可以与主表中的多行相关联。定义此类关系时，请使用{' '}
        <code>@ManyToMany()</code> 装饰器（Decorator）。
      </td>
    </tr>
  </tbody>
</table>

在实体（Entity）中定义关系时，需要使用相应的装饰器（Decorator）。例如，如果要定义每个 `User`（用户）可以拥有多张照片，可以使用 `@OneToMany()` 装饰器（Decorator）。

```typescript
@@filename(user.entity)
import { Entity, Column, PrimaryGeneratedColumn, OneToMany } from 'typeorm';
import { Photo } from '../photos/photo.entity';

@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  firstName: string;

  @Column()
  lastName: string;

  @Column({ default: true })
  isActive: boolean;

  @OneToMany(type => Photo, photo => photo.user)
  photos: Photo[];
}
```

> info **提示**
> 想要了解更多关于 TypeORM 关系的内容，请访问 [TypeORM 官方文档](https://typeorm.io/#/relations)。

#### 自动加载实体

手动将实体（Entity）添加到数据源配置对象的 `entities` 数组中，过程繁琐。而且，如果在根模块（Root Module）中引用实体，会打破应用的领域边界，并导致实现细节泄漏到应用的其他部分。为了解决这个问题，Nest 提供了一种替代方案。你可以通过在配置对象（传递给 `forRoot()` 方法）中设置 `autoLoadEntities` 属性为 `true`，实现实体的自动加载，示例如下：

```typescript
@@filename(app.module)
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [
    TypeOrmModule.forRoot({
      ...
      autoLoadEntities: true,
    }),
  ],
})
export class AppModule {}
```

启用该选项后，通过 `forFeature()` 方法注册的每个实体都会被自动添加到配置对象的 `entities` 数组中。

> warning **警告** 请注意，仅通过实体间关系被引用、但未通过 `forFeature()` 方法注册的实体，不会被 `autoLoadEntities` 设置自动包含。

#### 分离实体定义

你可以直接在模型中使用装饰器（Decorator）定义实体（Entity）及其列（Column）。但有些开发者更倾向于将实体及其列定义在单独的文件中，这可以通过 [“实体模式（entity schemas）”](https://typeorm.io/#/separating-entity-definition) 实现。

```typescript
import { EntitySchema } from 'typeorm'
import { User } from './user.entity'

export const UserSchema = new EntitySchema<User>({
  name: 'User',
  target: User,
  columns: {
    id: {
      type: Number,
      primary: true,
      generated: true,
    },
    firstName: {
      type: String,
    },
    lastName: {
      type: String,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  relations: {
    photos: {
      type: 'one-to-many',
      target: 'Photo', // PhotoSchema 的名称
    },
  },
})
```

> warning error **警告** 如果你提供了 `target` 选项，则 `name` 选项的值必须与目标类的名称一致。
> 如果未提供 `target`，则可以使用任意名称。

Nest 允许你在任何需要实体（Entity）的地方使用 `EntitySchema` 实例，例如：

```typescript
import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { UserSchema } from './user.schema'
import { UsersController } from './users.controller'
import { UsersService } from './users.service'

@Module({
  imports: [TypeOrmModule.forFeature([UserSchema])],
  providers: [UsersService],
  controllers: [UsersController],
})
export class UsersModule {}
```

#### TypeORM 事务（Transactions）

数据库事务（Transaction）是指在数据库管理系统中针对数据库执行的一组操作，这组操作被视为一个整体，并且与其他事务相互独立，以确保数据的一致性和可靠性。事务通常代表数据库中的任意更改（[了解更多](https://en.wikipedia.org/wiki/Database_transaction)）。

处理 [TypeORM 事务](https://typeorm.io/#/transactions) 有多种策略。我们推荐使用 `QueryRunner` 类，因为它可以让你完全控制事务的执行过程。

首先，我们需要像常规方式一样，将 `DataSource` 对象注入到类中：

```typescript
@Injectable()
export class UsersService {
  constructor(private dataSource: DataSource) {}
}
```

> info **提示** `DataSource` 类需从 `typeorm` 包中导入。

现在，我们可以使用该对象来创建事务。

```typescript
async createMany(users: User[]) {
  const queryRunner = this.dataSource.createQueryRunner();

  await queryRunner.connect();
  await queryRunner.startTransaction();
  try {
    await queryRunner.manager.save(users[0]);
    await queryRunner.manager.save(users[1]);

    await queryRunner.commitTransaction();
  } catch (err) {
    // 如果发生错误，则回滚之前的更改
    await queryRunner.rollbackTransaction();
  } finally {
    // 手动实例化的 queryRunner 需要手动释放
    await queryRunner.release();
  }
}
```

> info **提示** 请注意，`dataSource` 仅用于创建 `QueryRunner`。然而，测试该类时需要模拟整个 `DataSource` 对象（它暴露了多个方法）。因此，我们建议使用一个辅助工厂类（如 `QueryRunnerFactory`），并定义一个只包含维护事务所需方法的接口。这样可以更方便地对这些方法进行模拟。

<app-banner-devtools></app-banner-devtools>

另外，你也可以使用 `DataSource` 对象的 `transaction` 方法，采用回调风格来处理事务（[阅读更多](https://typeorm.io/#/transactions/creating-and-using-transactions)）。

```typescript
async createMany(users: User[]) {
  await this.dataSource.transaction(async manager => {
    await manager.save(users[0]);
    await manager.save(users[1]);
  });
}
```

#### 订阅者（Subscribers）

使用 TypeORM 的 [订阅者（subscribers）](https://typeorm.io/#/listeners-and-subscribers/what-is-a-subscriber) ，你可以监听特定实体的事件。

```typescript
import { DataSource, EntitySubscriberInterface, EventSubscriber, InsertEvent } from 'typeorm'
import { User } from './user.entity'

@EventSubscriber()
export class UserSubscriber implements EntitySubscriberInterface<User> {
  constructor(dataSource: DataSource) {
    dataSource.subscribers.push(this)
  }

  listenTo() {
    return User
  }

  beforeInsert(event: InsertEvent<User>) {
    console.log(`BEFORE USER INSERTED: `, event.entity)
  }
}
```

> error **警告** 事件订阅者（Event subscribers）不能为[请求作用域](/fundamentals/injection-scopes)（request-scoped）。

现在，将 `UserSubscriber` 类添加到 `providers`（提供者）数组中：

```typescript
import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { User } from './user.entity'
import { UsersController } from './users.controller'
import { UsersService } from './users.service'
import { UserSubscriber } from './user.subscriber'

@Module({
  imports: [TypeOrmModule.forFeature([User])],
  providers: [UsersService, UserSubscriber],
  controllers: [UsersController],
})
export class UsersModule {}
```

> info **提示** 你可以在 [这里](https://typeorm.io/#/listeners-and-subscribers/what-is-a-subscriber) 了解更多关于实体订阅者的信息。

#### 数据库迁移（Migrations）

[数据库迁移（Migrations）](https://typeorm.io/#/migrations) 提供了一种方式，可以逐步更新数据库结构，使其与应用的数据模型保持同步，同时保留数据库中的现有数据。要生成、运行和回滚迁移，TypeORM 提供了专用的 [命令行工具（CLI）](https://typeorm.io/#/migrations/creating-a-new-migration)。

迁移类与 Nest 应用的源代码是分离的。它们的生命周期由 TypeORM 命令行工具（CLI）进行管理。因此，在迁移中无法使用依赖注入（Dependency Injection）以及其他 Nest 特有的功能。想要了解更多关于迁移的内容，请参考 [TypeORM 官方文档](https://typeorm.io/#/migrations/creating-a-new-migration) 中的相关指南。

#### 多数据源（Multiple databases）

在某些项目中，可能需要连接多个数据库。通过本模块也可以实现这一需求。要使用多个连接，首先需要分别创建这些连接。在这种情况下，数据源命名就变得**必不可少**。

假设你有一个 `Album` 实体（Entity），它存储在独立的数据库中。

```typescript
const defaultOptions = {
  type: 'postgres',
  port: 5432,
  username: 'user',
  password: 'password',
  database: 'db',
  synchronize: true,
}

@Module({
  imports: [
    TypeOrmModule.forRoot({
      ...defaultOptions,
      host: 'user_db_host',
      entities: [User],
    }),
    TypeOrmModule.forRoot({
      ...defaultOptions,
      name: 'albumsConnection',
      host: 'album_db_host',
      entities: [Album],
    }),
  ],
})
export class AppModule {}
```

> warning **注意** 如果你没有为数据源（data source）设置 `name`，则其名称会被设为 `default`。请注意，**不要**创建多个未命名或同名的连接，否则它们会被覆盖。

> warning **注意** 如果你使用 `TypeOrmModule.forRootAsync`，**也必须**在 `useFactory` 之外设置数据源名称。例如：
>
> ```typescript
> TypeOrmModule.forRootAsync({
>   name: 'albumsConnection',
>   useFactory: ...,
>   inject: ...,
> }),
> ```
>
> 详情可参考 [这个 issue](https://github.com/nestjs/typeorm/issues/86)。

此时，你已经将 `User` 和 `Album` 实体分别注册到了各自的数据源中。在这种配置下，你需要在调用 `TypeOrmModule.forFeature()` 方法和使用 `@InjectRepository()` 装饰器时，指定应使用哪个数据源。如果不传递数据源名称，则会默认使用 `default` 数据源。

```typescript
@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    TypeOrmModule.forFeature([Album], 'albumsConnection'),
  ],
})
export class AppModule {}
```

你还可以为指定数据源注入 `DataSource` 或 `EntityManager`：

```typescript
@Injectable()
export class AlbumsService {
  constructor(
    @InjectDataSource('albumsConnection')
    private dataSource: DataSource,
    @InjectEntityManager('albumsConnection')
    private entityManager: EntityManager
  ) {}
}
```

同样，也可以将任意 `DataSource` 注入到提供者（Provider）中：

```typescript
@Module({
  providers: [
    {
      provide: AlbumsService,
      useFactory: (albumsConnection: DataSource) => {
        return new AlbumsService(albumsConnection)
      },
      inject: [getDataSourceToken('albumsConnection')],
    },
  ],
})
export class AlbumsModule {}
```

#### 测试

在进行单元测试（Unit Testing）时，我们通常希望避免与数据库建立连接，从而保持测试套件的独立性，并尽可能加快测试执行速度。但我们的类可能依赖于从数据源（连接实例）中获取的仓库（Repository）。那么该如何处理这种情况呢？解决方案是创建模拟仓库（mock repository）。为此，我们需要设置[自定义提供者](/fundamentals/custom-providers)。每个已注册的仓库都会自动以 `<EntityName>Repository` 令牌（Token）的形式表示，其中 `EntityName` 是你的实体类名称。

`@nestjs/typeorm` 包提供了 `getRepositoryToken()` 函数，该函数会根据给定的实体返回一个预设的令牌。

```typescript
@Module({
  providers: [
    UsersService,
    {
      provide: getRepositoryToken(User),
      useValue: mockRepository,
    },
  ],
})
export class UsersModule {}
```

现在，`mockRepository` 替代品将作为 `UsersRepository` 使用。每当有类通过 `@InjectRepository()` 装饰器（Decorator）请求 `UsersRepository` 时，Nest 就会使用已注册的 `mockRepository` 对象。

#### 异步配置（Async configuration）

有时候，你可能希望以异步方式（而非静态方式）传递你的仓库模块选项。此时，可以使用 `forRootAsync()` 方法。该方法为异步配置提供了多种处理方式。

其中一种方式是使用工厂函数（factory function）：

```typescript
TypeOrmModule.forRootAsync({
  useFactory: () => ({
    type: 'mysql',
    host: 'localhost',
    port: 3306,
    username: 'root',
    password: 'root',
    database: 'test',
    entities: [],
    synchronize: true,
  }),
})
```

我们的工厂函数行为与其他[异步提供者（asynchronous provider）](https://docs.nestjs.com/fundamentals/async-providers)类似（例如，它可以是 `async`，并且能够通过 `inject` 注入依赖）。

```typescript
TypeOrmModule.forRootAsync({
  imports: [ConfigModule],
  useFactory: (configService: ConfigService) => ({
    type: 'mysql',
    host: configService.get('HOST'),
    port: +configService.get('PORT'),
    username: configService.get('USERNAME'),
    password: configService.get('PASSWORD'),
    database: configService.get('DATABASE'),
    entities: [],
    synchronize: true,
  }),
  inject: [ConfigService],
})
```

另外，你也可以使用 `useClass` 语法：

```typescript
TypeOrmModule.forRootAsync({
  useClass: TypeOrmConfigService,
})
```

上述写法会在 `TypeOrmModule` 内部实例化 `TypeOrmConfigService`，并通过调用 `createTypeOrmOptions()` 方法来提供配置对象。需要注意的是，这意味着 `TypeOrmConfigService` 必须实现 `TypeOrmOptionsFactory` 接口，如下所示：

```typescript
@Injectable()
export class TypeOrmConfigService implements TypeOrmOptionsFactory {
  createTypeOrmOptions(): TypeOrmModuleOptions {
    return {
      type: 'mysql',
      host: 'localhost',
      port: 3306,
      username: 'root',
      password: 'root',
      database: 'test',
      entities: [],
      synchronize: true,
    }
  }
}
```

如果你希望避免在 `TypeOrmModule` 内部创建 `TypeOrmConfigService`，而是复用从其他模块导入的提供者，可以使用 `useExisting` 语法。

```typescript
TypeOrmModule.forRootAsync({
  imports: [ConfigModule],
  useExisting: ConfigService,
})
```

这种写法与 `useClass` 类似，但有一个关键区别 —— `TypeOrmModule` 会在已导入的模块中查找并复用现有的 `ConfigService`，而不是新建一个实例。

> info **提示** 请确保 `name` 属性与 `useFactory`、`useClass` 或 `useValue` 属性处于同一级别。这样可以让 Nest 正确地在相应的注入令牌下注册数据源。

#### 自定义数据源工厂（Custom DataSource Factory）

结合使用 `useFactory`、`useClass` 或 `useExisting` 进行异步配置时，你还可以选择性地指定一个 `dataSourceFactory` 函数。通过该函数，你可以自行提供 TypeORM 数据源（DataSource），而不是让 `TypeOrmModule` 自动创建数据源。

`dataSourceFactory` 会接收通过 `useFactory`、`useClass` 或 `useExisting` 异步配置时传入的 TypeORM 数据源选项（DataSourceOptions），并返回一个解析为 TypeORM 数据源的 Promise。

```typescript
TypeOrmModule.forRootAsync({
  imports: [ConfigModule],
  inject: [ConfigService],
  // 使用 useFactory、useClass 或 useExisting
  // 来配置 DataSourceOptions。
  useFactory: (configService: ConfigService) => ({
    type: 'mysql',
    host: configService.get('HOST'),
    port: +configService.get('PORT'),
    username: configService.get('USERNAME'),
    password: configService.get('PASSWORD'),
    database: configService.get('DATABASE'),
    entities: [],
    synchronize: true,
  }),
  // dataSourceFactory 接收已配置的 DataSourceOptions
  // 并返回一个 Promise<DataSource>。
  dataSourceFactory: async (options) => {
    const dataSource = await new DataSource(options).initialize()
    return dataSource
  },
})
```

> info **提示** `DataSource` 类需从 `typeorm` 包中导入。

#### 示例

一个可用的示例项目可在 [此处](https://github.com/nestjs/nest/tree/master/sample/05-sql-typeorm) 查看。

<app-banner-enterprise></app-banner-enterprise>

### 集成 Sequelize

除了使用 TypeORM 之外，你还可以选择通过 `@nestjs/sequelize` 包集成 [Sequelize](https://sequelize.org/) ORM。此外，我们还会用到 [sequelize-typescript](https://github.com/RobinBuschmann/sequelize-typescript) 包，它提供了一系列装饰器（Decorator），可以用声明式的方式定义实体。

要开始使用 Sequelize，需要先安装相关依赖。本章节以主流的 [MySQL](https://www.mysql.com/) 关系型数据库管理系统（Relational DBMS）为例进行演示，但 Sequelize 也支持多种关系型数据库，如 PostgreSQL、MySQL、Microsoft SQL Server、SQLite 和 MariaDB。无论选择哪种数据库，操作流程基本一致，只需安装对应的数据库客户端 API 库即可。

```bash
$ npm install --save @nestjs/sequelize sequelize sequelize-typescript mysql2
$ npm install --save-dev @types/sequelize
```

依赖安装完成后，我们可以在根模块（AppModule）中导入 `SequelizeModule`。

```typescript
@@filename(app.module)
import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';

@Module({
  imports: [
    SequelizeModule.forRoot({
      dialect: 'mysql',
      host: 'localhost',
      port: 3306,
      username: 'root',
      password: 'root',
      database: 'test',
      models: [],
    }),
  ],
})
export class AppModule {}
```

`forRoot()` 方法支持 Sequelize 构造函数的所有配置项（[详细配置说明](https://sequelize.org/docs/v6/getting-started/#connecting-to-a-database)）。此外，还支持如下额外配置：

<table>
  <tbody>
    <tr>
      <td>
        <code>retryAttempts</code>
      </td>
      <td>
        尝试连接数据库的最大次数（默认值：<code>10</code>）
      </td>
    </tr>
    <tr>
      <td>
        <code>retryDelay</code>
      </td>
      <td>
        每次重试连接的间隔时间（毫秒），默认值为 <code>3000</code>
      </td>
    </tr>
    <tr>
      <td>
        <code>autoLoadModels</code>
      </td>
      <td>
        若设为 <code>true</code>，则会自动加载模型（默认值：<code>false</code>）
      </td>
    </tr>
    <tr>
      <td>
        <code>keepConnectionAlive</code>
      </td>
      <td>
        若设为 <code>true</code>，应用关闭时不会断开数据库连接（默认值：<code>false</code>）
      </td>
    </tr>
    <tr>
      <td>
        <code>synchronize</code>
      </td>
      <td>
        若设为 <code>true</code>，自动加载的模型会自动同步到数据库（默认值：<code>true</code>）
      </td>
    </tr>
  </tbody>
</table>

完成上述配置后，`Sequelize` 对象会在整个项目中自动可用（无需额外导入模块），例如：

```typescript
@@filename(app.service)
import { Injectable } from '@nestjs/common';
import { Sequelize } from 'sequelize-typescript';

@Injectable()
export class AppService {
  constructor(private sequelize: Sequelize) {}
}
@@switch
import { Injectable } from '@nestjs/common';
import { Sequelize } from 'sequelize-typescript';

@Dependencies(Sequelize)
@Injectable()
export class AppService {
  constructor(sequelize) {
    this.sequelize = sequelize;
  }
}
```

#### 模型（Models）

Sequelize 实现了 Active Record（活动记录）模式。采用这种模式时，你可以直接通过模型类与数据库进行交互。继续前面的示例，我们至少需要一个模型。下面我们来定义 `User` 模型。

```typescript
@@filename(user.model)
import { Column, Model, Table } from 'sequelize-typescript';

@Table
export class User extends Model {
  @Column
  firstName: string;

  @Column
  lastName: string;

  @Column({ defaultValue: true })
  isActive: boolean;
}
```

> info **提示** 你可以在 [这里](https://github.com/RobinBuschmann/sequelize-typescript#column) 了解更多可用的装饰器（Decorator）信息。

`User` 模型文件位于 `users` 目录。该目录包含了所有与 `UsersModule`（用户模块）相关的文件。你可以自行决定模型文件的存放位置，但我们推荐将其放在靠近其**领域**的对应模块目录下。

要开始使用 `User` 模型，需要通过在模块的 `forRoot()` 方法选项中的 `models` 数组插入该模型，让 Sequelize 知道它的存在：

```typescript
@@filename(app.module)
import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { User } from './users/user.model';

@Module({
  imports: [
    SequelizeModule.forRoot({
      dialect: 'mysql',
      host: 'localhost',
      port: 3306,
      username: 'root',
      password: 'root',
      database: 'test',
      models: [User],
    }),
  ],
})
export class AppModule {}
```

接下来，我们来看一下 `UsersModule`：

```typescript
@@filename(users.module)
import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { User } from './user.model';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';

@Module({
  imports: [SequelizeModule.forFeature([User])],
  providers: [UsersService],
  controllers: [UsersController],
})
export class UsersModule {}
```

该模块通过 `forFeature()` 方法定义了当前作用域内注册的模型。有了这个配置后，我们就可以在 `UsersService`（用户服务）中通过 `@InjectModel()` 装饰器注入 `User` 模型：

```typescript
@@filename(users.service)
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { User } from './user.model';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User)
    private userModel: typeof User,
  ) {}

  async findAll(): Promise<User[]> {
    return this.userModel.findAll();
  }

  findOne(id: string): Promise<User> {
    return this.userModel.findOne({
      where: {
        id,
      },
    });
  }

  async remove(id: string): Promise<void> {
    const user = await this.findOne(id);
    await user.destroy();
  }
}
@@switch
import { Injectable, Dependencies } from '@nestjs/common';
import { getModelToken } from '@nestjs/sequelize';
import { User } from './user.model';

@Injectable()
@Dependencies(getModelToken(User))
export class UsersService {
  constructor(usersRepository) {
    this.usersRepository = usersRepository;
  }

  async findAll() {
    return this.userModel.findAll();
  }

  findOne(id) {
    return this.userModel.findOne({
      where: {
        id,
      },
    });
  }

  async remove(id) {
    const user = await this.findOne(id);
    await user.destroy();
  }
}
```

> warning **注意** 别忘了将 `UsersModule`导入到根模块 `AppModule`中。

如果你希望在导入了 `SequelizeModule.forFeature` 的模块之外使用该模型的仓库（Repository），需要重新导出由其生成的提供者（Provider）。
你可以像下面这样导出整个模块：

```typescript
@@filename(users.module)
import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { User } from './user.entity';

@Module({
  imports: [SequelizeModule.forFeature([User])],
  exports: [SequelizeModule]
})
export class UsersModule {}
```

现在，如果我们在 `UserHttpModule`中导入 `UsersModule`，就可以在后者的提供者中使用 `@InjectModel(User)` 了。

```typescript
@@filename(users-http.module)
import { Module } from '@nestjs/common';
import { UsersModule } from './users.module';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';

@Module({
  imports: [UsersModule],
  providers: [UsersService],
  controllers: [UsersController]
})
export class UserHttpModule {}
```

#### 关系（Relations）

关系是指在两个或多个数据表之间建立的关联。关系通常基于每个表中的公共字段，通常涉及主键和外键。

关系主要分为三种类型：

<table>
  <tbody>
    <tr>
      <td>
        <code>一对一</code>
      </td>
      <td>主表中的每一行都与外表中的唯一一行相关联</td>
    </tr>
    <tr>
      <td>
        <code>一对多 / 多对一</code>
      </td>
      <td>主表中的每一行都与外表中的一行或多行相关联</td>
    </tr>
    <tr>
      <td>
        <code>多对多</code>
      </td>
      <td>主表中的每一行都与外表中的多行相关联，同时外表中的每一行也与主表中的多行相关联</td>
    </tr>
  </tbody>
</table>

在模型中定义关系时，需要使用相应的 **装饰器（Decorator）**。例如，如果要定义每个 `User`（用户）可以拥有多张照片，可以使用 `@HasMany()` 装饰器。

```typescript
@@filename(user.model)
import { Column, Model, Table, HasMany } from 'sequelize-typescript';
import { Photo } from '../photos/photo.model';

@Table
export class User extends Model {
  @Column
  firstName: string;

  @Column
  lastName: string;

  @Column({ defaultValue: true })
  isActive: boolean;

  @HasMany(() => Photo)
  photos: Photo[];
}
```

> info **提示**
> 想要了解更多关于 Sequelize 关联（association）的内容，请阅读[本章节](https://github.com/RobinBuschmann/sequelize-typescript#model-association)。

#### 自动加载模型

手动将模型（Model）添加到连接配置的 `models` 数组中既繁琐又容易出错。此外，在根模块（Root Module）中引用模型，会打破应用的领域边界，并导致实现细节泄漏到应用的其他部分。为了解决这个问题，可以通过在 `forRoot()` 方法的配置对象中同时设置 `autoLoadModels` 和 `synchronize` 属性为 `true`，实现自动加载模型，如下所示：

```typescript
@@filename(app.module)
import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';

@Module({
  imports: [
    SequelizeModule.forRoot({
      ...
      autoLoadModels: true,
      synchronize: true,
    }),
  ],
})
export class AppModule {}
```

启用该选项后，通过 `forFeature()` 方法注册的每个模型都会被自动添加到配置对象的 `models` 数组中。

> warning **警告** 请注意，仅通过模型间关联（association）被引用、但未通过 `forFeature()` 方法注册的模型，将不会被包含在内。

#### Sequelize 事务

数据库事务（Transaction）是指在数据库管理系统中针对数据库执行的一组操作，这组操作作为一个整体被处理，并且与其他事务相互独立，保证一致性和可靠性。事务通常代表数据库中的任意更改（[了解更多](https://en.wikipedia.org/wiki/Database_transaction)）。

处理 [Sequelize 事务](https://sequelize.org/docs/v6/other-topics/transactions/) 有多种策略。下面是一个受管事务（自动回调）的示例实现。

首先，我们需要像常规方式一样将 `Sequelize` 对象注入到类中：

```typescript
@Injectable()
export class UsersService {
  constructor(private sequelize: Sequelize) {}
}
```

> info **提示** `Sequelize` 类需从 `sequelize-typescript` 包中导入。

现在，我们可以使用该对象来创建事务。

```typescript
async createMany() {
  try {
    await this.sequelize.transaction(async t => {
      const transactionHost = { transaction: t };

      await this.userModel.create(
          { firstName: 'Abraham', lastName: 'Lincoln' },
          transactionHost,
      );
      await this.userModel.create(
          { firstName: 'John', lastName: 'Boothe' },
          transactionHost,
      );
    });
  } catch (err) {
    // 事务已回滚
    // err 是回调中 promise 链被拒绝时返回的错误
  }
}
```

> info **提示** 请注意，`Sequelize` 实例仅用于启动事务。然而，若要对该类进行测试，则需要模拟整个 `Sequelize` 对象（该对象暴露了多个方法）。因此，我们推荐使用一个辅助工厂类（如 `TransactionRunner`），并定义一个仅包含维护事务所需方法的接口。此技巧可以让模拟这些方法变得非常简单。

#### 迁移

[迁移（Migrations）](https://sequelize.org/docs/v6/other-topics/migrations/) 提供了一种逐步更新数据库结构的方法，可以让数据库结构与应用的数据模型保持同步，同时保留数据库中已有的数据。为了生成、执行和回滚迁移，Sequelize 提供了专用的 [命令行工具（CLI）](https://sequelize.org/docs/v6/other-topics/migrations/#installing-the-cli)。

迁移类与 Nest 应用的源代码是分离的。它们的生命周期由 Sequelize 命令行工具（CLI）进行管理。因此，在迁移中无法使用依赖注入（Dependency Injection）以及其他 Nest 特有的功能。如果你想了解更多关于迁移的内容，请参考 [Sequelize 官方文档](https://sequelize.org/docs/v6/other-topics/migrations/#installing-the-cli) 中的相关指南。

<app-banner-courses></app-banner-courses>

#### 多数据库连接

在某些项目中，可能需要连接多个数据库。通过本模块也可以实现这一需求。要使用多个连接，首先需要创建这些连接。在这种情况下，**必须**为每个连接命名。

假设你有一个 `Album` 实体，它存储在独立的数据库中。

```typescript
const defaultOptions = {
  dialect: 'postgres',
  port: 5432,
  username: 'user',
  password: 'password',
  database: 'db',
  synchronize: true,
}

@Module({
  imports: [
    SequelizeModule.forRoot({
      ...defaultOptions,
      host: 'user_db_host',
      models: [User],
    }),
    SequelizeModule.forRoot({
      ...defaultOptions,
      name: 'albumsConnection',
      host: 'album_db_host',
      models: [Album],
    }),
  ],
})
export class AppModule {}
```

> warning **注意** 如果你没有为连接设置 `name`，则该连接的名称会被设置为 `default`。请注意，不应存在多个未命名或同名的连接，否则它们会被覆盖。

此时，你已经将 `User` 和 `Album` 模型分别注册到了各自的连接中。在这种设置下，你需要在 `SequelizeModule.forFeature()` 方法和 `@InjectModel()` 装饰器（Decorator）中指定要使用的连接名称。如果没有传递连接名称，则会使用 `default` 连接。

```typescript
@Module({
  imports: [
    SequelizeModule.forFeature([User]),
    SequelizeModule.forFeature([Album], 'albumsConnection'),
  ],
})
export class AppModule {}
```

你还可以为指定连接注入 `Sequelize` 实例：

```typescript
@Injectable()
export class AlbumsService {
  constructor(
    @InjectConnection('albumsConnection')
    private sequelize: Sequelize
  ) {}
}
```

同样，也可以将任意 `Sequelize` 实例注入到提供者中：

```typescript
@Module({
  providers: [
    {
      provide: AlbumsService,
      useFactory: (albumsSequelize: Sequelize) => {
        return new AlbumsService(albumsSequelize)
      },
      inject: [getDataSourceToken('albumsConnection')],
    },
  ],
})
export class AlbumsModule {}
```

#### 测试

在进行单元测试时，我们通常希望避免实际连接数据库，这样可以让测试套件保持独立，并且执行过程尽可能快。然而，我们的类可能依赖于从连接实例中获取的模型。那该如何处理呢？解决方案是创建模拟模型。为此，我们需要设置[自定义提供者](/fundamentals/custom-providers)。每个已注册的模型都会自动对应一个 `<模型名>Model` 的注入令牌（Injection Token），其中 `模型名` 是你的模型类名。

`@nestjs/sequelize` 包提供了 `getModelToken()` 函数，该函数会根据给定的模型返回一个准备好的注入令牌。

```typescript
@Module({
  providers: [
    UsersService,
    {
      provide: getModelToken(User),
      useValue: mockModel,
    },
  ],
})
export class UsersModule {}
```

现在，`mockModel` 替代品会被用作 `UserModel`。当任何类通过 `@InjectModel()` 装饰器请求 `UserModel` 时，Nest 就会使用已注册的 `mockModel` 对象。

#### 异步配置

有时你可能希望以异步方式而非静态方式传递 `SequelizeModule` 的选项。此时，可以使用 `forRootAsync()` 方法，该方法提供了多种处理异步配置的方式。

其中一种方式是使用工厂函数：

```typescript
SequelizeModule.forRootAsync({
  useFactory: () => ({
    dialect: 'mysql',
    host: 'localhost',
    port: 3306,
    username: 'root',
    password: 'root',
    database: 'test',
    models: [],
  }),
})
```

我们的工厂函数行为与其他[异步提供者](https://docs.nestjs.com/fundamentals/async-providers)类似（例如，它可以是 `async`，并且能够通过 `inject` 注入依赖）。

```typescript
SequelizeModule.forRootAsync({
  imports: [ConfigModule],
  useFactory: (configService: ConfigService) => ({
    dialect: 'mysql',
    host: configService.get('HOST'),
    port: +configService.get('PORT'),
    username: configService.get('USERNAME'),
    password: configService.get('PASSWORD'),
    database: configService.get('DATABASE'),
    models: [],
  }),
  inject: [ConfigService],
})
```

另外，你也可以使用 `useClass` 语法：

```typescript
SequelizeModule.forRootAsync({
  useClass: SequelizeConfigService,
})
```

上述写法会在 `SequelizeModule` 内部实例化 `SequelizeConfigService`，并通过调用 `createSequelizeOptions()` 方法来提供配置对象。请注意，这意味着 `SequelizeConfigService` 需要实现 `SequelizeOptionsFactory` 接口，如下所示：

```typescript
@Injectable()
class SequelizeConfigService implements SequelizeOptionsFactory {
  createSequelizeOptions(): SequelizeModuleOptions {
    return {
      dialect: 'mysql',
      host: 'localhost',
      port: 3306,
      username: 'root',
      password: 'root',
      database: 'test',
      models: [],
    }
  }
}
```

如果你希望避免在 `SequelizeModule` 内部创建 `SequelizeConfigService`，而是复用从其他模块导入的提供者，可以使用 `useExisting` 语法。

```typescript
SequelizeModule.forRootAsync({
  imports: [ConfigModule],
  useExisting: ConfigService,
})
```

这种写法与 `useClass` 类似，但有一个关键区别——`SequelizeModule` 会在已导入的模块中查找并复用现有的 `ConfigService`，而不是新建一个实例。

#### 示例

可用的完整示例请参考[这里](https://github.com/nestjs/nest/tree/master/sample/07-sequelize)。
