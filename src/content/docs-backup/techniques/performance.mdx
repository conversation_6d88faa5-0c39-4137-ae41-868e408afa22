### 性能优化（Fastify）

默认情况下，Nest 使用 [Express](https://expressjs.com/) 框架。如前文所述，Nest 也支持与其他库兼容，例如 [Fastify](https://github.com/fastify/fastify)。Nest 通过实现一个框架适配器（framework adapter），实现了对框架的无关性。适配器的主要作用是将中间件和处理器代理到对应库的实现。

> info **提示** 请注意，要实现一个框架适配器，目标库必须提供类似于 Express 的请求/响应管道处理机制。

[Fastify](https://github.com/fastify/fastify) 是 Nest 的一个优秀替代框架，因为它在设计上与 Express 类似地解决了许多问题。然而，Fastify 的性能比 Express 更快，基准测试结果几乎是 Express 的两倍。那为什么 Nest 默认使用 Express 作为 HTTP 服务器呢？原因在于 Express 被广泛使用、知名度高，并且拥有大量可用的中间件，这些中间件可以直接被 Nest 用户使用。

但由于 Nest 实现了框架无关性，你可以轻松地在它们之间切换。当你非常重视高性能时，Fastify 可能是更好的选择。要使用 Fastify，只需像本章所示选择内置的 `FastifyAdapter` 即可。

#### 安装

首先，我们需要安装所需的包：

```bash
$ npm i --save @nestjs/platform-fastify
```

#### 适配器

安装 Fastify 平台后，我们就可以使用 `FastifyAdapter` 了。

```typescript
@@filename(main)
import { NestFactory } from '@nestjs/core';
import {
  FastifyAdapter,
  NestFastifyApplication,
} from '@nestjs/platform-fastify';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter()
  );
  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
```

默认情况下，Fastify 只监听 `localhost 127.0.0.1` 接口（[详细说明](https://www.fastify.io/docs/latest/Guides/Getting-Started/#your-first-server)）。如果你希望在其他主机上接受连接，需要在 `listen()` 方法中指定 `'0.0.0.0'`：

```typescript
async function bootstrap() {
  const app = await NestFactory.create<NestFastifyApplication>(AppModule, new FastifyAdapter())
  await app.listen(3000, '0.0.0.0')
}
```

#### 平台专用包

请注意，当你使用 `FastifyAdapter` 时，Nest 会将 Fastify 作为 **HTTP 服务器**。这意味着所有依赖于 Express 的用法在 Fastify 下可能无法正常工作。你应该使用 Fastify 的等效包来实现相同功能。

#### 重定向响应

Fastify 处理重定向响应的方式与 Express 略有不同。要在 Fastify 中实现正确的重定向，需要同时返回状态码和 URL，示例如下：

```typescript
@Get()
index(@Res() res) {
  res.status(302).redirect('/login');
}
```

#### Fastify 配置项

你可以通过 `FastifyAdapter` 构造函数向 Fastify 构造函数传递配置项。例如：

```typescript
new FastifyAdapter({ logger: true })
```

#### 中间件

中间件函数会获取原始的 `req` 和 `res` 对象，而不是 Fastify 的封装对象。这是因为底层使用了 `middie` 包以及 `fastify` 的机制。详细信息可参考 [Fastify 官方文档](https://www.fastify.io/docs/latest/Reference/Middleware/)。

```typescript
@@filename(logger.middleware)
import { Injectable, NestMiddleware } from '@nestjs/common';
import { FastifyRequest, FastifyReply } from 'fastify';

@Injectable()
export class LoggerMiddleware implements NestMiddleware {
  use(req: FastifyRequest['raw'], res: FastifyReply['raw'], next: () => void) {
    console.log('Request...');
    next();
  }
}
@@switch
import { Injectable } from '@nestjs/common';

@Injectable()
export class LoggerMiddleware {
  use(req, res, next) {
    console.log('Request...');
    next();
  }
}
```

#### 路由配置（Route Config）

你可以结合 Fastify 的 [路由配置](https://fastify.dev/docs/latest/Reference/Routes/#config) 功能，配合 `@RouteConfig()` 装饰器来使用。

```typescript
@RouteConfig({ output: 'hello world' })
@Get()
index(@Req() req) {
  return req.routeConfig.output;
}
```

#### 路由约束（Route Constraints）

自 v10.3.0 起，`@nestjs/platform-fastify` 支持 Fastify 的 [路由约束](https://fastify.dev/docs/latest/Reference/Routes/#constraints) 功能，可以通过 `@RouteConstraints` 装饰器实现。

```typescript
@RouteConstraints({ version: '1.2.x' })
newFeature() {
  return '此功能仅适用于版本 >= 1.2.x';
}
```

> info **提示** `@RouteConfig()` 和 `@RouteConstraints` 需从 `@nestjs/platform-fastify` 导入。

#### 示例

你可以在 [这里](https://github.com/nestjs/nest/tree/master/sample/10-fastify) 查看完整示例。
