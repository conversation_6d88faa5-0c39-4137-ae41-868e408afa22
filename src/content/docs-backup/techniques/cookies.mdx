### Cookie（HTTP Cookie）

**HTTP Cookie**（HTTP Cookie）是一小段由用户浏览器存储的数据。Cookie 设计之初是为了让网站能够可靠地记住有状态的信息。当用户再次访问网站时，Cookie 会自动随请求一起发送。

#### 在 Express（默认）中使用

首先安装[所需的包](https://github.com/expressjs/cookie-parser)（TypeScript 用户还需安装类型声明）：

```shell
$ npm i cookie-parser
$ npm i -D @types/cookie-parser
```

安装完成后，将 `cookie-parser` 中间件（Middleware）作为全局中间件应用（例如，在你的 `main.ts` 文件中）：

```typescript
import * as cookieParser from 'cookie-parser'
// 在初始化文件中某处
app.use(cookieParser())
```

你可以为 `cookieParser` 中间件传递多个选项：

- `secret`：用于签名 Cookie 的字符串或字符串数组。该参数为可选项，如果未指定，则不会解析已签名的 Cookie。如果提供字符串，则该字符串作为密钥使用；如果提供数组，则会依次尝试用每个密钥对 Cookie 进行解签。
- `options`：一个对象，会作为第二个参数传递给 `cookie.parse`。更多信息请参见 [cookie](https://www.npmjs.org/package/cookie)。

该中间件会解析请求头（Header）中的 `Cookie`，并将 Cookie 数据暴露为 `req.cookies` 属性。如果提供了密钥，还会暴露为 `req.signedCookies` 属性。这些属性都是以 Cookie 名称为键、Cookie 值为值的键值对。

当提供了密钥时，该模块会对所有已签名的 Cookie 值进行解签和校验，并将这些键值对从 `req.cookies` 移动到 `req.signedCookies`。已签名的 Cookie 是指值以 `s:` 前缀开头的 Cookie。如果签名校验失败，对应的 Cookie 值会被设置为 `false`，而不是被篡改的值。

有了这些配置后，你就可以在路由处理器（Route Handler）中读取 Cookie，例如：

```typescript
@Get()
findAll(@Req() request: Request) {
  console.log(request.cookies); // 或 "request.cookies['cookieKey']"
  // 或 console.log(request.signedCookies);
}
```

> info **提示** `@Req()` 装饰器（Decorator）从 `@nestjs/common` 导入，`Request` 类型从 `express` 包导入。

要在响应对象（Response Object）中设置 Cookie，可以使用 `Response#cookie()` 方法：

```typescript
@Get()
findAll(@Res({ passthrough: true }) response: Response) {
  response.cookie('key', 'value')
}
```

> warning **警告** 如果你希望将响应处理逻辑交由框架处理，请务必如上所示将 `passthrough` 选项设置为 `true`。详细说明请参见[此处](/controllers#library-specific-approach)。

> info **提示** `@Res()` 装饰器从 `@nestjs/common` 导入，`Response` 类型从 `express` 包导入。

#### 在 Fastify 中使用

首先安装所需的依赖包：

```shell
$ npm i @fastify/cookie
```

安装完成后，需要注册 `@fastify/cookie` 插件：

```typescript
import fastifyCookie from '@fastify/cookie'

// 在你的初始化文件中
const app = await NestFactory.create<NestFastifyApplication>(AppModule, new FastifyAdapter())
await app.register(fastifyCookie, {
  secret: 'my-secret', // 用于 Cookie 签名
})
```

完成上述配置后，你就可以在路由处理器中读取 Cookie 了，示例如下：

```typescript
@Get()
findAll(@Req() request: FastifyRequest) {
  console.log(request.cookies); // 或 "request.cookies['cookieKey']"
}
```

> info **提示** `@Req()` 装饰器（Decorator）需从 `@nestjs/common` 导入，`FastifyRequest` 则来自 `fastify` 包。

如果需要在响应中设置 Cookie，可以使用 `FastifyReply#setCookie()` 方法：

```typescript
@Get()
findAll(@Res({ passthrough: true }) response: FastifyReply) {
  response.setCookie('key', 'value')
}
```

如需了解更多关于 `FastifyReply#setCookie()` 方法的信息，请参阅 [此页面](https://github.com/fastify/fastify-cookie#sending)。

> warning **警告** 如果你希望将响应处理逻辑交由框架处理，请务必如上例所示，将 `passthrough` 选项设置为 `true`。详细说明可参考 [这里](/controllers#library-specific-approach)。

> info **提示** `@Res()` 装饰器需从 `@nestjs/common` 导入，`FastifyReply` 则来自 `fastify` 包。

#### 创建自定义装饰器（跨平台通用）

为了让访问请求中的 Cookie 更加便捷和声明式，我们可以创建一个[自定义装饰器](/custom-decorators)。

```typescript
import { createParamDecorator, ExecutionContext } from '@nestjs/common'

export const Cookies = createParamDecorator((data: string, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest()
  return data ? request.cookies?.[data] : request.cookies
})
```

`@Cookies()` 装饰器会从 `req.cookies` 对象中提取所有 Cookie，或根据参数提取指定名称的 Cookie，并将其赋值给被装饰的参数。

有了这个装饰器后，我们可以在路由处理器方法签名中这样使用：

```typescript
@Get()
findAll(@Cookies('name') name: string) {}
```
