### 配置

应用程序通常会运行在不同的 **环境（Environment）** 下。根据环境的不同，应当使用不同的配置设置。例如，本地环境通常依赖于只对本地数据库实例有效的特定数据库凭据，而生产环境则会使用另一套数据库凭据。由于配置变量会发生变化，最佳实践是将[配置变量](https://12factor.net/config)存储在环境变量（Environment Variable）中。

在 Node.js 中，通过全局对象 `process.env` 可以访问外部定义的环境变量。我们可以尝试在每个环境中分别设置环境变量来解决多环境配置的问题。但这种方式在开发和测试环境下很快会变得难以管理，尤其是当这些值需要频繁模拟或更改时。

在 Node.js 应用中，通常会使用 `.env` 文件来管理环境变量。每个 `.env` 文件以键值对的形式保存变量，每个键代表一个特定的配置项。切换不同环境时，只需替换对应的 `.env` 文件即可。

在 Nest 中，推荐的做法是创建一个 `配置模块（ConfigModule）`，并通过 `配置服务（ConfigService）` 加载对应的 `.env` 文件。虽然你可以自行实现这样的模块，但 Nest 已经内置提供了 `@nestjs/config` 包，极大简化了配置管理。本章节将详细介绍该包的用法。

#### 安装

要开始使用，首先需要安装相关依赖：

```bash
$ npm i --save @nestjs/config
```

> info **提示** `@nestjs/config` 包内部依赖了 [dotenv](https://github.com/motdotla/dotenv)。

> warning **注意** `@nestjs/config` 需要 TypeScript 4.1 或更高版本。

#### 快速上手

安装完成后，我们可以导入 `ConfigModule`。通常会在根模块 `AppModule` 中导入，并通过其静态方法 `.forRoot()` 控制行为。在这一步，环境变量的键值对会被解析和加载。后续我们还会介绍如何在其他功能模块中访问 `ConfigModule` 提供的 `ConfigService`。

```typescript
@@filename(app.module)
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [ConfigModule.forRoot()],
})
export class AppModule {}
```

上述代码会从默认位置（项目根目录）加载并解析 `.env` 文件，将 `.env` 文件中的键值对与 `process.env` 中已有的环境变量合并，并将结果存储在一个私有结构中，供 `ConfigService` 访问。`forRoot()` 方法会注册 `ConfigService` 提供者（Provider），该服务通过 `get()` 方法读取已解析和合并的配置变量。由于 `@nestjs/config` 依赖 [dotenv](https://github.com/motdotla/dotenv)，因此变量冲突时会遵循 dotenv 的解析规则：如果某个键同时存在于运行时环境变量（如通过 shell 导出 `export DATABASE_USER=test`）和 `.env` 文件中，则以运行时环境变量为准。

一个示例 `.env` 文件如下：

```json
DATABASE_USER=test
DATABASE_PASSWORD=test
```

如果你需要在 `ConfigModule` 加载和 Nest 应用启动之前就能访问某些环境变量（例如，需要将微服务配置传递给 `NestFactory#createMicroservice` 方法），可以使用 Nest CLI 的 `--env-file` 选项。该选项允许你在应用启动前指定要加载的 `.env` 文件路径。`--env-file` 标志在 Node v20 中引入，详见[官方文档](https://nodejs.org/dist/v20.18.1/docs/api/cli.html#--env-fileconfig)。

```bash
$ nest start --env-file .env
```

#### 自定义 env 文件路径

默认情况下，`@nestjs/config` 包会在应用程序根目录查找 `.env` 文件。如果你希望指定其他路径，可以在传递给 `forRoot()` 的选项对象中设置 `envFilePath` 属性，例如：

```typescript
ConfigModule.forRoot({
  envFilePath: '.development.env',
})
```

你也可以同时指定多个 `.env` 文件路径，如下所示：

```typescript
ConfigModule.forRoot({
  envFilePath: ['.env.development.local', '.env.development'],
})
```

如果同一个变量在多个文件中都存在，则以第一个文件中的值为准。

#### 禁用 env 文件加载

如果你不希望加载 `.env` 文件，而是只想直接访问运行环境中的环境变量（例如通过 shell 导出的 `export DATABASE_USER=test`），可以将选项对象的 `ignoreEnvFile` 属性设置为 `true`，如下所示：

```typescript
ConfigModule.forRoot({
  ignoreEnvFile: true,
})
```

#### 全局使用配置模块

如果你希望在其他模块中使用 `配置模块（ConfigModule）`，通常需要在每个模块中单独导入。或者，你也可以通过将选项对象的 `isGlobal` 属性设置为 `true`，将其声明为[全局模块](https://docs.nestjs.com/modules#global-modules)。这样一来，只需在根模块（如 `AppModule`）中导入一次，后续其他模块无需再单独导入。

```typescript
ConfigModule.forRoot({
  isGlobal: true,
})
```

#### 自定义配置文件

对于更复杂的项目，你可以使用自定义配置文件，返回嵌套的配置对象。这样可以按功能（如数据库相关配置）对配置进行分组，并将相关配置分别存放在独立文件中，便于维护和管理。

自定义配置文件需要导出一个工厂函数，返回一个配置对象。该对象可以是任意嵌套的普通 JavaScript 对象。此时，`process.env` 对象中会包含所有已解析的环境变量（包括 `.env` 文件和外部定义的变量，合并方式见[上文](techniques/configuration#getting-started)）。你可以在工厂函数中添加类型转换、默认值等逻辑。例如：

```typescript
@@filename(config/configuration)
export default () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  database: {
    host: process.env.DATABASE_HOST,
    port: parseInt(process.env.DATABASE_PORT, 10) || 5432
  }
});
```

我们可以通过在传递给 `ConfigModule.forRoot()` 的选项对象中设置 `load` 属性来加载该文件：

```typescript
import configuration from './config/configuration'

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [configuration],
    }),
  ],
})
export class AppModule {}
```

> info **提示** 赋值给 `load` 属性的是一个数组，因此你可以同时加载多个配置文件（如 `load: [databaseConfig, authConfig]`）。

使用自定义配置文件时，也可以管理如 YAML 等自定义格式的文件。以下是一个使用 YAML 格式的配置示例：

```yaml
http:
  host: 'localhost'
  port: 8080

db:
  postgres:
    url: 'localhost'
    port: 5432
    database: 'yaml-db'

  sqlite:
    database: 'sqlite.db'
```

要读取和解析 YAML 文件，可以借助 `js-yaml` 包。

```bash
$ npm i js-yaml
$ npm i -D @types/js-yaml
```

安装完成后，可以使用 `yaml#load` 方法加载刚刚创建的 YAML 文件：

```typescript
@@filename(config/configuration)
import { readFileSync } from 'fs';
import * as yaml from 'js-yaml';
import { join } from 'path';

const YAML_CONFIG_FILENAME = 'config.yaml';

export default () => {
  return yaml.load(
    readFileSync(join(__dirname, YAML_CONFIG_FILENAME), 'utf8'),
  ) as Record<string, any>;
};
```

> warning **注意** Nest CLI 在构建过程中不会自动将你的"资源文件"（非 TS 文件）移动到 `dist` 目录。为了确保 YAML 文件被正确复制，需要在 `nest-cli.json` 的 `compilerOptions#assets` 字段中进行配置。例如，如果 `config` 文件夹与 `src` 文件夹同级，可以这样设置：`"assets": [{{ '{' }}"include": "../config/*.yaml", "outDir": "./dist/config"{{ '}' }}]`。详细说明可参考[官方文档](/cli/monorepo#assets)。

需要注意的是，即使你在 `ConfigModule` 中使用了 `validationSchema` 选项，配置文件本身也不会被自动校验。如果你需要校验或转换配置内容，需要在工厂函数内部自行处理，这样可以完全自定义校验逻辑。

例如，如果你希望确保端口号在某个范围内，可以在工厂函数中添加校验步骤：

```typescript
@@filename(config/configuration)
export default () => {
  const config = yaml.load(
    readFileSync(join(__dirname, YAML_CONFIG_FILENAME), 'utf8'),
  ) as Record<string, any>;

  if (config.http.port < 1024 || config.http.port > 49151) {
    throw new Error('HTTP 端口号必须在 1024 到 49151 之间');
  }

  return config;
};
```

这样，如果端口号超出指定范围，应用会在启动时抛出错误。

<app-banner-devtools></app-banner-devtools>

#### 使用 `配置服务（ConfigService）`

要访问 `配置服务（ConfigService）` 中的配置信息，首先需要注入 `ConfigService`。和其他提供者（Provider）一样，需要在使用它的模块中导入其所在的 `配置模块（ConfigModule）`（除非你在 `ConfigModule.forRoot()` 的选项对象中将 `isGlobal` 属性设置为 `true`）。在功能模块中导入方式如下：

```typescript
@@filename(feature.module)
@Module({
  imports: [ConfigModule],
  // ...
})
```

然后可以通过标准的构造函数注入方式注入 `ConfigService`：

```typescript
constructor(private configService: ConfigService) {}
```

> info **提示** `ConfigService` 需从 `@nestjs/config` 包中导入。

在类中即可这样使用：

```typescript
// 获取环境变量
const dbUser = this.configService.get<string>('DATABASE_USER')

// 获取自定义配置项
const dbHost = this.configService.get<string>('database.host')
```

如上所示，使用 `configService.get()` 方法并传入变量名即可获取简单的环境变量。你还可以通过传递类型参数（如 `get<string>(...)`）获得 TypeScript 类型提示。对于嵌套的自定义配置对象（通过[自定义配置文件](techniques/configuration#custom-configuration-files)创建），同样可以通过点号语法访问。

你还可以通过接口类型提示获取整个嵌套的自定义配置对象：

```typescript
interface DatabaseConfig {
  host: string
  port: number
}

const dbConfig = this.configService.get<DatabaseConfig>('database')

// 现在可以直接使用 dbConfig.port 和 dbConfig.host
const port = dbConfig.port
```

`get()` 方法还支持可选的第二个参数，用于指定默认值（当对应 key 不存在时返回该值）：

```typescript
// 当未定义 database.host 时，使用 "localhost" 作为默认值
const dbHost = this.configService.get<string>('database.host', 'localhost')
```

`ConfigService` 支持两个可选泛型参数。第一个用于防止访问不存在的配置属性，示例如下：

```typescript
interface EnvironmentVariables {
  PORT: number;
  TIMEOUT: string;
}

// 代码片段
constructor(private configService: ConfigService<EnvironmentVariables>) {
  const port = this.configService.get('PORT', { infer: true });

  // TypeScript 错误：URL 属性未在 EnvironmentVariables 中定义
  const url = this.configService.get('URL', { infer: true });
}
```

当 `infer` 属性设置为 `true` 时，`ConfigService#get` 方法会自动根据接口推断属性类型。例如，`PORT` 在接口中为 `number` 类型，则 `typeof port === "number"`（如果未启用 TypeScript 的 `strictNullChecks`）。

此外，借助 `infer` 特性，即使使用点号语法访问嵌套自定义配置对象的属性，也能自动推断类型：

```typescript
constructor(private configService: ConfigService<{ database: { host: string } }>) {
  const dbHost = this.configService.get('database.host', { infer: true })!;
  // typeof dbHost === "string"                                          |
  //                                                                     +--> 非空断言操作符
}
```

第二个泛型参数依赖于第一个，用于断言类型，去除 `ConfigService` 方法在启用 `strictNullChecks` 时可能返回的 `undefined` 类型。例如：

```typescript
// ...
constructor(private configService: ConfigService<{ PORT: number }, true>) {
  //                                                               ^^^^
  const port = this.configService.get('PORT', { infer: true });
  //    ^^^ 此时 port 的类型为 number，无需再做类型断言
}
```

> info **提示** 如果希望 `ConfigService#get` 方法只从自定义配置文件获取值而忽略 `process.env` 变量，可以在 `ConfigModule.forRoot()` 的选项对象中设置 `skipProcessEnv: true`。

#### 配置命名空间（Configuration namespaces）

`配置模块（ConfigModule）` 允许你像前文[自定义配置文件](techniques/configuration#custom-configuration-files)所述一样，定义并加载多个自定义配置文件。你可以通过嵌套配置对象来管理复杂的配置层级结构。除此之外，还可以通过 `registerAs()` 函数返回一个"命名空间配置对象（namespaced configuration object）"，示例如下：

```typescript
@@filename(config/database.config)
export default registerAs('database', () => ({
  host: process.env.DATABASE_HOST,
  port: process.env.DATABASE_PORT || 5432
}));
```

与自定义配置文件类似，在 `registerAs()` 工厂函数内部，`process.env` 对象会包含所有已解析的环境变量（包括 `.env` 文件和外部定义的变量，合并方式见[上文](techniques/configuration#getting-started)）。

> info **提示** `registerAs` 函数由 `@nestjs/config` 包导出。

要加载命名空间配置，可以像加载自定义配置文件一样，将其传递给 `forRoot()` 方法选项对象的 `load` 属性：

```typescript
import databaseConfig from './config/database.config'

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [databaseConfig],
    }),
  ],
})
export class AppModule {}
```

此时，如果要从 `database` 命名空间获取 `host` 配置项，可以使用点号语法，前缀为命名空间名称（即 `registerAs()` 的第一个参数）：

```typescript
const dbHost = this.configService.get<string>('database.host')
```

另一种常用方式是直接注入 `database` 命名空间，这样可以获得更强的类型提示：

```typescript
constructor(
  @Inject(databaseConfig.KEY)
  private dbConfig: ConfigType<typeof databaseConfig>,
) {}
```

> info **提示** `ConfigType` 由 `@nestjs/config` 包导出。

#### 在模块中使用命名空间配置

如果你希望将命名空间配置作为其他模块的配置对象，可以使用配置对象的 `.asProvider()` 方法。该方法会将命名空间配置转换为一个提供者（Provider），然后可以将其传递给目标模块的 `forRootAsync()`（或类似方法）。

示例：

```typescript
import databaseConfig from './config/database.config';

@Module({
  imports: [
    TypeOrmModule.forRootAsync(databaseConfig.asProvider()),
  ],
})
```

要理解 `.asProvider()` 方法的作用，可以看下它的返回值结构：

```typescript
// .asProvider() 方法的返回值
{
  imports: [ConfigModule.forFeature(databaseConfig)],
  useFactory: (configuration: ConfigType<typeof databaseConfig>) => configuration,
  inject: [databaseConfig.KEY]
}
```

这种结构可以让你无缝地将命名空间配置集成到各个模块中，保证应用结构清晰、模块化，无需编写重复样板代码。

#### 缓存环境变量（Cache environment variables）

由于访问 `process.env` 可能存在性能瓶颈，你可以在传递给 `ConfigModule.forRoot()` 的选项对象中设置 `cache` 属性，以提升 `ConfigService#get` 方法读取环境变量时的性能。

```typescript
ConfigModule.forRoot({
  cache: true,
})
```

#### 局部注册（Partial registration）

目前为止，我们通常在根模块（如 `AppModule`）中通过 `forRoot()` 方法加载所有配置文件。但如果你的项目结构较为复杂，存在多个特性模块分别拥有各自的配置文件，可以不必在根模块一次性加载所有配置。`@nestjs/config` 包提供了**局部注册（partial registration）**功能，只引用与当前特性模块相关的配置文件。你可以在特性模块中通过 `forFeature()` 静态方法实现局部注册，示例如下：

```typescript
import databaseConfig from './config/database.config'

@Module({
  imports: [ConfigModule.forFeature(databaseConfig)],
})
export class DatabaseModule {}
```

> info **警告** 某些情况下，你可能需要通过 `onModuleInit()` 钩子访问通过局部注册加载的配置属性，而不是在构造函数中访问。原因在于 `forFeature()` 方法会在模块初始化阶段运行，而模块初始化顺序是不确定的。如果你在构造函数中访问其他模块通过局部注册加载的配置，可能会遇到依赖的模块尚未初始化的问题。`onModuleInit()` 方法会在所有依赖模块初始化完成后才执行，因此这种方式更安全。

#### 配置校验（Schema validation）

在应用程序启动时，如果缺少必要的环境变量，或者这些变量不符合特定的校验规则，通常应当抛出异常。`@nestjs/config` 包支持两种校验方式：

- 内置 [Joi](https://github.com/sideway/joi) 校验器。你可以使用 Joi 定义对象的验证模式（Validation Schema），并对 JavaScript 对象进行校验。
- 自定义 `validate()` 函数，接收环境变量作为输入参数。

要使用 Joi，首先需要安装 Joi 包：

```bash
$ npm install --save joi
```

安装完成后，可以像下面这样定义 Joi 校验模式，并通过 `forRoot()` 方法的 `validationSchema` 选项传递：

```typescript
@@filename(app.module)
import * as Joi from 'joi';

@Module({
  imports: [
    ConfigModule.forRoot({
      validationSchema: Joi.object({
        NODE_ENV: Joi.string()
          .valid('development', 'production', 'test', 'provision')
          .default('development'),
        PORT: Joi.number().port().default(3000),
      }),
    }),
  ],
})
export class AppModule {}
```

默认情况下，所有校验模式中的键都是可选的。上例中，我们为 `NODE_ENV` 和 `PORT` 设置了默认值，如果环境（`.env` 文件或进程环境变量）中未提供这些变量，则会使用默认值。你也可以使用 `required()` 校验方法，强制要求环境中必须定义某个变量。如果未定义，校验阶段会抛出异常。更多 Joi 校验方法可参考 [Joi 官方文档](https://joi.dev/api/?v=17.3.0#example)。

默认情况下，校验模式中未声明的环境变量（即未知环境变量）是允许的，不会触发校验异常。同时，所有校验错误都会被报告。你可以通过 `forRoot()` 选项对象的 `validationOptions` 属性传递一个选项对象，来修改这些行为。该对象可以包含 [Joi 校验选项](https://joi.dev/api/?v=17.3.0#anyvalidatevalue-options)支持的任何标准属性。例如，若要反转上述两个默认设置，可以这样写：

```typescript
@@filename(app.module)
import * as Joi from 'joi';

@Module({
  imports: [
    ConfigModule.forRoot({
      validationSchema: Joi.object({
        NODE_ENV: Joi.string()
          .valid('development', 'production', 'test', 'provision')
          .default('development'),
        PORT: Joi.number().port().default(3000),
      }),
      validationOptions: {
        allowUnknown: false,
        abortEarly: true,
      },
    }),
  ],
})
export class AppModule {}
```

`@nestjs/config` 包的默认设置如下：

- `allowUnknown`：控制是否允许环境变量中出现未知键。默认值为 `true`。
- `abortEarly`：为 `true` 时，遇到第一个校验错误即停止校验；为 `false` 时，返回所有错误。默认值为 `false`。

注意：一旦你传递了 `validationOptions` 对象，未显式指定的设置将采用 Joi 的标准默认值（而不是 `@nestjs/config` 的默认值）。例如，如果你在自定义 `validationOptions` 对象中未指定 `allowUnknown`，则其值会变为 Joi 的默认值 `false`。因此，建议在自定义对象中**同时指定**这两个设置。

> info **提示** 如果希望禁用对预定义环境变量的校验，可以在 `forRoot()` 方法的选项对象中设置 `validatePredefined: false`。预定义环境变量指的是在导入模块前就已设置的进程变量（`process.env` 变量）。例如，使用 `PORT=3000 node main.js` 启动应用时，`PORT` 就是预定义环境变量。

#### 自定义校验函数

你还可以指定一个**同步**的 `validate` 函数。该函数接收一个包含所有环境变量（来自 env 文件和进程环境变量）的对象，并返回一个经过校验和（如有需要）转换后的环境变量对象。如果该函数抛出错误，应用将会在启动阶段被中断，无法正常启动。

下面的示例使用了 `class-transformer` 和 `class-validator` 包。首先，需要定义：

- 一个带有校验约束的类
- 一个利用 `plainToInstance` 和 `validateSync` 方法的校验函数

```typescript
@@filename(env.validation)
import { plainToInstance } from 'class-transformer';
import { IsEnum, IsNumber, Max, Min, validateSync } from 'class-validator';

enum Environment {
  Development = "development",
  Production = "production",
  Test = "test",
  Provision = "provision",
}

class EnvironmentVariables {
  @IsEnum(Environment)
  NODE_ENV: Environment;

  @IsNumber()
  @Min(0)
  @Max(65535)
  PORT: number;
}

export function validate(config: Record<string, unknown>) {
  const validatedConfig = plainToInstance(
    EnvironmentVariables,
    config,
    { enableImplicitConversion: true },
  );
  const errors = validateSync(validatedConfig, { skipMissingProperties: false });

  if (errors.length > 0) {
    throw new Error(errors.toString());
  }
  return validatedConfig;
}
```

定义好校验函数后，可以如下方式将其作为 `ConfigModule` 的配置选项：

```typescript
@@filename(app.module)
import { validate } from './env.validation';

@Module({
  imports: [
    ConfigModule.forRoot({
      validate,
    }),
  ],
})
export class AppModule {}
```

#### 自定义 getter 函数

`ConfigService` 提供了通用的 `get()` 方法，可以通过键名获取对应的配置信息。除此之外，我们还可以为其添加自定义的 `getter` 属性，使代码风格更加简洁、自然。

```typescript
@@filename()
@Injectable()
export class ApiConfigService {
  constructor(private configService: ConfigService) {}

  get isAuthEnabled(): boolean {
    return this.configService.get('AUTH_ENABLED') === 'true';
  }
}
@@switch
@Dependencies(ConfigService)
@Injectable()
export class ApiConfigService {
  constructor(configService) {
    this.configService = configService;
  }

  get isAuthEnabled() {
    return this.configService.get('AUTH_ENABLED') === 'true';
  }
}
```

现在可以像下面这样使用 getter 函数：

```typescript
@@filename(app.service)
@Injectable()
export class AppService {
  constructor(apiConfigService: ApiConfigService) {
    if (apiConfigService.isAuthEnabled) {
      // 已启用身份验证
    }
  }
}
@@switch
@Dependencies(ApiConfigService)
@Injectable()
export class AppService {
  constructor(apiConfigService) {
    if (apiConfigService.isAuthEnabled) {
      // 已启用身份验证
    }
  }
}
```

#### 环境变量加载钩子（Environment variables loaded hook）

如果某个模块的配置依赖于环境变量，并且这些变量是从 `.env` 文件加载的，你可以使用 `ConfigModule.envVariablesLoaded` 钩子，确保在操作 `process.env` 对象前，`.env` 文件已被加载。例如：

```typescript
export async function getStorageModule() {
  await ConfigModule.envVariablesLoaded
  return process.env.STORAGE === 'S3' ? S3StorageModule : DefaultStorageModule
}
```

这种写法可以保证在 `ConfigModule.envVariablesLoaded` 之后再访问环境变量。
