### 任务调度（Task scheduling）

任务调度允许你按照固定的日期/时间、周期性间隔，或在指定间隔后执行任意代码（方法/函数）。在 Linux 世界中，这通常由操作系统层面的 [cron](https://en.wikipedia.org/wiki/Cron) 等软件包处理。对于 Node.js 应用，有多个软件包可以模拟类似 cron 的功能。Nest 提供了 `@nestjs/schedule` 包，该包集成了流行的 Node.js [cron](https://github.com/kelektiv/node-cron) 包。本章将介绍如何使用该包。

#### 安装

要开始使用任务调度功能，首先需要安装相关依赖。

```bash
$ npm install --save @nestjs/schedule
```

要启用任务调度功能，需要在根模块（AppModule）中导入 `ScheduleModule`，并按照如下方式调用其静态方法 `forRoot()`：

```typescript
@@filename(app.module)
import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';

@Module({
  imports: [
    ScheduleModule.forRoot()
  ],
})
export class AppModule {}
```

调用 `.forRoot()` 方法会初始化调度器，并注册应用中所有声明式的 [定时任务（cron jobs）](techniques/task-scheduling#declarative-cron-jobs)、[超时任务（timeouts）](techniques/task-scheduling#declarative-timeouts) 和 [间隔任务（intervals）](techniques/task-scheduling#declarative-intervals)。这些任务会在 `onApplicationBootstrap` 生命周期钩子（lifecycle hook）触发时注册，确保所有模块都已加载并声明了各自的调度任务。

#### 声明式定时任务（cron jobs）

定时任务（cron job）用于自动调度执行任意函数（方法调用）。定时任务可以：

- 在指定的日期/时间执行一次。
- 按照设定的周期重复执行；周期性任务可以在指定的时间点、以指定的间隔运行（例如，每小时一次、每周一次、每 5 分钟一次）。

你可以通过在方法定义前添加 `@Cron()` 装饰器（Decorator），来声明一个定时任务。被装饰的方法中包含需要执行的代码，示例如下：

```typescript
import { Injectable, Logger } from '@nestjs/common'
import { Cron } from '@nestjs/schedule'

@Injectable()
export class TasksService {
  private readonly logger = new Logger(TasksService.name)

  @Cron('45 * * * * *')
  handleCron() {
    this.logger.debug('当当前秒数为 45 时调用')
  }
}
```

在上述示例中，`handleCron()` 方法会在每分钟的第 45 秒被调用一次。换句话说，该方法会在每分钟的 45 秒时自动运行。

`@Cron()` 装饰器支持以下标准 [cron 表达式](http://crontab.org/)：

- 星号（例如：`*`）
- 范围（例如：`1-3,5`）
- 步进（例如：`*/2`）

在上面的示例中，我们将 `45 * * * * *` 作为参数传递给装饰器。下面的说明展示了 cron 表达式字符串中每个位置的含义：

<pre class="language-javascript">
  <code class="language-javascript">
    * * * * * * | | | | | | | | | | | 星期几 | | | | 月份 | | | 每月的第几天 | | 小时 | 分钟
    秒（可选）
  </code>
</pre>

以下是一些常见的 cron 表达式示例：

<table>
  <tbody>
    <tr>
      <td>
        <code>* * * * * *</code>
      </td>
      <td>每秒执行一次</td>
    </tr>
    <tr>
      <td>
        <code>45 * * * * *</code>
      </td>
      <td>每分钟的第 45 秒执行</td>
    </tr>
    <tr>
      <td>
        <code>0 10 * * * *</code>
      </td>
      <td>每小时的第 10 分钟开始时执行</td>
    </tr>
    <tr>
      <td>
        <code>0 */30 9-17 * * *</code>
      </td>
      <td>每天 9:00 至 17:00 之间，每隔 30 分钟执行一次</td>
    </tr>
    <tr>
      <td>
        <code>0 30 11 * * 1-5</code>
      </td>
      <td>每周一至周五的 11:30 执行</td>
    </tr>
  </tbody>
</table>

`@nestjs/schedule` 包提供了一个包含常用 cron 表达式（Cron
Expression）的枚举，方便开发者直接使用。你可以按如下方式使用该枚举：

```typescript
import { Injectable, Logger } from '@nestjs/common'
import { Cron, CronExpression } from '@nestjs/schedule'

@Injectable()
export class TasksService {
  private readonly logger = new Logger(TasksService.name)

  @Cron(CronExpression.EVERY_30_SECONDS)
  handleCron() {
    this.logger.debug('每 30 秒调用一次')
  }
}
```

在上述示例中，`handleCron()` 方法会每隔 30 秒被调用一次。如果发生异常，异常信息会被记录到控制台，因为所有使用 `@Cron()` 装饰器的方法都会被自动包裹在 `try-catch` 语句块中。

另外，你也可以为 `@Cron()` 装饰器传入一个 JavaScript 的 `Date` 对象。这样，任务会在指定的日期和时间仅执行一次。

> info **提示** 你可以利用 JavaScript 的日期运算来实现相对当前时间的定时任务。例如，`@Cron(new Date(Date.now() + 10 * 1000))` 可以让任务在应用启动后 10 秒执行一次。

你还可以为 `@Cron()` 装饰器提供第二个参数，用于传递额外的选项。

<table>
  <tbody>
    <tr>
      <td>
        <code>name</code>
      </td>
      <td>用于在声明后访问和控制定时任务（cron job）。</td>
    </tr>
    <tr>
      <td>
        <code>timeZone</code>
      </td>
      <td>
        指定任务执行时所用的时区（timezone）。这会根据你的时区调整实际执行时间。如果时区无效，将会抛出错误。你可以在{' '}
        <a href="http://momentjs.com/timezone/">Moment Timezone</a> 网站上查看所有可用时区。
      </td>
    </tr>
    <tr>
      <td>
        <code>utcOffset</code>
      </td>
      <td>
        允许你通过设置偏移量（offset）来指定时区，而不是直接使用 <code>timeZone</code> 参数。
      </td>
    </tr>
    <tr>
      <td>
        <code>waitForCompletion</code>
      </td>
      <td>
        如果设置为 <code>true</code>，则在当前 onTick
        回调函数执行完成前，不会启动新的定时任务实例。当前任务运行期间发生的所有新调度将会被完全跳过。
      </td>
    </tr>
    <tr>
      <td>
        <code>disabled</code>
      </td>
      <td>用于指示该任务是否会被执行。</td>
    </tr>
  </tbody>
</table>

```typescript
import { Injectable } from '@nestjs/common'
import { Cron, CronExpression } from '@nestjs/schedule'

@Injectable()
export class NotificationService {
  @Cron('* * 0 * * *', {
    name: 'notifications',
    timeZone: 'Europe/Paris',
  })
  triggerNotifications() {}
}
```

你可以在声明后访问和控制定时任务（cron job），也可以通过 <a href="/techniques/task-scheduling#dynamic-schedule-module-api">动态 API</a> 动态创建定时任务（其 cron 表达式在运行时定义）。要通过 API 访问声明式定时任务，必须通过装饰器（Decorator）第二个参数 options 对象中的 `name` 属性为任务指定名称。

#### 声明式定时任务

要声明某个方法以指定的（周期性）间隔运行，只需在方法定义前添加 `@Interval()` 装饰器（Decorator）。如下面所示，将间隔时间（以毫秒为单位的数字）作为参数传递给装饰器：

```typescript
@Interval(10000)
handleInterval() {
  this.logger.debug('每 10 秒调用一次');
}
```

> info **提示** 该机制底层使用了 JavaScript 的 `setInterval()` 函数。你也可以使用 cron 任务（cron job）来调度周期性任务。

如果你希望通过 <a href="/techniques/task-scheduling#dynamic-schedule-module-api">动态 API（Dynamic API）</a> 在声明类外部控制声明式定时任务，可以通过如下方式为定时任务指定名称：

```typescript
@Interval('notifications', 2500)
handleInterval() {}
```

如果发生异常，异常信息会被记录到控制台，因为所有使用 `@Interval()` 装饰器的方法都会被自动包裹在 `try-catch` 代码块中。

<a href="techniques/task-scheduling#dynamic-intervals">动态 API</a>
还支持**创建**动态定时任务（dynamic
intervals），即在运行时定义定时任务的属性，并且可以**列出和删除**这些任务。

<app-banner-enterprise></app-banner-enterprise>

#### 声明式超时任务

要声明某个方法在指定超时时间后（仅一次）运行，只需在方法定义前添加 `@Timeout()` 装饰器。如下所示，将相对于应用启动时间的延迟（以毫秒为单位）作为参数传递给装饰器：

```typescript
@Timeout(5000)
handleTimeout() {
  this.logger.debug('在 5 秒后调用一次');
}
```

> info **提示** 该机制底层使用了 JavaScript 的 `setTimeout()` 函数。

如果发生异常，异常信息会被记录到控制台，因为所有使用 `@Timeout()` 装饰器的方法都会被自动包裹在 `try-catch` 代码块中。

如果你希望通过 <a href="/techniques/task-scheduling#dynamic-schedule-module-api">动态 API</a> 在声明类外部控制声明式超时任务，可以通过如下方式为超时任务指定名称：

```typescript
@Timeout('notifications', 2500)
handleTimeout() {}
```

<a href="techniques/task-scheduling#dynamic-timeouts">动态 API</a>
还支持**创建**动态超时任务（dynamic
timeouts），即在运行时定义超时任务的属性，并且可以**列出和删除**这些任务。

#### 动态调度模块 API

`@nestjs/schedule` 模块提供了一个动态 API，支持管理声明式的 <a href="techniques/task-scheduling#declarative-cron-jobs">定时任务（cron jobs）</a>、<a href="techniques/task-scheduling#declarative-timeouts">超时（timeouts）</a> 和 <a href="techniques/task-scheduling#declarative-intervals">间隔任务（intervals）</a>。该 API 还支持在运行时动态创建和管理 **动态** 定时任务、超时和间隔任务。

#### 动态定时任务（cron jobs）

你可以通过 `SchedulerRegistry` API，在代码的任意位置根据名称获取 `CronJob` 实例的引用。首先，使用标准的构造函数依赖注入（Dependency Injection）方式注入 `SchedulerRegistry`：

```typescript
constructor(private schedulerRegistry: SchedulerRegistry) {}
```

> info **提示** 请从 `@nestjs/schedule` 包中导入 `SchedulerRegistry`。

然后在类中这样使用。假设你已经通过如下方式声明了一个定时任务：

```typescript
@Cron('* * 8 * * *', {
  name: 'notifications',
})
triggerNotifications() {}
```

可以通过如下方式访问该任务：

```typescript
const job = this.schedulerRegistry.getCronJob('notifications')

job.stop()
console.log(job.lastDate())
```

`getCronJob()` 方法会返回指定名称的定时任务。返回的 `CronJob` 对象包含以下方法：

- `stop()` - 停止已计划运行的任务。
- `start()` - 重新启动已被停止的任务。
- `setTime(time: CronTime)` - 停止任务，设置新的时间，然后重新启动任务。
- `lastDate()` - 返回该任务上一次执行的日期（`DateTime` 类型）。
- `nextDate()` - 返回该任务下一次计划执行的日期（`DateTime` 类型）。
- `nextDates(count: number)` - 返回一个数组（长度为 `count`），包含该任务接下来将被触发的日期（`DateTime` 类型）。`count` 默认为 0，此时返回空数组。

> info **提示** 可以对 `DateTime` 对象调用 `toJSDate()` 方法，将其转换为 JavaScript 的 `Date` 类型。

**动态创建** 新的定时任务，可以使用 `SchedulerRegistry#addCronJob` 方法，如下所示：

```typescript
addCronJob(name: string, seconds: string) {
  const job = new CronJob(`${seconds} * * * * *`, () => {
    this.logger.warn(`time (${seconds}) for job ${name} to run!`);
  });

  this.schedulerRegistry.addCronJob(name, job);
  job.start();

  this.logger.warn(
    `job ${name} added for each minute at ${seconds} seconds!`,
  );
}
```

在上述代码中，我们使用 `cron` 包中的 `CronJob` 对象来创建定时任务。`CronJob` 构造函数的第一个参数是 cron 表达式（与 `@Cron()` <a href="techniques/task-scheduling#declarative-cron-jobs">装饰器（Decorator）</a>用法一致），第二个参数是定时器触发时要执行的回调函数。`SchedulerRegistry#addCronJob` 方法接收两个参数：任务名称和 `CronJob` 实例。

> warning **警告** 在访问 `SchedulerRegistry` 之前，务必先注入它。`CronJob` 需从 `cron` 包中导入。

**删除** 指定名称的定时任务，可以使用 `SchedulerRegistry#deleteCronJob` 方法，如下所示：

```typescript
deleteCron(name: string) {
  this.schedulerRegistry.deleteCronJob(name);
  this.logger.warn(`job ${name} deleted!`);
}
```

**列出** 所有定时任务，可以使用 `SchedulerRegistry#getCronJobs` 方法，如下所示：

```typescript
getCrons() {
  const jobs = this.schedulerRegistry.getCronJobs();
  jobs.forEach((value, key, map) => {
    let next;
    try {
      next = value.nextDate().toJSDate();
    } catch (e) {
      next = 'error: next fire date is in the past!';
    }
    this.logger.log(`job: ${key} -> next: ${next}`);
  });
}
```

`getCronJobs()` 方法会返回一个 `map`。在上述代码中，我们遍历该 map，并尝试访问每个 `CronJob` 的 `nextDate()` 方法。在 `CronJob` 的 API 中，如果任务已经执行完毕且没有未来的触发时间，则会抛出异常。

#### 动态定时任务间隔

可以通过 `SchedulerRegistry#getInterval` 方法获取某个定时任务（interval）的引用。如前所述，使用标准的构造函数依赖注入（Dependency Injection）方式注入 `SchedulerRegistry`：

```typescript
constructor(private schedulerRegistry: SchedulerRegistry) {}
```

然后可以这样使用：

```typescript
const interval = this.schedulerRegistry.getInterval('notifications')
clearInterval(interval)
```

**动态创建**新的定时任务间隔，可以使用 `SchedulerRegistry#addInterval` 方法，示例如下：

```typescript
addInterval(name: string, milliseconds: number) {
  const callback = () => {
    this.logger.warn(`Interval ${name} executing at time (${milliseconds})!`);
  };

  const interval = setInterval(callback, milliseconds);
  this.schedulerRegistry.addInterval(name, interval);
}
```

在上述代码中，我们首先创建了一个标准的 JavaScript 定时任务（interval），然后将其传递给 `SchedulerRegistry#addInterval` 方法。
该方法接收两个参数：定时任务的名称和定时任务本身。

**删除**指定名称的定时任务，可以使用 `SchedulerRegistry#deleteInterval` 方法，示例如下：

```typescript
deleteInterval(name: string) {
  this.schedulerRegistry.deleteInterval(name);
  this.logger.warn(`Interval ${name} deleted!`);
}
```

**列出**所有定时任务，可以使用 `SchedulerRegistry#getIntervals` 方法，示例如下：

```typescript
getIntervals() {
  const intervals = this.schedulerRegistry.getIntervals();
  intervals.forEach(key => this.logger.log(`Interval: ${key}`));
}
```

#### 动态超时

可以通过 `SchedulerRegistry#getTimeout` 方法获取某个超时（timeout）的引用。如前所述，使用标准的构造函数依赖注入（Dependency Injection）方式注入 `SchedulerRegistry`：

```typescript
constructor(private readonly schedulerRegistry: SchedulerRegistry) {}
```

然后可以这样使用：

```typescript
const timeout = this.schedulerRegistry.getTimeout('notifications')
clearTimeout(timeout)
```

**动态创建**新的超时任务，可以使用 `SchedulerRegistry#addTimeout` 方法，示例如下：

```typescript
addTimeout(name: string, milliseconds: number) {
  const callback = () => {
    this.logger.warn(`Timeout ${name} executing after (${milliseconds})!`);
  };

  const timeout = setTimeout(callback, milliseconds);
  this.schedulerRegistry.addTimeout(name, timeout);
}
```

在上述代码中，我们创建了一个标准的 JavaScript 超时（timeout），然后将其传递给 `SchedulerRegistry#addTimeout` 方法。
该方法接收两个参数：超时任务的名称和超时对象本身。

**删除**指定名称的超时任务，可以使用 `SchedulerRegistry#deleteTimeout` 方法，示例如下：

```typescript
deleteTimeout(name: string) {
  this.schedulerRegistry.deleteTimeout(name);
  this.logger.warn(`Timeout ${name} deleted!`);
}
```

**列出**所有超时任务，可以使用 `SchedulerRegistry#getTimeouts` 方法，示例如下：

```typescript
getTimeouts() {
  const timeouts = this.schedulerRegistry.getTimeouts();
  timeouts.forEach(key => this.logger.log(`Timeout: ${key}`));
}
```

#### 示例

可用的完整示例请参考 [这里](https://github.com/nestjs/nest/tree/master/sample/27-scheduling)。
