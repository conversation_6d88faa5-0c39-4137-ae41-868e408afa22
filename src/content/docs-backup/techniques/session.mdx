### 会话（Session）

**HTTP 会话（HTTP sessions）** 提供了一种在多个请求之间存储用户信息的方式，这对于 [MVC](/techniques/mvc) 应用尤其有用。

#### 在 Express（默认）中使用

首先安装[所需依赖包](https://github.com/expressjs/session)（TypeScript 用户还需安装类型声明）：

```shell
$ npm i express-session
$ npm i -D @types/express-session
```

安装完成后，在全局中间件中应用 `express-session` 中间件（例如在 `main.ts` 文件中）：

```typescript
import * as session from 'express-session'
// 在初始化文件中
app.use(
  session({
    secret: 'my-secret',
    resave: false,
    saveUninitialized: false,
  })
)
```

> warning **注意** 默认的服务端会话存储并不适用于生产环境。在大多数情况下会导致内存泄漏，无法扩展到多进程，仅适合调试和开发使用。详情请参阅[官方仓库](https://github.com/expressjs/session)。

`secret` 用于对会话 ID Cookie 进行签名。它可以是单个字符串（单一密钥），也可以是字符串数组（多个密钥）。如果提供了密钥数组，只有第一个元素会用于签名会话 ID Cookie，而所有元素都会在请求时用于校验签名。密钥本身应难以被人类解析，建议使用随机字符。

启用 `resave` 选项会强制每次请求都将会话保存到会话存储，即使会话内容没有发生变化。默认值为 `true`，但该默认值已被弃用，未来版本中默认值可能会更改。

同样，启用 `saveUninitialized` 选项会强制将"未初始化"的会话保存到存储中。所谓"未初始化"是指新建但尚未被修改的会话。选择 `false` 对于实现登录会话、减少服务器存储消耗或遵守需要用户同意后才能设置 Cookie 的法律非常有用。选择 `false` 还可以避免客户端在没有会话的情况下并发发起多个请求时出现竞态条件（[参考来源](https://github.com/expressjs/session#saveuninitialized)）。

你还可以为 `session` 中间件传递更多选项，详见 [API 文档](https://github.com/expressjs/session#options)。

> info **提示** 推荐设置 `secure: true` 选项。但请注意，这需要你的网站启用 HTTPS，即必须通过 HTTPS 才能使用安全 Cookie。如果设置了 `secure: true`，但通过 HTTP 访问网站，则不会设置 Cookie。如果你的 Node.js 应用部署在代理服务器后，并且使用了 `secure: true`，还需要在 express 中设置 `"trust proxy"`。

配置完成后，你可以在路由处理器（Route Handler）中设置和读取会话值，例如：

```typescript
@Get()
findAll(@Req() request: Request) {
  request.session.visits = request.session.visits ? request.session.visits + 1 : 1;
}
```

> info **提示** `@Req()` 装饰器从 `@nestjs/common` 导入，`Request` 类型来自 `express` 包。

你也可以使用 `@Session()` 装饰器直接从请求中提取会话对象，如下所示：

```typescript
@Get()
findAll(@Session() session: Record<string, any>) {
  session.visits = session.visits ? session.visits + 1 : 1;
}
```

> info **提示** `@Session()` 装饰器从 `@nestjs/common` 包导入。

#### 在 Fastify 中使用

首先安装所需的依赖包：

```shell
$ npm i @fastify/secure-session
```

安装完成后，在初始化文件中注册 `fastify-secure-session` 插件：

```typescript
import secureSession from '@fastify/secure-session'

// 在你的初始化文件中
const app = await NestFactory.create<NestFastifyApplication>(AppModule, new FastifyAdapter())
await app.register(secureSession, {
  secret: 'averylogphrasebiggerthanthirtytwochars',
  salt: 'mq9hDxBVDbspDR6n',
})
```

> info **提示** 你也可以预先生成密钥（[查看说明](https://github.com/fastify/fastify-secure-session)），或者使用[密钥轮换](https://github.com/fastify/fastify-secure-session#using-keys-with-key-rotation)功能。

更多可用选项请参考 [官方仓库](https://github.com/fastify/fastify-secure-session)。

完成上述配置后，你就可以在路由处理器（Route Handler）中设置和读取会话（session）值，例如：

```typescript
@Get()
findAll(@Req() request: FastifyRequest) {
  const visits = request.session.get('visits');
  request.session.set('visits', visits ? visits + 1 : 1);
}
```

另外，你也可以使用 `@Session()` 装饰器从请求中提取会话对象，示例如下：

```typescript
@Get()
findAll(@Session() session: secureSession.Session) {
  const visits = session.get('visits');
  session.set('visits', visits ? visits + 1 : 1);
}
```

> info **提示** `@Session()` 装饰器（Decorator）需从 `@nestjs/common` 导入，而 `secureSession.Session` 类型则来自 `@fastify/secure-session` 包（导入语句：`import * as secureSession from '@fastify/secure-session'`）。
