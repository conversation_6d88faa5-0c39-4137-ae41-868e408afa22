### HTTP 适配器

有时，你可能希望在 Nest 应用上下文（ApplicationContext）内部或外部访问底层的 HTTP 服务器。

每个原生（特定平台的）HTTP 服务器或库（如 Express 和 Fastify）实例都会被封装在一个**适配器**中。适配器会作为全局可用的提供者（Provider）注册，可以从应用上下文中获取，也可以注入到其他提供者中。

#### 应用上下文外的获取方式

如果需要在应用上下文外部获取 `HttpAdapter` 的引用，可以调用 `getHttpAdapter()` 方法。

```typescript
@@filename()
const app = await NestFactory.create(AppModule);
const httpAdapter = app.getHttpAdapter();
```

#### 作为可注入的依赖

如果需要在应用上下文内部获取 `HttpAdapterHost` 的引用，可以像注入其他提供者一样，通过构造函数注入的方式实现。

```typescript
@@filename()
export class CatsService {
  constructor(private adapterHost: HttpAdapterHost) {}
}
@@switch
@Dependencies(HttpAdapterHost)
export class CatsService {
  constructor(adapterHost) {
    this.adapterHost = adapterHost;
  }
}
```

> info **提示** `HttpAdapterHost` 需要从 `@nestjs/core` 包中导入。

需要注意，`HttpAdapterHost` 本身**不是**实际的 `HttpAdapter`。要获取实际的 `HttpAdapter` 实例，只需访问其 `httpAdapter` 属性即可。

```typescript
const adapterHost = app.get(HttpAdapterHost)
const httpAdapter = adapterHost.httpAdapter
```

`httpAdapter` 就是底层框架所使用的 HTTP 适配器实例。它是 `ExpressAdapter` 或 `FastifyAdapter` 的实例（这两个类都继承自 `AbstractHttpAdapter`）。

适配器对象暴露了多个用于与 HTTP 服务器交互的有用方法。如果你希望直接访问底层库的实例（例如 Express 实例），可以调用 `getInstance()` 方法。

```typescript
const instance = httpAdapter.getInstance()
```

#### 监听事件（Listening event）

如果你希望在服务器开始监听请求时执行某些操作，可以订阅 `listen$` 流，如下所示：

```typescript
this.httpAdapterHost.listen$.subscribe(() => console.log('HTTP server is listening'))
```

此外，`HttpAdapterHost` 还提供了一个 `listening` 布尔属性，用于指示服务器当前是否处于监听状态：

```typescript
if (this.httpAdapterHost.listening) {
  console.log('HTTP server is listening')
}
```
