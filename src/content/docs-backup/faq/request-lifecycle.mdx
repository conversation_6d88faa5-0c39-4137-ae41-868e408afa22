### 请求生命周期（Request Lifecycle）

Nest 应用会按照特定顺序处理请求并生成响应，这一过程被称为**请求生命周期**。由于涉及中间件、管道、守卫和拦截器等机制，尤其是在全局、控制器级和路由级组件共同作用时，追踪某段代码在生命周期中的执行位置可能会变得复杂。通常，请求会依次经过中间件、守卫、拦截器、管道，最后在响应生成阶段再回到拦截器。

#### 中间件

中间件会按照特定顺序执行。首先，Nest 会运行全局绑定的中间件（如通过 `app.use` 绑定的中间件），然后运行[模块绑定中间件](/middleware)，这些中间件会根据路径进行绑定。中间件会按照绑定顺序依次执行，这与 Express（Node.js 框架）中的中间件机制类似。如果中间件分布在不同模块中，则根模块绑定的中间件会最先执行，随后按照模块在 `imports` 数组中的添加顺序依次执行。

#### 守卫

守卫的执行顺序为：先执行全局守卫，然后是控制器守卫，最后是路由守卫。与中间件类似，守卫会按照绑定顺序依次执行。例如：

```typescript
@UseGuards(Guard1, Guard2)
@Controller('cats')
export class CatsController {
  constructor(private catsService: CatsService) {}

  @UseGuards(Guard3)
  @Get()
  getCats(): Cats[] {
    return this.catsService.getCats()
  }
}
```

`Guard1` 会先于 `Guard2` 执行，且两者都会在 `Guard3` 之前执行。

> info **提示** 这里所说的全局绑定、控制器绑定或本地绑定，区别在于守卫（或其他组件）被绑定的位置。如果你使用 `app.useGlobalGuard()` 或通过模块提供该组件，则为全局绑定。若装饰器用于控制器类前，则为控制器绑定；若用于路由声明前，则为路由绑定。

#### 拦截器

拦截器的执行模式大体与守卫类似，但有一个特殊之处：拦截器返回的是 [RxJS Observable](https://github.com/ReactiveX/rxjs)，这些 Observable 会以“先进后出”的方式解析。也就是说，入站请求会按照全局、控制器、路由级顺序依次解析，而响应阶段（即从控制器方法返回后）则会按照路由、控制器、全局的顺序依次解析。此外，任何由管道、控制器或服务抛出的错误，都可以在拦截器的 `catchError` 操作符中捕获。

#### 管道

管道的执行顺序为：全局管道、控制器管道、路由管道，且在 `@UsePipes()` 装饰器参数中也是“先进先出”。但在路由参数级别，如果有多个管道，则会按照“最后一个参数到第一个参数”的顺序依次执行。这同样适用于路由级和控制器级管道。例如，假设有如下控制器：

```typescript
@UsePipes(GeneralValidationPipe)
@Controller('cats')
export class CatsController {
  constructor(private catsService: CatsService) {}

  @UsePipes(RouteSpecificPipe)
  @Patch(':id')
  updateCat(
    @Body() body: UpdateCatDTO,
    @Param() params: UpdateCatParams,
    @Query() query: UpdateCatQuery
  ) {
    return this.catsService.updateCat(body, params, query)
  }
}
```

此时，`GeneralValidationPipe` 会依次作用于 `query`、`params`、`body`，然后再进入 `RouteSpecificPipe`，顺序同样为 `query`、`params`、`body`。如果存在参数级管道，则会在控制器和路由级管道之后（同样是从最后一个参数到第一个参数）执行。

#### 异常过滤器

异常过滤器是唯一不按“全局优先”解析的组件。相反，过滤器会从最低级别开始解析，即先执行路由级过滤器，然后是控制器级，最后才是全局过滤器。需要注意的是，异常无法在过滤器之间传递；如果路由级过滤器捕获了异常，控制器级或全局过滤器将无法再捕获同一异常。若想实现类似效果，只能通过继承方式串联过滤器。

> info **提示** 只有在请求过程中出现未捕获异常时，过滤器才会被执行。已通过 `try/catch` 捕获的异常不会触发异常过滤器。一旦遇到未捕获异常，生命周期的其余部分会被跳过，直接进入过滤器处理。

#### 总结

一般来说，请求生命周期如下所示：

1. 请求到达
2. 中间件
   - 2.1 全局中间件
   - 2.2 模块中间件
3. 守卫
   - 3.1 全局守卫
   - 3.2 控制器守卫
   - 3.3 路由守卫
4. 拦截器（控制器前）
   - 4.1 全局拦截器
   - 4.2 控制器拦截器
   - 4.3 路由拦截器
5. 管道
   - 5.1 全局管道
   - 5.2 控制器管道
   - 5.3 路由管道
   - 5.4 路由参数管道
6. 控制器（方法处理器）
7. 服务（如存在）
8. 拦截器（请求后）
   - 8.1 路由拦截器
   - 8.2 控制器拦截器
   - 8.3 全局拦截器
9. 异常过滤器
   - 9.1 路由
   - 9.2 控制器
   - 9.3 全局
10. 服务器响应
