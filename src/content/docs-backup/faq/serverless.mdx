### Serverless（无服务器架构）

无服务器计算（Serverless computing）是一种云计算执行模型。在该模型下，云服务提供商会根据实际需求分配计算资源，并为客户管理服务器。当应用未被使用时，不会为其分配任何计算资源。计费方式基于应用实际消耗的资源量（[来源](https://en.wikipedia.org/wiki/Serverless_computing)）。

采用 **无服务器架构（serverless architecture）** 时，开发者只需专注于应用代码中的各个独立函数。像 AWS Lambda、Google Cloud Functions 和 Microsoft Azure Functions 这样的服务，会负责所有物理硬件、虚拟机操作系统以及 Web 服务器软件的管理工作。

> info **提示**  
> 本章节不讨论无服务器函数的优缺点，也不会深入介绍任何云服务商的具体实现。

#### 冷启动（Cold start）

冷启动（cold start）指的是代码在一段时间未被执行后首次运行。根据所使用的云服务商不同，冷启动可能涉及多个操作，例如下载代码、引导运行时（bootstrapping the runtime），最终执行你的代码。
这个过程会因多种因素（如编程语言、应用所需依赖包数量等）而带来 **显著的延迟**。

冷启动问题非常重要。虽然有些因素我们无法控制，但我们仍可以通过自身的优化，尽量缩短冷启动时间。

虽然你可以将 Nest 视为一个为复杂企业级应用设计的完整框架，但它同样 **适用于更"简单"的应用**（或脚本）。例如，借助 [独立应用](/standalone-applications) 特性，你可以在简单的 worker、定时任务（CRON jobs）、命令行工具（CLI）或无服务器函数中，充分利用 Nest 的依赖注入机制。

#### 基准测试

为了更好地理解在无服务器函数（Serverless Function）场景下，使用 Nest 或其他知名库（如 `express`）的性能开销，我们对比了 Node 运行时执行以下脚本所需的时间：

```typescript
// #1 Express
import * as express from 'express'

async function bootstrap() {
  const app = express()
  app.get('/', (req, res) => res.send('Hello world!'))
  await new Promise<void>((resolve) => app.listen(3000, resolve))
}
bootstrap()

// #2 Nest（基于 @nestjs/platform-express）
import { NestFactory } from '@nestjs/core'
import { AppModule } from './app.module'

async function bootstrap() {
  const app = await NestFactory.create(AppModule, { logger: ['error'] })
  await app.listen(process.env.PORT ?? 3000)
}
bootstrap()

// #3 Nest 作为独立应用（无 HTTP 服务器）
import { NestFactory } from '@nestjs/core'
import { AppModule } from './app.module'
import { AppService } from './app.service'

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule, {
    logger: ['error'],
  })
  console.log(app.get(AppService).getHello())
}
bootstrap()

// #4 原生 Node.js 脚本
async function bootstrap() {
  console.log('Hello world!')
}
bootstrap()
```

上述所有脚本均使用 `tsc`（TypeScript 编译器）进行编译，因此代码未经过打包（未使用 `webpack`）。

|                                         |                         |
| --------------------------------------- | ----------------------- |
| Express                                 | 0.0079 秒（7.9 毫秒）   |
| Nest（基于 `@nestjs/platform-express`） | 0.1974 秒（197.4 毫秒） |
| Nest（独立应用）                        | 0.1117 秒（111.7 毫秒） |
| 原生 Node.js 脚本                       | 0.0071 秒（7.1 毫秒）   |

> info **说明** 机器配置：MacBook Pro Mid 2014，2.5 GHz 四核 Intel Core i7，16 GB 1600 MHz DDR3，SSD。

接下来，我们将所有基准测试重复一遍，但这次使用 `webpack` 进行打包（如果你已安装 [Nest 命令行工具（Nest CLI）](/cli/overview)，可以通过 `nest build --webpack` 命令将应用打包为单一可执行 JavaScript 文件）。不过，我们不会使用 Nest CLI 默认的 `webpack` 配置，而是确保将所有依赖（`node_modules`）一并打包，配置如下：

```javascript
module.exports = (options, webpack) => {
  const lazyImports = [
    '@nestjs/microservices/microservices-module',
    '@nestjs/websockets/socket-module',
  ]

  return {
    ...options,
    externals: [],
    plugins: [
      ...options.plugins,
      new webpack.IgnorePlugin({
        checkResource(resource) {
          if (lazyImports.includes(resource)) {
            try {
              require.resolve(resource)
            } catch (err) {
              return true
            }
          }
          return false
        },
      }),
    ],
  }
}
```

> info **提示** 如需让 Nest CLI 使用此配置，请在项目根目录下新建 `webpack.config.js` 文件。

采用上述配置后，得到如下测试结果：

|                                         |                        |
| --------------------------------------- | ---------------------- |
| Express                                 | 0.0068 秒（6.8 毫秒）  |
| Nest（基于 `@nestjs/platform-express`） | 0.0815 秒（81.5 毫秒） |
| Nest（独立应用）                        | 0.0319 秒（31.9 毫秒） |
| 原生 Node.js 脚本                       | 0.0066 秒（6.6 毫秒）  |

> info **说明** 机器配置：MacBook Pro Mid 2014，2.5 GHz 四核 Intel Core i7，16 GB 1600 MHz DDR3，SSD。

> info **提示** 你还可以通过进一步的代码压缩和优化（如使用 `webpack` 插件等）来提升性能。

可以看到，编译方式（以及是否打包代码）对整体启动时间有着至关重要的影响。通过 `webpack` 打包后，独立 Nest 应用（包含一个模块、一个控制器和一个服务）的启动时间平均可降至约 32 毫秒，基于 Express 的常规 NestJS 应用约为 81.5 毫秒。

对于更复杂的 Nest 应用，例如包含 10 个资源（通过 `$ nest g resource` 原型生成器生成，即 10 个模块、10 个控制器、10 个服务、20 个数据传输对象（DTO）类、50 个 HTTP 端点以及 `AppModule`），在 MacBook Pro Mid 2014，2.5 GHz 四核 Intel Core i7，16 GB 1600 MHz DDR3，SSD 上的整体启动时间约为 0.1298 秒（129.8 毫秒）。将单体应用作为无服务器函数运行通常并不合理，因此请将本基准测试视为应用规模增长时启动时间可能增加的参考示例。

#### 运行时优化

到目前为止，我们介绍了编译时优化。这些优化与您在应用中如何定义提供者和加载 Nest 模块无关，而这些方式在应用规模变大时起着至关重要的作用。

举个例子，假设您有一个数据库连接被定义为[异步提供者](/fundamentals/async-providers)。异步提供者旨在延迟应用启动，直到一个或多个异步任务完成。
这意味着，如果您的无服务器函数（serverless function）在引导（bootstrap）时平均需要 2 秒才能连接到数据库，那么您的端点至少需要多等待两秒（因为它必须等到连接建立后才能响应），才能返回响应（当发生冷启动且应用尚未运行时）。

正如您所见，在**无服务器环境**下，提供者的结构方式与普通环境有所不同，因为引导时间非常重要。
另一个例子是，如果您只在某些场景下使用 Redis 进行缓存，那么在这种情况下，您不应该将 Redis 连接定义为异步提供者（Async Provider），因为这会拖慢引导时间，即使本次函数调用并不需要缓存。

此外，有时您可以使用 `LazyModuleLoader` 类（如[本章](/fundamentals/lazy-loading-modules)所述）来懒加载（Lazy Loading）整个模块。缓存就是一个很好的例子。
假设您的应用有一个 `CacheModule`，它内部连接到 Redis，并且导出 `CacheService` 以与 Redis 存储交互。如果并非所有函数调用都需要缓存，您可以按需、懒加载该模块。这样，对于不需要缓存的所有冷启动调用，启动速度都会更快。

```typescript
if (request.method === RequestMethod[RequestMethod.GET]) {
  const { CacheModule } = await import('./cache.module')
  const moduleRef = await this.lazyModuleLoader.load(() => CacheModule)

  const { CacheService } = await import('./cache.service')
  const cacheService = moduleRef.get(CacheService)

  return cacheService.get(ENDPOINT_KEY)
}
```

另一个典型场景是 webhook 或 worker，根据某些特定条件（例如输入参数），可能会执行不同的操作。
在这种情况下，您可以在路由处理器中指定条件，按需懒加载适合本次函数调用的模块（Module），其余模块也都采用懒加载方式。

```typescript
if (workerType === WorkerType.A) {
  const { WorkerAModule } = await import('./worker-a.module')
  const moduleRef = await this.lazyModuleLoader.load(() => WorkerAModule)
  // ...
} else if (workerType === WorkerType.B) {
  const { WorkerBModule } = await import('./worker-b.module')
  const moduleRef = await this.lazyModuleLoader.load(() => WorkerBModule)
  // ...
}
```

#### 集成示例

应用程序的入口文件（通常为 `main.ts` 文件）应如何编写，**取决于多个因素**，因此**没有一个通用模板**可以适用于所有场景。例如，针对不同云服务商（如 AWS、Azure、GCP 等）部署无服务器函数时，初始化文件的写法会有所不同。此外，取决于你是要运行一个包含多个路由/端点的典型 HTTP 应用，还是只提供单一路由（或仅执行特定代码片段），应用的代码结构也会不同（例如，采用"每个端点一个函数"模式时，你可以使用 `NestFactory.createApplicationContext`，而不是启动 HTTP 服务器、设置中间件等）。

这里仅作演示，我们将 Nest（使用 `@nestjs/platform-express`，即启动完整的 HTTP 路由功能）与 [Serverless](https://www.serverless.com/) 框架集成（本例以 AWS Lambda 为目标）。如前所述，具体代码会因所选云服务商及其他因素而异。

首先，安装所需依赖包：

```bash
$ npm i @codegenie/serverless-express aws-lambda
$ npm i -D @types/aws-lambda serverless-offline
```

> info **提示** 为了加快开发迭代速度，我们安装了 `serverless-offline` 插件，它可以模拟 AWS Lambda 和 API Gateway 的本地环境。

安装完成后，创建 `serverless.yml` 文件，用于配置 Serverless 框架：

```yaml
service: serverless-example

plugins:
  - serverless-offline

provider:
  name: aws
  runtime: nodejs14.x

functions:
  main:
    handler: dist/main.handler
    events:
      - http:
          method: ANY
          path: /
      - http:
          method: ANY
          path: '{proxy+}'
```

> info **提示** 想了解更多关于 Serverless 框架的信息，请访问 [官方文档](https://www.serverless.com/framework/docs/)。

接下来，打开 `main.ts` 文件，按照如下方式更新启动代码，加入所需的模板代码（Boilerplate）：

```typescript
import { NestFactory } from '@nestjs/core'
import serverlessExpress from '@codegenie/serverless-express'
import { Callback, Context, Handler } from 'aws-lambda'
import { AppModule } from './app.module'

let server: Handler

async function bootstrap(): Promise<Handler> {
  const app = await NestFactory.create(AppModule)
  await app.init()

  const expressApp = app.getHttpAdapter().getInstance()
  return serverlessExpress({ app: expressApp })
}

export const handler: Handler = async (event: any, context: Context, callback: Callback) => {
  server = server ?? (await bootstrap())
  return server(event, context, callback)
}
```

> info **提示** 如果你需要创建多个无服务器函数，并在它们之间共享通用模块，建议使用 [CLI 多包仓库结构（Monorepo）模式](/cli/monorepo#monorepo-mode)。

> warning **警告** 如果你使用了 `@nestjs/swagger` 包，在无服务器函数场景下还需额外配置，才能正常工作。详情请参考此 [讨论帖](https://github.com/nestjs/swagger/issues/199)。

然后，打开 `tsconfig.json` 文件，确保启用了 `esModuleInterop` 选项，以便正确加载 `@codegenie/serverless-express` 包。

```json
{
  "compilerOptions": {
    ...
    "esModuleInterop": true
  }
}
```

现在可以构建应用（使用 `nest build` 或 `tsc`），并通过 `serverless` CLI 在本地启动 Lambda 函数：

```bash
$ npm run build
$ npx serverless offline
```

应用启动后，打开浏览器访问 `http://localhost:3000/dev/[ANY_ROUTE]`（其中 `[ANY_ROUTE]` 为应用中注册的任意端点）。

在上文中我们提到，使用 `webpack` 对应用进行打包会显著影响整体启动时间。不过，为了让本例正常运行，还需在 `webpack.config.js` 文件中做一些额外配置。通常，为确保 `handler` 函数能被正确识别，需要将 `output.libraryTarget` 属性设置为 `commonjs2`。

```javascript
return {
  ...options,
  externals: [],
  output: {
    ...options.output,
    libraryTarget: 'commonjs2',
  },
  // ... 其他配置项
}
```

完成上述配置后，可以使用 `$ nest build --webpack` 编译函数代码（然后用 `$ npx serverless offline` 进行本地测试）。

此外，推荐（但**非必需**，因为会降低构建速度）安装 `terser-webpack-plugin` 包，并重写其配置，在生产环境压缩代码时保留类名。否则在应用中使用 `class-validator` 时，可能会出现异常行为。

```javascript
const TerserPlugin = require('terser-webpack-plugin')

return {
  ...options,
  externals: [],
  optimization: {
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          keep_classnames: true,
        },
      }),
    ],
  },
  output: {
    ...options.output,
    libraryTarget: 'commonjs2',
  },
  // ... 其他配置项
}
```

#### 使用独立应用功能

另外，如果你希望让你的函数保持非常轻量，并且不需要任何 HTTP 相关功能（如路由、守卫（Guard）、拦截器（Interceptor）、管道等），你可以像前文所述，直接使用 `NestFactory.createApplicationContext`，而不是运行整个 HTTP 服务器（底层为 `express`），示例如下：

```typescript
@@filename(main)
import { HttpStatus } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { Callback, Context, Handler } from 'aws-lambda';
import { AppModule } from './app.module';
import { AppService } from './app.service';

export const handler: Handler = async (
  event: any,
  context: Context,
  callback: Callback,
) => {
  const appContext = await NestFactory.createApplicationContext(AppModule);
  const appService = appContext.get(AppService);

  return {
    body: appService.getHello(),
    statusCode: HttpStatus.OK,
  };
};
```

> info **提示** 请注意，`NestFactory.createApplicationContext` 不会为控制器方法应用增强器（如守卫、拦截器等）。如需此功能，必须使用 `NestFactory.create` 方法。

你也可以将 `event` 对象传递给，比如说，`EventsService` 提供者（Provider），让其根据输入值和你的业务逻辑进行处理并返回相应结果。

```typescript
export const handler: Handler = async (event: any, context: Context, callback: Callback) => {
  const appContext = await NestFactory.createApplicationContext(AppModule)
  const eventsService = appContext.get(EventsService)
  return eventsService.process(event)
}
```
