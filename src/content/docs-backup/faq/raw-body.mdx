### 原始请求体（Raw body）

在实际开发中，最常见的原始请求体使用场景之一，就是进行 Webhook 签名校验。通常，进行 Webhook 签名验证时，需要使用未被序列化的请求体来计算 HMAC 哈希值。

> warning **警告** 只有在启用了内置全局 body parser 中间件时，才能使用此功能。也就是说，创建应用时不能传递 `bodyParser: false`。

#### 在 Express（Node.js 框架）中使用

首先，在创建 Nest Express 应用时启用该选项：

```typescript
import { NestFactory } from '@nestjs/core'
import type { NestExpressApplication } from '@nestjs/platform-express'
import { AppModule } from './app.module'

// 在 "bootstrap" 函数中
const app = await NestFactory.create<NestExpressApplication>(AppModule, {
  rawBody: true,
})
await app.listen(process.env.PORT ?? 3000)
```

要在控制器（Controller）中访问原始请求体，可以使用便捷接口 `RawBodyRequest`，该接口会在请求对象上暴露一个 `rawBody` 字段。用法如下：

```typescript
import { Controller, Post, RawBodyRequest, Req } from '@nestjs/common'
import { Request } from 'express'

@Controller('cats')
class CatsController {
  @Post()
  create(@Req() req: RawBodyRequest<Request>) {
    const raw = req.rawBody // 返回一个缓冲区（Buffer）。
  }
}
```

#### 注册其他解析器

默认情况下，仅注册了 `json` 和 `urlencoded` 解析器。如果你需要临时注册其他解析器，需要显式调用相关方法。

例如，注册 `text` 解析器，可以这样写：

```typescript
app.useBodyParser('text')
```

> warning **警告** 请确保在调用 `NestFactory.create` 时传递了正确的应用类型。对于 Express 应用，类型应为 `NestExpressApplication`，否则 `.useBodyParser` 方法将无法使用。

#### 请求体大小限制

如果你的应用需要解析超过 Express 默认 `100kb` 的请求体，可以这样设置：

```typescript
app.useBodyParser('json', { limit: '10mb' })
```

`.useBodyParser` 方法会遵循应用选项中传递的 `rawBody` 配置。

#### 在 Fastify（高性能 Node.js 框架）中使用

首先，在创建 Nest Fastify 应用时启用该选项：

```typescript
import { NestFactory } from '@nestjs/core'
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify'
import { AppModule } from './app.module'

// 在 "bootstrap" 函数中
const app = await NestFactory.create<NestFastifyApplication>(AppModule, new FastifyAdapter(), {
  rawBody: true,
})
await app.listen(process.env.PORT ?? 3000)
```

要在控制器中访问原始请求体，同样可以使用 `RawBodyRequest` 接口，在请求对象上暴露 `rawBody` 字段。用法如下：

```typescript
import { Controller, Post, RawBodyRequest, Req } from '@nestjs/common'
import { FastifyRequest } from 'fastify'

@Controller('cats')
class CatsController {
  @Post()
  create(@Req() req: RawBodyRequest<FastifyRequest>) {
    const raw = req.rawBody // 返回一个缓冲区（Buffer）。
  }
}
```

#### 注册其他解析器

默认情况下，仅注册了 `application/json` 和 `application/x-www-form-urlencoded` 解析器。如果你需要临时注册其他解析器，需要显式调用相关方法。

例如，注册 `text/plain` 解析器，可以这样写：

```typescript
app.useBodyParser('text/plain')
```

> warning **警告** 请确保在调用 `NestFactory.create` 时传递了正确的应用类型。对于 Fastify 应用，类型应为 `NestFastifyApplication`，否则 `.useBodyParser` 方法将无法使用。

#### 请求体大小限制

如果你的应用需要解析超过 Fastify 默认 1MiB 的请求体，可以这样设置：

```typescript
const bodyLimit = 10_485_760 // 10MiB
app.useBodyParser('application/json', { bodyLimit })
```

`.useBodyParser` 方法会遵循应用选项中传递的 `rawBody` 配置。
