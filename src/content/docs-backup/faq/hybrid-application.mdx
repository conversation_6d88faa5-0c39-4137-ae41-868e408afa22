### 混合应用（Hybrid application）

混合应用指的是同时监听来自两个或更多不同来源请求的应用。这通常意味着将 HTTP 服务器与微服务监听器结合，或者仅仅是多个不同的微服务监听器。默认的 `createMicroservice` 方法不支持同时创建多个服务器，因此在这种情况下，每个微服务都需要手动创建并启动。为此，可以通过 `connectMicroservice()` 方法，将 `INestApplication` 实例与一个或多个 `INestMicroservice` 实例连接起来。

```typescript
const app = await NestFactory.create(AppModule)
const microservice = app.connectMicroservice<MicroserviceOptions>({
  transport: Transport.TCP,
})

await app.startAllMicroservices()
await app.listen(3001)
```

> info **提示** `app.listen(port)` 方法会在指定地址启动一个 HTTP 服务器。如果你的应用不需要处理 HTTP 请求，则应使用 `app.init()` 方法。

如果需要连接多个微服务实例，只需为每个微服务调用一次 `connectMicroservice()`：

```typescript
const app = await NestFactory.create(AppModule)
// 微服务 #1
const microserviceTcp = app.connectMicroservice<MicroserviceOptions>({
  transport: Transport.TCP,
  options: {
    port: 3001,
  },
})
// 微服务 #2
const microserviceRedis = app.connectMicroservice<MicroserviceOptions>({
  transport: Transport.REDIS,
  options: {
    host: 'localhost',
    port: 6379,
  },
})

await app.startAllMicroservices()
await app.listen(3001)
```

如果你希望在混合应用中，将 `@MessagePattern()` 仅绑定到某一种传输策略（例如 MQTT），可以传递第二个参数 `Transport`，它是一个包含所有内置传输策略的枚举类型。

```typescript
@@filename()
@MessagePattern('time.us.*', Transport.NATS)
getDate(@Payload() data: number[], @Ctx() context: NatsContext) {
  console.log(`Subject: ${context.getSubject()}`); // 例如 "time.us.east"
  return new Date().toLocaleTimeString(...);
}
@MessagePattern({ cmd: 'time.us' }, Transport.TCP)
getTCPDate(@Payload() data: number[]) {
  return new Date().toLocaleTimeString(...);
}
@@switch
@Bind(Payload(), Ctx())
@MessagePattern('time.us.*', Transport.NATS)
getDate(data, context) {
  console.log(`Subject: ${context.getSubject()}`); // 例如 "time.us.east"
  return new Date().toLocaleTimeString(...);
}
@Bind(Payload(), Ctx())
@MessagePattern({ cmd: 'time.us' }, Transport.TCP)
getTCPDate(data, context) {
  return new Date().toLocaleTimeString(...);
}
```

> info **提示** `@Payload()`、`@Ctx()`、`Transport` 和 `NatsContext` 都是从 `@nestjs/microservices` 导入的。

#### 共享配置（Sharing configuration）

默认情况下，混合应用不会继承为主应用（基于 HTTP 的应用）配置的全局管道、拦截器、守卫和过滤器。
如果希望让微服务继承主应用的这些配置属性，可以在 `connectMicroservice()` 的第二个参数（可选配置对象）中设置 `inheritAppConfig` 属性，如下所示：

```typescript
const microservice = app.connectMicroservice<MicroserviceOptions>(
  {
    transport: Transport.TCP,
  },
  { inheritAppConfig: true }
)
```
