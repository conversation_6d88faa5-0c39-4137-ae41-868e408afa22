# NestJS 中文文档站点（非官方）

本项目旨在为中文开发者提供优质、专业、易读的 NestJS 技术文档。我们坚持“原生中文”表达，确保技术概念准确传达，并严格遵循官方术语对照表，致力于打造最适合本地开发者阅读习惯的 NestJS 中文资料库。

## 项目特色

- **非官方译本**，专为中文技术社区打造
- 遵循 [NestJS 中文文档翻译指南][translation-guide]，确保术语统一、表达地道
- 保持与官方文档结构一致，便于查阅与对照
- 支持社区协作，欢迎参与翻译与校对

## 快速开始

1. 安装依赖：

   ```bash
   npm install
   # 或
   yarn
   # 或
   pnpm install
   ```

2. 启动本地开发服务器：

   ```bash
   npm run dev
   # 或
   yarn dev
   # 或
   pnpm dev
   ```

3. 打开浏览器访问 [http://localhost:3000](http://localhost:3000) 查看文档站点效果。

## 参与贡献

- 欢迎提交 Pull Request，参与文档翻译、校对与优化。
- 翻译请严格遵循术语表和 [翻译指南][translation-guide]，确保内容准确、风格统一。
- 有任何建议或问题，欢迎在 Issue 区留言交流。

## 版权声明

本项目为社区驱动的非官方 NestJS 中文文档，仅供学习与交流使用。NestJS 及其相关标识为其官方所有。

[translation-guide]: ./.cursor/rules/nestjs_translation_guide.mdc
