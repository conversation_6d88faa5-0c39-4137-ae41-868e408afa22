---
description: 代码注释规范
globs: 
alwaysApply: false
---
# 代码注释规范

## 格式

- 避免使用数字序号作为标题，降低后续修改成本。
- 中文与英文之间需加空格，提升可读性。
- 数字与其他字符之间需加空格，保证格式清晰。
- 注释应与被注释代码保持适当间距，避免紧贴或过远。
- 单行注释使用 //，多行注释使用 /* ... */，避免嵌套注释。
- 注释内容应简洁明了，避免冗长和重复。

## 内容

- 当代码中明确标注了 TypeScript 类型时，则不需要再重复生成 JSDoc 类型注释。
- 如果是通用的工具方法，则给出使用示例。
- 注释应解释“为什么”这样实现，而不仅仅是“做了什么”。
- 对于复杂逻辑、边界条件、特殊处理等，必须详细说明原因和背景。
- 对于函数、类、模块等，需在声明前添加简要说明其用途和核心逻辑。
- 变量、常量、枚举等命名不够直观时，需补充注释说明其含义和取值范围。
- 对外暴露的 API、接口、事件等，需注明输入输出、异常情况及使用注意事项。
- 避免在注释中出现与代码实现不符的描述，保持同步更新。

## AI 生成注释的补充要求

- 优先补充缺失的关键注释，而非机械重复已有内容。
- 遇到业务相关名词或缩写，需在首次出现时进行解释。
- 对于算法、数据结构等实现，建议补充伪代码或流程描述，便于理解。
- 注释风格应统一，避免中英文混杂、术语不一致等问题。
- 遇到不确定的业务逻辑，可在注释中标明“待确认”或“TODO”，便于后续完善。
- 避免生成无意义的注释（如“这是一个函数”），应突出实际用途和注意事项。