---
description: NestJS 中文文档润色与优化指引
globs: 
alwaysApply: false
---
# NestJS 中文文档润色与优化指引

## 🧭 总体原则

- **以中文开发者为中心**：优化表达方式，使文档更符合中文技术读者的阅读习惯。
- **技术准确性优先**：在润色过程中，始终确保技术概念、术语和原文信息不被曲解或遗漏。
- **自然通顺，非生硬翻译**：目标是打造“像是用中文原生写成”的技术文档。

---

## 🎯 风格目标

- **自然流畅**：文字读起来应如中文技术博客般顺滑，不生硬、不拗口。
- **逻辑清晰**：语义组织有条理，必要时调整语序和段落结构以增强理解。
- **轻松严谨并存**：避免语言晦涩，同时保持专业性和技术权威感。
- **适度延展**：对复杂概念可适度增加解释，提升文档教学价值。

---

## ✍️ 表达优化细则

### 简洁直达

- 删除冗余修饰，合并重复表述。
- 使用现代、常见的技术用语，如“依赖注入”、“控制器”等。

### 主动语态优先

- 将英文中的被动句转为中文常用的主动句式，如：
  > “is handled by” → “由…处理”

### 拆分长句，增强可读性

- 一个句子传达多个信息点时，可拆成短句，保持节奏舒适。

### 衔接词优化

- 增加恰当的连接词（例如：此外、因此、例如、同时），使句子间过渡自然。

### 地道表达替换

- 英文比喻、修辞用语优先换成中文读者熟悉的说法。
- 可少量使用通俗比喻、成语增强表达，但避免文学化或网络化风格。

### 称谓与语气

- 文档整体语气保持**中性、专业、客观**。
- **避免使用敬语（如“您”）**，统一使用“你”或直接省略主语以保持技术文档风格。

---

## 🧪 技术内容处理

### 代码块、命令行、配置内容

- **保持原样**，仅在必要时添加简明注释帮助理解（以中文注释为主）。

### 注释风格

- 清晰、简练，突出重点。
- 避免对显而易见的代码添加无意义的注释。

### API 文档类内容

- 保持与英文一致，避免创造性翻译。
- 润色仅限于语序和结构优化，**不改变信息内容本身**。

---

## 🧾 格式与排版规范

### Markdown / MDX 结构

- 不得改动原文档的层级结构与语义标签。

### 中文排版注意事项

- 中英文之间、数字与中文之间添加空格：
  - ✅ 使用 TypeScript 编写
  - ✅ 3 种模式
- **规范使用中文标点符号**：
  - 中文句子中应使用全角标点符号，如“，”、“。”、“？”、“！”、“：”、“；”等。
  - 避免在中文内容中使用半角标点，如 ","、"."、"?" 等。
  - 英文术语或代码片段中，保持使用英文半角标点。

### 链接与资源路径

- **保持原始链接路径**，仅翻译链接文字部分。

### 信息提示块

- 例如：`Note`、`Warning`、`Error` 等提示块内容需翻译为自然中文，格式不变。

### 表格内容

- 翻译内容时保持格式和对齐一致，不可破坏表格结构。

---

## 📋 润色操作流程

1. **通读原文**：确保对段落技术意图有完整理解。
2. **逐段优化表达**：每段处理后确认逻辑通顺，中文自然。
3. **术语检查**：对照术语表，确保术语使用一致。
4. **重点技术校对**：复杂句、嵌套句要格外注意含义是否准确。
5. **语言打磨**：在准确的基础上优化语言风格与阅读节奏。
6. **对照英文**：最终逐段比对，确保无误译、漏译。

---

## 🧠 特殊内容处理

| 内容类型 | 优化建议 |
|----------|----------|
| 教程类 | 说明步骤时可适度细化，确保易于上手 |
| 概念讲解 | 可添加简洁类比或实例辅助理解 |
| 最佳实践 | 重点突出“为什么这么做”，增加可操作性 |
| API 说明 | 精准至上，语言简练，避免润色过度 |

---

## 👥 读者画像

- 有一定开发经验的中文开发者。
- 对编程基础概念熟悉，但可能初次接触 NestJS 或相关生态。

---

## ⚠️ 常见误区提醒

- ❌ **省略内容以求简洁** → 应完整保留原文信息
- ❌ **创造性翻译术语** → 术语应与原文、主流社区保持一致
- ❌ **强行照搬英文句式** → 优先使用中文母语表达习惯
- ❌ **使用敬词（如“您”）** → 技术文档应保持中性、客观语气
- ✅ **复杂句可拆分** → 拆解后要确保所有信息点无遗漏
- ✅ **先技术准确，再语言润色**

---
