---
description: 
globs: 
alwaysApply: false
---
# NestJS 中文文档标题优化指引

## 适用范围

本指引用于 **NestJS 官方文档的中文标题润色与优化**，确保译文标题符合中文技术社区的表达习惯，在**技术准确性、表达流畅性和可读性**之间取得平衡。

适用于：

- Markdown 文档标题（一级/二级/三级标题）
- 侧边栏导航标题
- 目录结构条目（如目录页、小节名称等）

---

## 优化目标

1. **技术准确**：完整传达原文技术核心，不遗漏任何关键概念。
2. **自然流畅**：符合中文开发者的阅读习惯，像中文技术博客或开源文档那样易读。
3. **简洁有力**：标题应突出重点，避免冗余修饰或堆叠词组。
4. **逻辑清晰**：语序合理，表达明确，避免歧义或词不达意。
5. **风格统一**：保持一致的语言风格、术语用法和排版规范。
6. **语义增强**：在不违背原意的前提下，**鼓励根据上下文对标题进行重写**，使其更清晰易懂、贴近中文母语者的阅读习惯。

---

## 优化细则

### 一、表达方式

| 规则 | 示例 | 说明 |
|---|---|---|
| 使用主动语态 | `使用中间件`（而非“中间件的使用”） | 更符合中文表达习惯 |
| 允许意译，避免生硬直译 | `快速上手`（而非“开始使用”） | 贴近博客风格，提升亲切感 |
| 可根据上下文重写标题 | `深入了解模块机制`（原文：`Modules in Depth`） | 提高标题可读性，避免晦涩 |
| 中英文之间留空格 | `TypeScript 支持`、`CLI 工具` | 保持排版清晰、一致性强 |
| 长标题可适当拆分或精简 | `在中间件中使用依赖注入` → `中间件中的依赖注入` | 简化结构，突出主干 |

### 二、术语与风格规范

| 规范项 | 要求 |
|---|---|
| 技术术语 | 与 NestJS 中文官网、主流社区（如掘金、知乎、TypeScript 中文文档）保持一致 |
| 标点符号 | 使用中文语境下的中文标点（如句号、顿号），英文术语仍用英文标点 |
| 语言风格 | 中性、专业，避免口语化、敬语、主观评价 |
| 示例术语统一 | Controller → 控制器，Provider → 提供者，Middleware → 中间件，Interceptor → 拦截器，Guard → 守卫 |

---

## 优化流程

1. **理解原文意图**：不仅看标题，要阅读对应内容把握核心概念。
2. **判断可读性**：识别是否存在直译痕迹、语序不自然或术语使用混乱。
3. **结合上下文适当改写**：如果原文标题不利于理解，可重组结构或补充关键词。
4. **润色为自然中文表达**：句式简洁，语言顺滑，突出主题。
5. **校对术语与风格统一性**：对照术语表，确保一致。
6. **自查与审阅**：标题是否有误译、漏译？是否可以再简洁一点？是否像中文技术人写的？

---

## 标题优化示例对照

| 原英文标题 | 优化后中文标题 | 说明 |
|---|---|---|
| Getting Started | 快速上手 | 采用中文社区常用术语 |
| Custom Providers | 自定义提供者 | 保留术语，语义完整 |
| Request Lifecycle | 请求生命周期 | 可读性强，结构清晰 |
| Using Middleware | 使用中间件 | 主动语态，自然流畅 |
| Introduction to Guards | 守卫简介 | 术语准确 |
| Advanced Techniques | 进阶技巧 | 精炼表达 |
| Modules in Depth | 深入了解模块机制 | 重写标题，更具信息量 |
| Testing Applications | 应用测试实践 | 增强语义，避免空泛 |
| Working with Databases | 数据库集成指南 | 更具体、更贴近上下文 |
| Serialization and Transformation | 序列化与数据转换 | 保留关键概念，语义清晰 |

---

## 常见误区提醒

- **生硬直译**  
  示例：`Exception Filters` → “异常过滤器们” ❌  
  推荐：`异常过滤器` ✅

- **遗漏关键信息**  
  示例：`Guards and Interceptors` → “守卫” ❌  
  推荐：`守卫与拦截器` ✅

- **过度创译**  
  示例：`Controllers` → “请求处理者” ❌  
  推荐：`控制器` ✅

- **语义不清或含糊**  
  示例：`Advanced Topics` → “高级主题” ❌  
  推荐：`进阶技巧`、`高级用法解析` ✅

- **夹杂主观色彩或敬语**  
  示例：“请了解模块的结构”、“强烈建议使用 CLI” ❌  
  推荐：`模块结构解析`、`使用 CLI 工具` ✅

---

## 附加建议

- 建议团队间评审优化后的标题，提升文档一致性和质量。
- 建议定期更新术语表，适应 NestJS 演进或社区术语变化。
- 可参考其他优质文档风格，如 Vue、TypeScript、React 中文文档，借鉴其标题表达。

---

## 目标读者画像

- 有一定开发经验的中文开发者
- 注重技术文档准确性与表达自然性的文档贡献者或维护者
- NestJS 用户、前端/后端开发者、文档翻译协作者

---

**使用本提示词时，请严格遵循上述规则，优先保证技术准确性，再追求语言的自然流畅与可读性。不要拘泥于英文原文结构，鼓励在理解上下文基础上对标题进行合理改写，让每一个标题都更像是中文技术人写的，而非“翻译出来的”。**