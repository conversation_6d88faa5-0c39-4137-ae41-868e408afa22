---
description: 
globs: 
alwaysApply: false
---
# NestJS 中文文档翻译指南

## 翻译核心理念
- **读者第一**：以中文开发者阅读体验为中心，创造"原生中文"感受
- **准确传达**：在地道表达的同时确保技术概念准确无误
- **平衡艺术**：在直译与意译间找到平衡，既尊重原文又适应中文习惯

## 翻译目标与风格
- 创造如同**原生中文撰写**的技术文档，而非"翻译作品"
- 优先考虑**中文读者理解路径**，可重组句式结构和解释顺序
- 允许适度**扩充解释**，使复杂概念更易理解
- 营造**轻松而不失严谨**的阅读体验

## 翻译准确性与完整性补充规范

### 保持原文完整性
- **避免过度简化**：翻译时不应过分追求简洁而省略原文信息和细节
- **保留原文结构**：尊重原文的句子结构和段落划分，除非严重影响中文阅读体验
- **保持语气一致**：维持原文的语气和表达方式，如温和建议、强烈警告、中立说明等

### 翻译细节处理
- **修饰词与副词**：如 generally、typically、often 等表示频率或程度的词语应予以保留
- **条件与转折**：while、however、but、although 等表示条件或转折的连接词必须翻译，以保留原文逻辑
- **解释性内容**：原文中的解释、例证和补充说明（如括号内容）应完整翻译
- **承上启下词语处理**：如 and、then 等起到承上启下作用的词语，需根据上下文灵活翻译，确保句子逻辑连贯。and 可根据语境译为“并且”“同时”“而且”“以及”等，then 可译为“然后”“接着”“于是”等，避免机械直译，优先保证中文表达的自然流畅和逻辑顺畅。

### 平衡表达与准确性
- **先准确，再优化**：优先确保翻译的准确性和完整性，然后再考虑流畅性
- **复杂句处理**：对于复杂长句，可适当拆分，但需确保所有信息点都被翻译
- **专业术语处理**：在保持准确性的前提下，可使用中文技术社区约定俗成的表达方式

### 审校规范
- **对比原文**：翻译完成后，应与原文逐段对比，检查是否有遗漏或误译
- **检查关键点**：特别关注条件句、转折句和包含多个信息点的复杂句

## 技术内容处理准则
- 代码块、配置示例、命令行指令**完整保留原文**
- 技术术语处理遵循"**首现双语，后续从简**"原则：
  - 首次出现：中文（英文原文）
  - 后续出现：直接使用中文
- **一词一译**：同一概念在整个文档中保持翻译一致性
- 专有名词（框架名、设计模式名等）保持英文原样
- API 文档部分格外注重**术语精准性**，避免创造性翻译

## 术语清单使用指南
- 项目提供了官方术语清单 `nestjs-glossary.json`，**必须严格遵循**此清单进行翻译
- 首次出现术语时的标准格式示例：
  ```
  Nest 使用依赖注入（Dependency Injection）机制来管理类之间的依赖关系。
  ```
- 术语后续出现时直接使用中文翻译：
  ```
  通过依赖注入，我们可以轻松替换服务的具体实现，提升模块的可测试性。
  ```
- 对于术语清单未覆盖的新术语：
  1. 参考业内通用翻译
  2. 记录在翻译过程文档中，以便后续添加到术语清单
  3. 在文档中保持一致的翻译

## 格式与结构规范
- 严格保留原文档的 Markdown/MDX 标记和层级结构
- 遵循中文排版规范：
  - 中英文之间加空格（如：使用 TypeScript 开发）
  - 数字与中文之间加空格（如：共有 3 种模式）
  - 保留英文标点在英文短语内，中文语境使用中文标点
- 链接、图片引用保持原路径，仅翻译可见文本
- 信息提示块（提示、警告、错误）维持原格式，内容翻译为自然中文
- 表格结构不变，内容翻译保持格式对齐

## 中文表达优化策略
- **化整为零**：将复杂长句拆分为短句，增强可读性
- **去被动化**：英文被动句式转换为中文主动表达
- **适度本土化**：将英文比喻转换为中文读者熟悉的类比
- **增强连贯性**：适当添加过渡词增强段落间逻辑连贯
- **用词精准**：选择最符合上下文语境的中文表达
- **巧用成语**：适度使用四字成语增强亲和力，但避免过度文学化
- **技术精确**：确保技术概念解释不因表达优化而失准

## 标准化与一致性
- 严格遵循项目**术语对照表**，确保核心概念翻译一致
- 保持文档**语气一致**，避免在正式与随意间切换
- 注意英文缩写的处理，首次出现提供全称，保持格式一致性
- 版本号、参数值等关键信息保持与英文原文完全一致

## 翻译实践技巧
- 先通读全文，理解整体内容和上下文关系，再开始翻译
- 对难以直译的句子，可尝试"**重述而非翻译**"
- 术语翻译有疑问时，优先查阅项目术语清单，其次参考主流中文技术社区的通用叫法
- 复杂技术概念可增加一句简短解释，帮助理解
- 校对时先检查技术准确性，再检查语言流畅度

## 特定内容处理指南
- **教程类内容**：步骤解释可适当详细，确保易于操作
- **API文档**：严格保持精确性，减少创造性表达
- **概念性内容**：可增加类比或额外解释，帮助理解
- **最佳实践**：翻译时注重可操作性和清晰度

## 目标读者定位
- 针对有一定开发经验的中文技术人员，避免过度科普基础概念
- 假设读者了解基础编程概念，但可能不熟悉NestJS特定术语
